{% extends 'notifications_app/base_notifications.html' %}
{% load static %}

{% block title %}{{ notification.title }} - Notification Detail - CozyWish{% endblock %}

{% block notifications_extra_css %}
<style>
    /* Professional Notification Detail Page */

    .notification-meta-info {
        display: flex;
        gap: 2.5rem;
        flex-wrap: wrap;
        margin-bottom: 1.5rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.75);
        font-weight: 500;
        padding: 0.5rem 1rem;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 2rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .meta-item:hover {
        background: rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
    }

    .meta-item i {
        color: rgba(0, 0, 0, 0.6);
    }

    .notification-content-card {
        background: linear-gradient(135deg, white 0%, #fafafa 100%);
        border: 3px solid black;
        border-radius: 1.5rem;
        padding: 2.5rem;
        margin-bottom: 2.5rem;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        position: relative;
    }

    .content-section {
        margin-bottom: 2rem;
    }

    .content-section:last-child {
        margin-bottom: 0;
    }

    .section-title {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        font-size: 1.4rem;
        margin-bottom: 1.25rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    }

    .section-title i {
        background: white;
        border: 2px solid black;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .section-content {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.7;
        font-size: 1.15rem;
        padding: 1rem 0;
    }

    .related-object-card {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, white 100%);
        border: 2px solid rgba(0, 0, 0, 0.15);
        border-radius: 1.25rem;
        padding: 2rem;
        margin-top: 1.5rem;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

    .related-object-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
        border-color: rgba(0, 0, 0, 0.25);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .status-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    }

    .status-badge.read {
        background: linear-gradient(135deg, #f8fff9 0%, white 100%);
        color: #28a745;
        border: 2px solid #28a745;
    }

    .status-badge.unread {
        background: linear-gradient(135deg, #fff8f8 0%, white 100%);
        color: #dc3545;
        border: 2px solid #dc3545;
        animation: pulse-unread 2s infinite;
    }

    @keyframes pulse-unread {
        0%, 100% { box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2); }
        50% { box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4); }
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
        padding: 1rem 0;
        margin-top: 1rem;
    }

    .action-btn {
        background: white;
        color: black;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        font-family: var(--font-primary);
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.375rem;
        text-decoration: none;
    }

    .action-btn:hover {
        background: rgba(0, 0, 0, 0.05);
        color: black;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .action-btn.primary {
        background: black;
        color: white;
        border-color: black;
    }

    .action-btn.primary:hover {
        background: rgba(0, 0, 0, 0.9);
        color: white;
    }

    .action-btn.danger {
        border-color: #dc3545;
        color: #dc3545;
    }

    .action-btn.danger:hover {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .footer-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 2.5rem;
        border-top: 3px solid rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.01) 0%, transparent 100%);
        border-radius: 1rem;
        padding: 2rem;
    }

    /* Enhanced Responsive Design */
    @media (max-width: 768px) {
        .notification-detail-title {
            font-size: 1.8rem;
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .notification-detail-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .notification-meta-info {
            justify-content: center;
            gap: 1.5rem;
        }

        .meta-item {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .action-btn {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
        }

        .footer-navigation {
            flex-direction: column;
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .notification-content-card {
            padding: 2rem 1.5rem;
        }

        .section-title {
            font-size: 1.2rem;
        }

        .section-content {
            font-size: 1.05rem;
        }

        .related-object-card {
            padding: 1.5rem;
        }
    }

    @media (max-width: 480px) {
        .notification-detail-header {
            padding: 2rem 1.5rem;
        }

        .notification-detail-title {
            font-size: 1.6rem;
        }

        .meta-item {
            flex-direction: column;
            text-align: center;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block notifications_content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{% url 'notifications_app:notification_list' %}">
                <i class="fas fa-bell me-1"></i>Notifications
            </a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">
            <i class="fas fa-eye me-1"></i>Detail
        </li>
    </ol>
</nav>

<!-- Professional Header Section -->
<div class="mb-4">
    <div class="d-flex align-items-center gap-3 mb-3">
        <div class="notification-icon bg-light border rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
            {% if notification.notification_type == 'booking' %}
                <i class="fas fa-calendar-check text-dark"></i>
            {% elif notification.notification_type == 'payment' %}
                <i class="fas fa-credit-card text-dark"></i>
            {% elif notification.notification_type == 'review' %}
                <i class="fas fa-star text-dark"></i>
            {% elif notification.notification_type == 'announcement' %}
                <i class="fas fa-bullhorn text-dark"></i>
            {% else %}
                <i class="fas fa-bell text-dark"></i>
            {% endif %}
        </div>
        <div class="flex-grow-1">
            <h1 class="h4 mb-1 text-dark fw-semibold">{{ notification.title }}</h1>
            <div class="d-flex align-items-center gap-3 text-muted small">
                <span><i class="fas fa-clock me-1"></i>{{ notification.created_at|date:"M d, Y, g:i A" }}</span>
                <span class="badge bg-light text-dark border">{{ notification.get_notification_type_display }}</span>
                {% if notification.read_status == 'read' %}
                    <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>Read</span>
                {% else %}
                    <span class="badge bg-danger"><i class="fas fa-exclamation-circle me-1"></i>Unread</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Notification Content -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <h5 class="card-title text-dark mb-3">
            <i class="fas fa-envelope-open me-2"></i>Message
        </h5>
        <div class="card-text">
            {{ notification.message|linebreaks }}
        </div>
    </div>
</div>

{% if related_object %}
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <h5 class="card-title text-dark mb-3">
            <i class="fas fa-link me-2"></i>Related Information
        </h5>
        <div class="related-content">
            {% if notification.related_object_type == 'booking' %}
                <div class="d-flex align-items-start gap-3 mb-3">
                    <div class="notification-detail-icon" style="width: 48px; height: 48px; font-size: 1.2rem;">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h4 class="mb-2" style="font-family: var(--font-heading); color: black;">
                            Booking #{{ related_object.booking_id }}
                        </h4>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <strong>Status:</strong><br>
                                <span class="badge bg-primary">{{ related_object.get_status_display }}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Date:</strong><br>
                                {{ related_object.booking_date|date:"M d, Y" }}
                            </div>
                            <div class="col-md-4">
                                <strong>Total:</strong><br>
                                <span style="font-size: 1.1rem; font-weight: 600;">${{ related_object.total_price }}</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'booking_cart_app:booking_detail' related_object.booking_id %}"
                               class="action-btn primary">
                                <i class="fas fa-eye"></i>View Booking Details
                            </a>
                        </div>
                    </div>
                </div>

            {% elif notification.related_object_type == 'review' %}
                <div class="d-flex align-items-start gap-3 mb-3">
                    <div class="notification-detail-icon" style="width: 48px; height: 48px; font-size: 1.2rem;">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h4 class="mb-2" style="font-family: var(--font-heading); color: black;">
                            Review for {{ related_object.venue.name }}
                        </h4>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <strong>Rating:</strong><br>
                                <div class="d-flex align-items-center gap-1">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= related_object.rating %}
                                            <i class="fas fa-star text-warning"></i>
                                        {% else %}
                                            <i class="far fa-star text-muted"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="ms-1">({{ related_object.rating }}/5)</span>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <strong>Date:</strong><br>
                                {{ related_object.created_at|date:"M d, Y" }}
                            </div>
                        </div>
                        {% if related_object.comment %}
                        <div class="mt-3">
                            <strong>Comment:</strong><br>
                            <p class="mt-1">{{ related_object.comment }}</p>
                        </div>
                        {% endif %}
                        <div class="mt-3">
                            <a href="{% url 'review_app:review_detail' related_object.id %}"
                               class="action-btn primary">
                                <i class="fas fa-eye"></i>View Review Details
                            </a>
                        </div>
                    </div>
                </div>

            {% elif notification.related_object_type == 'payment' %}
                <div class="d-flex align-items-start gap-3 mb-3">
                    <div class="notification-detail-icon" style="width: 48px; height: 48px; font-size: 1.2rem;">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h4 class="mb-2" style="font-family: var(--font-heading); color: black;">
                            Payment for Booking #{{ related_object.booking.booking_id }}
                        </h4>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <strong>Amount:</strong><br>
                                <span style="font-size: 1.2rem; font-weight: 600; color: #28a745;">${{ related_object.amount }}</span>
                            </div>
                            <div class="col-md-4">
                                <strong>Date:</strong><br>
                                {{ related_object.payment_date|date:"M d, Y" }}
                            </div>
                            <div class="col-md-4">
                                <strong>Status:</strong><br>
                                <span class="badge bg-success">{{ related_object.get_status_display }}</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'payments_app:payment_detail' related_object.id %}"
                               class="action-btn primary">
                                <i class="fas fa-eye"></i>View Payment Details
                            </a>
                        </div>
                    </div>
                </div>

            {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Related information is not available for display.</p>
                </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% if notification.expires_at %}
    <div class="content-section">
        <div class="section-title">
            <i class="fas fa-clock"></i>
            Expiration
        </div>
        <div class="section-content">
            <div class="d-flex align-items-center gap-2">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                <span>This notification expires on {{ notification.expires_at|date:"M d, Y, g:i A" }}</span>
            </div>
        </div>
    </div>
    {% endif %}

    {% if notification.read_status == 'read' and notification.read_at %}
    <div class="content-section">
        <div class="section-title">
            <i class="fas fa-check-circle"></i>
            Read Status
        </div>
        <div class="section-content">
            <div class="d-flex align-items-center gap-2">
                <span class="status-badge read">
                    <i class="fas fa-check-circle"></i>
                    Read on {{ notification.read_at|date:"M d, Y, g:i A" }}
                </span>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    {% if notification.read_status == 'read' %}
    <form method="post" action="{% url 'notifications_app:mark_notification_unread' notification.id %}" class="d-inline">
        {% csrf_token %}
        <button type="submit" class="action-btn">
            <i class="fas fa-envelope"></i>
            Mark as Unread
        </button>
    </form>
    {% else %}
    <form method="post" action="{% url 'notifications_app:mark_notification_read' notification.id %}" class="d-inline">
        {% csrf_token %}
        <button type="submit" class="action-btn primary">
            <i class="fas fa-envelope-open"></i>
            Mark as Read
        </button>
    </form>
    {% endif %}

    {% if notification.action_url %}
    <a href="{{ notification.action_url }}" class="action-btn primary">
        <i class="fas fa-external-link-alt"></i>
        Take Action
    </a>
    {% endif %}

    <form method="post" action="{% url 'notifications_app:delete_notification' notification.id %}"
          class="d-inline" onsubmit="return confirm('Are you sure you want to delete this notification? This action cannot be undone.')">
        {% csrf_token %}
        <button type="submit" class="action-btn danger">
            <i class="fas fa-trash"></i>
            Delete Notification
        </button>
    </form>
</div>

<!-- Footer Navigation -->
<div class="footer-navigation">
    <a href="{% url 'notifications_app:notification_list' %}" class="action-btn">
        <i class="fas fa-arrow-left"></i>
        Back to Notifications
    </a>
    <a href="{% url 'notifications_app:notification_preferences' %}" class="action-btn">
        <i class="fas fa-cog"></i>
        Notification Settings
    </a>
</div>
{% endblock %}


