{% extends 'base.html' %}

{% block title %}Notification Management Dashboard{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_app:admin_dashboard' %}">Admin Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Notification Dashboard</li>
                </ol>
            </nav>
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h2 mb-0">Notification Management</h1>
                <div class="d-flex gap-2">
                    <a href="{% url 'notifications_app:admin_create_announcement' %}" class="btn btn-primary">
                        <i class="fas fa-bullhorn me-2"></i>Create Announcement
                    </a>
                    <a href="{% url 'dashboard_app:admin_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Admin Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-bell fa-2x text-primary"></i>
                    </div>
                    <h5 class="card-title">{{ total_notifications }}</h5>
                    <p class="card-text text-muted">Total Notifications</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-envelope fa-2x text-warning"></i>
                    </div>
                    <h5 class="card-title">{{ unread_notifications }}</h5>
                    <p class="card-text text-muted">Unread Notifications</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-bullhorn fa-2x text-info"></i>
                    </div>
                    <h5 class="card-title">{{ pending_announcements }}</h5>
                    <p class="card-text text-muted">Pending Announcements</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-chart-bar fa-2x text-success"></i>
                    </div>
                    <h5 class="card-title">{{ notification_types|length }}</h5>
                    <p class="card-text text-muted">Notification Types</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Notifications -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Recent Notifications</h5>
                        <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-sm btn-outline-primary">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_notifications %}
                        <div class="list-group list-group-flush">
                            {% for notification in recent_notifications %}
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <span class="badge bg-secondary me-2">{{ notification.get_notification_type_display }}</span>
                                                {{ notification.title }}
                                            </h6>
                                            <p class="mb-1 text-muted">{{ notification.message|truncatechars:100 }}</p>
                                            <small class="text-muted">
                                                To: {{ notification.user.email }} • 
                                                {{ notification.created_at|date:"M d, Y, g:i a" }}
                                            </small>
                                        </div>
                                        <div class="ms-3">
                                            <a href="{% url 'notifications_app:admin_notification_detail' notification.id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent notifications</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Notification Types & Recent Announcements -->
        <div class="col-lg-4">
            <!-- Notification Types Distribution -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Notification Types</h5>
                </div>
                <div class="card-body">
                    {% if notification_types %}
                        {% for type_name, count in notification_types.items %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ type_name }}</span>
                                <span class="badge bg-primary">{{ count }}</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted text-center">No notification data available</p>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Announcements -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bullhorn me-2"></i>Recent Announcements</h5>
                </div>
                <div class="card-body">
                    {% if recent_announcements %}
                        <div class="list-group list-group-flush">
                            {% for announcement in recent_announcements %}
                                <div class="list-group-item px-0">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><a href="{{ announcement.get_absolute_url }}">{{ announcement.title }}</a></h6>
                                        <small class="text-muted">{{ announcement.created_at|date:"M d" }}</small>
                                    </div>
                                    <p class="mb-1 small text-muted">{{ announcement.announcement_text|truncatechars:80 }}</p>
                                    <small class="text-muted">
                                        <span class="badge bg-{{ announcement.status|yesno:'success,warning' }}">
                                            {{ announcement.get_status_display }}
                                        </span>
                                        • {{ announcement.get_target_audience_display }}
                                        {% if announcement.total_recipients %}
                                            • {{ announcement.total_recipients }} recipients
                                        {% endif %}
                                    </small>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-bullhorn fa-2x text-muted mb-2"></i>
                            <p class="text-muted small">No recent announcements</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'notifications_app:admin_create_announcement' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-bullhorn me-2"></i>Create Announcement
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-list me-2"></i>View All Notifications
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'notifications_app:admin_notification_list' %}?read_status=unread" class="btn btn-outline-warning w-100">
                                <i class="fas fa-envelope me-2"></i>View Unread
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'dashboard_app:admin_dashboard' %}" class="btn btn-outline-info w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide success messages after 3 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
{% endblock %}
