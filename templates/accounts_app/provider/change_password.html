{% extends 'base.html' %}

{% block title %}Change Password - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Provider Change Password */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Change Password Section */
    .change-password-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .change-password-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .change-password-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .change-password-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .change-password-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="security-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M10,2 L18,10 L10,18 L2,10 Z" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23security-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .change-password-header .content {
        position: relative;
        z-index: 2;
    }

    .change-password-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .change-password-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .change-password-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Form Styling */
    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
    }

    .form-label i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    /* Input Group Styling */
    .input-group-cw {
        position: relative;
        display: flex;
        align-items: stretch;
        width: 100%;
    }

    .input-group-cw .form-control-cw {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: none;
    }

    .toggle-password-cw {
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
        color: var(--cw-neutral-600);
        padding: 0.875rem 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 50px;
    }

    .toggle-password-cw:hover {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
    }

    .input-group-cw .form-control-cw:focus + .toggle-password-cw {
        border-color: var(--cw-brand-primary);
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        font-size: 1.125rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-ghost {
        background: transparent;
        border: none;
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.875rem 1.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-ghost:hover {
        background: var(--cw-accent-light);
        color: var(--cw-brand-primary);
        text-decoration: none;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-success {
        background: #f0fff4;
        border-color: #bbf7d0;
        color: #166534;
    }

    .alert-cw-error {
        background: #fef2f2;
        border-color: #fecaca;
        color: #991b1b;
    }

    /* Error Messages */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #dc2626;
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .invalid-feedback i {
        margin-right: 0.25rem;
    }

    /* Form Text */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
    }

    /* Security Tips Card */
    .security-tips-card {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 2rem;
        margin-top: 2rem;
    }

    .security-tips-title {
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        font-weight: 600;
        font-size: 1.125rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .security-tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .security-tips-list li {
        color: var(--cw-neutral-700);
        margin-bottom: 0.75rem;
        padding-left: 1.5rem;
        position: relative;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .security-tips-list li::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--cw-brand-primary);
        font-weight: bold;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .change-password-section {
            padding: 3rem 0;
        }

        .change-password-container {
            max-width: 600px;
            padding: 0 1.5rem;
        }

        .change-password-header {
            padding: 2rem 2rem 1.5rem;
        }

        .change-password-title {
            font-size: 2rem;
        }

        .change-password-subtitle {
            font-size: 1rem;
        }

        .change-password-body {
            padding: 2rem;
        }

        .security-tips-card {
            padding: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        .change-password-container {
            padding: 0 1rem;
        }

        .change-password-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .change-password-body {
            padding: 1.5rem;
        }

        .change-password-title {
            font-size: 1.75rem;
        }

        .change-password-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .security-tips-card {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="change-password-section">
    <div class="change-password-container">
        <div class="change-password-card">
            <div class="change-password-header">
                <div class="content">
                    <div class="change-password-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h1 class="change-password-title">Change Password</h1>
                    <p class="change-password-subtitle">Update your business account password for enhanced security</p>
                </div>
            </div>

            <div class="change-password-body">
                <!-- Display messages -->
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert-cw alert-cw-{{ message.tags }} mb-4" role="alert">
                      <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                      {{ message }}
                    </div>
                  {% endfor %}
                {% endif %}

                <form method="post" novalidate>
                  {% csrf_token %}

                  {% if form.non_field_errors %}
                  <div class="alert-cw alert-cw-error mb-4">
                    {% for error in form.non_field_errors %}
                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}

                  <!-- Current Password -->
                  <div class="mb-4">
                    <label for="{{ form.old_password.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.old_password.label }}
                    </label>
                    <div class="input-group-cw">
                      {% if form.old_password.errors %}
                        {{ form.old_password|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your current password" }}
                      {% else %}
                        {{ form.old_password|add_class:"form-control-cw"|attr:"placeholder:Enter your current password" }}
                      {% endif %}
                      <button type="button" class="toggle-password-cw" data-target="#{{ form.old_password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.old_password.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.old_password.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <!-- New Password -->
                  <div class="mb-4">
                    <label for="{{ form.new_password1.id_for_label }}" class="form-label">
                      <i class="fas fa-key"></i>{{ form.new_password1.label }}
                    </label>
                    <div class="input-group-cw">
                      {% if form.new_password1.errors %}
                        {{ form.new_password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Enter your new password" }}
                      {% else %}
                        {{ form.new_password1|add_class:"form-control-cw"|attr:"placeholder:Enter your new password" }}
                      {% endif %}
                      <button type="button" class="toggle-password-cw" data-target="#{{ form.new_password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.new_password1.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.new_password1.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.new_password1.help_text %}
                    <div class="form-text">{{ form.new_password1.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Confirm New Password -->
                  <div class="mb-4">
                    <label for="{{ form.new_password2.id_for_label }}" class="form-label">
                      <i class="fas fa-key"></i>{{ form.new_password2.label }}
                    </label>
                    <div class="input-group-cw">
                      {% if form.new_password2.errors %}
                        {{ form.new_password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your new password" }}
                      {% else %}
                        {{ form.new_password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your new password" }}
                      {% endif %}
                      <button type="button" class="toggle-password-cw" data-target="#{{ form.new_password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.new_password2.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.new_password2.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                  </div>

                  <!-- Action buttons -->
                  <div class="d-grid mb-4">
                    <button type="submit" class="btn-cw-primary">
                      <i class="fas fa-save me-2"></i>Change Password
                    </button>
                  </div>

                  <div class="d-grid mb-4">
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                      <i class="fas fa-arrow-left"></i>Back to Profile
                    </a>
                  </div>
                </form>

                <!-- Forgot Password Option -->
                <div class="text-center mt-4 pt-4" style="border-top: 1px solid var(--cw-brand-accent);">
                  <p class="mb-3" style="color: var(--cw-neutral-600);">Forgot your current password?</p>
                  <a href="{% url 'accounts_app:service_provider_password_reset' %}" class="btn-cw-ghost">
                    <i class="fas fa-envelope"></i>Reset Password via Email
                  </a>
                </div>

                <!-- Security Tips -->
                <div class="security-tips-card">
                  <h3 class="security-tips-title">
                    <i class="fas fa-shield-alt"></i>Password Security Tips
                  </h3>
                  <ul class="security-tips-list">
                    <li>Use a combination of uppercase and lowercase letters, numbers, and symbols</li>
                    <li>Make your password at least 8 characters long</li>
                    <li>Avoid using personal information like names or birthdays</li>
                    <li>Don't reuse passwords from other accounts</li>
                    <li>Consider using a password manager for better security</li>
                  </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
(function() {
    'use strict';

    // Password toggle functionality
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password-cw');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();

            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);
        }
    });

    // Initialize accessibility attributes
    function initializePasswordToggles() {
        const toggleButtons = document.querySelectorAll('.toggle-password-cw');
        toggleButtons.forEach(function(btn) {
            btn.setAttribute('type', 'button');
            btn.setAttribute('tabindex', '0');
            btn.title = btn.title || 'Show password';
            btn.setAttribute('aria-label', btn.getAttribute('aria-label') || 'Show password');
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePasswordToggles);
    } else {
        initializePasswordToggles();
    }
})();
</script>
{% endblock %}
