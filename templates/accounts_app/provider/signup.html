{% extends 'base.html' %}

{% block title %}Join <PERSON> as a Service Provider{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Provider Signup */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Professional Signup Section */
    .signup-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .signup-container {
        max-width: 1000px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .signup-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .signup-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .signup-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="signup-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23signup-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .signup-header .content {
        position: relative;
        z-index: 2;
    }

    .signup-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .signup-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .signup-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .signup-body {
        padding: 3rem;
    }

    /* Form Layout */
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2.5rem;
    }

    .form-section-title {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
    }

    .form-section-title i {
        margin-right: 0.75rem;
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-floating > label {
        color: var(--cw-neutral-600);
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .form-floating > label i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
    }

    .form-label i {
        color: var(--cw-brand-primary);
        margin-right: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        width: 100%;
        font-size: 1.125rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
    }

    .alert-cw-success {
        background: #f0fdf4;
        border-color: #bbf7d0;
        color: #166534;
    }

    .alert-cw-error {
        background: #fef2f2;
        border-color: #fecaca;
        color: #991b1b;
    }

    .alert-cw-info {
        background: #f0f9ff;
        border-color: #bae6fd;
        color: #0c4a6e;
    }

    /* Error Messages */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #dc2626;
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .invalid-feedback i {
        margin-right: 0.25rem;
    }

    /* Form Text */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
    }

    /* Password Toggle */
    .toggle-password {
        border: 2px solid var(--cw-brand-accent) !important;
        border-left: none !important;
        background: white !important;
        color: var(--cw-brand-primary) !important;
        padding: 0.875rem 1rem !important;
        border-radius: 0 0.5rem 0.5rem 0 !important;
        transition: all 0.2s ease;
    }

    .toggle-password:hover,
    .toggle-password:focus {
        background: var(--cw-accent-light) !important;
        border-color: var(--cw-brand-primary) !important;
        color: var(--cw-brand-primary) !important;
    }

    .input-group .form-control-cw:focus + .toggle-password {
        border-color: var(--cw-brand-primary) !important;
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 500;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Responsive */
    @media (max-width: 992px) {
        .form-row {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .signup-container {
            max-width: 700px;
            padding: 0 1.5rem;
        }
    }

    @media (max-width: 768px) {
        .signup-section {
            padding: 3rem 0;
        }

        .signup-header {
            padding: 2rem 2rem 1.5rem;
        }

        .signup-title {
            font-size: 2.25rem;
        }

        .signup-subtitle {
            font-size: 1.125rem;
        }

        .signup-body {
            padding: 2rem;
        }

        .form-section-title {
            font-size: 1.25rem;
        }

        .signup-container {
            padding: 0 1rem;
        }
    }

    @media (max-width: 576px) {
        .signup-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .signup-body {
            padding: 1.5rem;
        }

        .signup-title {
            font-size: 1.875rem;
        }

        .signup-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="signup-section">
    <div class="signup-container">
        <div class="signup-card">
            <div class="signup-header">
                <div class="content">
                    <div class="signup-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <h1 class="signup-title">Join CozyWish for Business</h1>
                    <p class="signup-subtitle">Create your business account and start reaching new customers</p>
                </div>
            </div>

            <div class="signup-body">
            <!-- Display messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="alert-cw alert-cw-{{ message.tags }} mb-4" role="alert">
                  <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                  {{ message }}
                </div>
              {% endfor %}
            {% endif %}

            <form method="post" novalidate>
              {% csrf_token %}

              {% if form.non_field_errors %}
              <div class="alert-cw alert-cw-error mb-4">
                {% for error in form.non_field_errors %}
                <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                {% endfor %}
              </div>
              {% endif %}

              <!-- Account Information Section -->
              <div class="form-section">
                <h5 class="form-section-title">
                  <i class="fas fa-user-circle"></i>Account Information
                </h5>

                <!-- Email field -->
                <div class="form-floating mb-4">
                  {% if form.email.errors %}
                    {{ form.email|add_class:"form-control-cw is-invalid"|attr:"placeholder:<EMAIL>"|attr:"aria-describedby:email_help" }}
                  {% else %}
                    {{ form.email|add_class:"form-control-cw"|attr:"placeholder:<EMAIL>"|attr:"aria-describedby:email_help" }}
                  {% endif %}
                  <label for="{{ form.email.id_for_label }}">
                    <i class="fas fa-envelope"></i>{{ form.email.label }}
                  </label>
                  {% if form.email.errors %}
                  <div class="invalid-feedback" role="alert" aria-live="polite">
                    {% for error in form.email.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                  {% if form.email.help_text %}
                  <div class="form-text" id="email_help">{{ form.email.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Password Row -->
                <div class="form-row">
                  <!-- Password field -->
                  <div>
                    <label for="{{ form.password1.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.password1.label }}
                    </label>
                    <div class="input-group">
                      {% if form.password1.errors %}
                        {{ form.password1|add_class:"form-control-cw is-invalid"|attr:"placeholder:Create a strong password" }}
                      {% else %}
                        {{ form.password1|add_class:"form-control-cw"|attr:"placeholder:Create a strong password" }}
                      {% endif %}
                      <button type="button" class="btn toggle-password" data-target="#{{ form.password1.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.password1.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.password1.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.password1.help_text %}
                    <div class="form-text">{{ form.password1.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Confirm Password field -->
                  <div>
                    <label for="{{ form.password2.id_for_label }}" class="form-label">
                      <i class="fas fa-lock"></i>{{ form.password2.label }}
                    </label>
                    <div class="input-group">
                      {% if form.password2.errors %}
                        {{ form.password2|add_class:"form-control-cw is-invalid"|attr:"placeholder:Confirm your password" }}
                      {% else %}
                        {{ form.password2|add_class:"form-control-cw"|attr:"placeholder:Confirm your password" }}
                      {% endif %}
                      <button type="button" class="btn toggle-password" data-target="#{{ form.password2.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                      </button>
                    </div>
                    {% if form.password2.errors %}
                    <div class="invalid-feedback" role="alert" aria-live="polite">
                      {% for error in form.password2.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.password2.help_text %}
                    <div class="form-text">{{ form.password2.help_text }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>

              <!-- Business Information Section -->
              <div class="form-section">
                <h5 class="form-section-title">
                  <i class="fas fa-building"></i>Business Information
                </h5>

                <!-- Business Name -->
                <div class="form-floating mb-4">
                  {% if form.business_name.errors %}
                    {{ form.business_name|add_class:"form-control-cw is-invalid"|attr:"placeholder:Business Name" }}
                  {% else %}
                    {{ form.business_name|add_class:"form-control-cw"|attr:"placeholder:Business Name" }}
                  {% endif %}
                  <label for="{{ form.business_name.id_for_label }}">
                    <i class="fas fa-building"></i>{{ form.business_name.label }}
                  </label>
                  {% if form.business_name.errors %}
                  <div class="invalid-feedback" role="alert" aria-live="polite">
                    {% for error in form.business_name.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                  {% if form.business_name.help_text %}
                  <div class="form-text">{{ form.business_name.help_text }}</div>
                  {% endif %}
                </div>

                <!-- Business Contact Row -->
                <div class="form-row">
                  <!-- Business Phone -->
                  <div class="form-floating">
                    {% if form.business_phone_number.errors %}
                      {{ form.business_phone_number|add_class:"form-control-cw is-invalid"|attr:"placeholder:Phone Number" }}
                    {% else %}
                      {{ form.business_phone_number|add_class:"form-control-cw"|attr:"placeholder:Phone Number" }}
                    {% endif %}
                    <label for="{{ form.business_phone_number.id_for_label }}">
                      <i class="fas fa-phone"></i>{{ form.business_phone_number.label }}
                    </label>
                    {% if form.business_phone_number.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.business_phone_number.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.business_phone_number.help_text %}
                    <div class="form-text">{{ form.business_phone_number.help_text }}</div>
                    {% endif %}
                  </div>

                  <!-- Contact Person Name -->
                  <div class="form-floating">
                    {% if form.contact_person_name.errors %}
                      {{ form.contact_person_name|add_class:"form-control-cw is-invalid"|attr:"placeholder:Contact Person Name" }}
                    {% else %}
                      {{ form.contact_person_name|add_class:"form-control-cw"|attr:"placeholder:Contact Person Name" }}
                    {% endif %}
                    <label for="{{ form.contact_person_name.id_for_label }}">
                      <i class="fas fa-user-tie"></i>{{ form.contact_person_name.label }}
                    </label>
                    {% if form.contact_person_name.errors %}
                    <div class="invalid-feedback">
                      {% for error in form.contact_person_name.errors %}
                      <i class="fas fa-exclamation-circle"></i>{{ error }}
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.contact_person_name.help_text %}
                    <div class="form-text">{{ form.contact_person_name.help_text }}</div>
                    {% endif %}
                  </div>
                </div>
              </div>

              <!-- Business Address Section -->
              <div class="form-section">
                <h5 class="form-section-title">
                  <i class="fas fa-map-marker-alt"></i>Business Address
                </h5>

                <!-- Street Address -->
                <div class="form-floating mb-4">
                  {% if form.business_address.errors %}
                    {{ form.business_address|add_class:"form-control-cw is-invalid"|attr:"placeholder:Street Address" }}
                  {% else %}
                    {{ form.business_address|add_class:"form-control-cw"|attr:"placeholder:Street Address" }}
                  {% endif %}
                  <label for="{{ form.business_address.id_for_label }}">
                    <i class="fas fa-map-marker-alt"></i>{{ form.business_address.label }}
                  </label>
                  {% if form.business_address.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.business_address.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                </div>

                <!-- City, State, ZIP Row -->
                <div class="row mb-4">
                  <div class="col-md-4">
                    <div class="form-floating">
                      {% if form.city.errors %}
                        {{ form.city|add_class:"form-control-cw is-invalid"|attr:"placeholder:City" }}
                      {% else %}
                        {{ form.city|add_class:"form-control-cw"|attr:"placeholder:City" }}
                      {% endif %}
                      <label for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
                      {% if form.city.errors %}
                      <div class="invalid-feedback">
                        {% for error in form.city.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-floating">
                      {% if form.state.errors %}
                        {{ form.state|add_class:"form-control-cw is-invalid" }}
                      {% else %}
                        {{ form.state|add_class:"form-control-cw" }}
                      {% endif %}
                      <label for="{{ form.state.id_for_label }}">{{ form.state.label }}</label>
                      {% if form.state.errors %}
                      <div class="invalid-feedback">
                        {% for error in form.state.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-floating">
                      {% if form.zip_code.errors %}
                        {{ form.zip_code|add_class:"form-control-cw is-invalid"|attr:"placeholder:ZIP Code" }}
                      {% else %}
                        {{ form.zip_code|add_class:"form-control-cw"|attr:"placeholder:ZIP Code" }}
                      {% endif %}
                      <label for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
                      {% if form.zip_code.errors %}
                      <div class="invalid-feedback">
                        {% for error in form.zip_code.errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>

                <!-- EIN -->
                <div class="form-floating">
                  {% if form.ein.errors %}
                    {{ form.ein|add_class:"form-control-cw is-invalid"|attr:"placeholder:EIN" }}
                  {% else %}
                    {{ form.ein|add_class:"form-control-cw"|attr:"placeholder:EIN" }}
                  {% endif %}
                  <label for="{{ form.ein.id_for_label }}">
                    <i class="fas fa-id-card"></i>{{ form.ein.label }}
                  </label>
                  {% if form.ein.errors %}
                  <div class="invalid-feedback">
                    {% for error in form.ein.errors %}
                    <i class="fas fa-exclamation-circle"></i>{{ error }}
                    {% endfor %}
                  </div>
                  {% endif %}
                  {% if form.ein.help_text %}
                  <div class="form-text">{{ form.ein.help_text }}</div>
                  {% endif %}
                </div>
              </div>

              <!-- Submit Section -->
              <div class="form-section">
                <div class="d-grid mb-4">
                  <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-user-plus me-2"></i>Create Business Account
                  </button>
                </div>

                <!-- Login link -->
                <div class="text-center">
                  <p class="mb-0" style="color: var(--cw-neutral-600);">Already have an account?
                    <a href="{% url 'accounts_app:service_provider_login' %}">Sign in here</a>
                  </p>
                </div>
              </div>
            </form>
            </div>
        </div>
    </div>
</section>

<!-- Password Toggle JavaScript -->
<script>
(function() {
    'use strict';

    // Password toggle functionality
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();

            const targetSelector = toggleBtn.getAttribute('data-target');
            if (!targetSelector) return;

            const input = document.querySelector(targetSelector);
            if (!input) return;

            // Toggle password visibility
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';

            // Update icon
            const icon = toggleBtn.querySelector('i');
            if (icon) {
                if (isPassword) {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Update accessibility attributes
            const newTitle = isPassword ? 'Hide password' : 'Show password';
            toggleBtn.title = newTitle;
            toggleBtn.setAttribute('aria-label', newTitle);

            return false;
        }
    }, true);
})();
</script>
{% endblock %}
