{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Edit Business Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Service Provider Profile Edit */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Edit Profile Section */
    .edit-profile-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .edit-profile-container {
        max-width: 900px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Header */
    .edit-header {
        display: flex;
        align-items: center;
        margin-bottom: 3rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .back-btn {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-right: 1.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        font-size: 1.125rem;
    }

    .back-btn:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .edit-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .edit-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Form Cards */
    .form-card {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .form-card:hover {
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .form-card-header {
        background: var(--cw-gradient-card-subtle);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .form-card-title {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-card-title i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    .form-card-body {
        padding: 2rem;
    }

    /* Form Styling */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control,
    .form-select {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control::placeholder {
        color: var(--cw-neutral-600);
        opacity: 0.7;
    }

    .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Logo Preview */
    .logo-preview-container {
        text-align: center;
        margin-bottom: 1rem;
    }

    .logo-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 1rem;
        border: 3px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-md);
        transition: all 0.3s ease;
    }

    .logo-preview:hover {
        border-color: var(--cw-brand-primary);
        transform: scale(1.02);
    }

    /* Checkbox Styling */
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        background-color: white;
        transition: all 0.2s ease;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        font-family: var(--cw-font-heading);
        font-weight: 500;
        color: var(--cw-brand-primary);
        margin-left: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Action Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        padding: 2rem 0;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 2rem;
    }

    /* Alert Styling */
    .alert {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-success {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .alert-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }

    .alert-info {
        background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .edit-profile-section {
            padding: 3rem 0;
        }

        .edit-profile-container {
            padding: 0 1.5rem;
        }

        .edit-header {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .back-btn {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .edit-title {
            font-size: 2rem;
        }

        .form-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .edit-profile-container {
            padding: 0 1rem;
        }

        .form-card-body {
            padding: 1.5rem;
        }

        .edit-title {
            font-size: 1.75rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
/**
 * Service Provider Profile Edit JavaScript
 * Handles logo preview and form validation
 */
document.addEventListener('DOMContentLoaded', function() {
    // Logo preview functionality
    const logoInput = document.getElementById('{{ form.logo.id_for_label }}');
    const logoPreview = document.getElementById('logo-preview');

    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG or PNG).');
                    this.value = '';
                    return;
                }

                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    this.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    logoPreview.src = e.target.result;
                    logoPreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    }

    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = textarea.scrollHeight + 'px';
    });
});
</script>
{% endblock %}

{% block content %}
<section class="edit-profile-section">
    <div class="edit-profile-container">
        <!-- Header -->
        <div class="edit-header">
            <a href="{% url 'accounts_app:service_provider_profile' %}" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="edit-title">Edit Business Profile</h1>
                <p class="edit-subtitle">Update your business information and settings</p>
            </div>
        </div>

        <!-- Display messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <form method="post" enctype="multipart/form-data" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Basic Business Information -->
            <div class="form-card">
                <div class="form-card-header">
                    <h3 class="form-card-title">
                        <i class="fas fa-building"></i>
                        Basic Information
                    </h3>
                </div>
                <div class="form-card-body">
                    <div class="row">
                        <!-- Legal Name -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.legal_name.id_for_label }}">{{ form.legal_name.label }}</label>
                            {{ form.legal_name|add_class:"form-control" }}
                            {% if form.legal_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.legal_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.legal_name.help_text %}
                                <div class="form-text">{{ form.legal_name.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Display Name -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.display_name.id_for_label }}">{{ form.display_name.label }}</label>
                            {{ form.display_name|add_class:"form-control" }}
                            {% if form.display_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.display_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.display_name.help_text %}
                                <div class="form-text">{{ form.display_name.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label class="form-label" for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                            {{ form.description|add_class:"form-control" }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Business Logo -->
                        <div class="col-12 mb-3">
                            <label class="form-label" for="{{ form.logo.id_for_label }}">{{ form.logo.label }}</label>
                            <div class="logo-preview-container">
                                {% if form.instance.logo %}
                                    <img id="logo-preview" src="{{ form.instance.logo.url }}" alt="Current Logo" class="logo-preview">
                                    <p class="form-text">Current logo</p>
                                {% else %}
                                    <img id="logo-preview" src="https://via.placeholder.com/200x200/fae1d7/2F160F?text=No+Logo" alt="Logo Preview" class="logo-preview" style="display: none;">
                                    <p class="form-text">No logo uploaded</p>
                                {% endif %}
                            </div>
                            {{ form.logo|add_class:"form-control" }}
                            {% if form.logo.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.logo.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.logo.help_text %}
                                <div class="form-text">{{ form.logo.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="form-card">
                <div class="form-card-header">
                    <h3 class="form-card-title">
                        <i class="fas fa-phone"></i>
                        Contact Information
                    </h3>
                </div>
                <div class="form-card-body">
                    <div class="row">
                        <!-- Phone -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                            {{ form.phone|add_class:"form-control" }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Contact Name -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.contact_name.id_for_label }}">{{ form.contact_name.label }}</label>
                            {{ form.contact_name|add_class:"form-control" }}
                            {% if form.contact_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.contact_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.contact_name.help_text %}
                                <div class="form-text">{{ form.contact_name.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="form-card">
                <div class="form-card-header">
                    <h3 class="form-card-title">
                        <i class="fas fa-map-marker-alt"></i>
                        Business Address
                    </h3>
                </div>
                <div class="form-card-body">
                    <div class="row">
                        <!-- Street Address -->
                        <div class="col-12 mb-3">
                            <label class="form-label" for="{{ form.address.id_for_label }}">{{ form.address.label }}</label>
                            {{ form.address|add_class:"form-control" }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.address.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- City -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
                            {{ form.city|add_class:"form-control" }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- State -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="{{ form.state.id_for_label }}">{{ form.state.label }}</label>
                            {{ form.state|add_class:"form-select" }}
                            {% if form.state.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.state.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- ZIP Code -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label" for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
                            {{ form.zip_code|add_class:"form-control" }}
                            {% if form.zip_code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.zip_code.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- County -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.county.id_for_label }}">{{ form.county.label }}</label>
                            {{ form.county|add_class:"form-control" }}
                            {% if form.county.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.county.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- EIN -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.ein.id_for_label }}">{{ form.ein.label }}</label>
                            {{ form.ein|add_class:"form-control" }}
                            {% if form.ein.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.ein.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                            {% if form.ein.help_text %}
                                <div class="form-text">{{ form.ein.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Online Presence -->
            <div class="form-card">
                <div class="form-card-header">
                    <h3 class="form-card-title">
                        <i class="fas fa-globe"></i>
                        Online Presence
                    </h3>
                </div>
                <div class="form-card-body">
                    <div class="row">
                        <!-- Website -->
                        <div class="col-12 mb-3">
                            <label class="form-label" for="{{ form.website.id_for_label }}">{{ form.website.label }}</label>
                            {{ form.website|add_class:"form-control" }}
                            {% if form.website.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.website.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Instagram -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.instagram.id_for_label }}">{{ form.instagram.label }}</label>
                            {{ form.instagram|add_class:"form-control" }}
                            {% if form.instagram.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.instagram.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Facebook -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="{{ form.facebook.id_for_label }}">{{ form.facebook.label }}</label>
                            {{ form.facebook|add_class:"form-control" }}
                            {% if form.facebook.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.facebook.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visibility Settings -->
            <div class="form-card">
                <div class="form-card-header">
                    <h3 class="form-card-title">
                        <i class="fas fa-eye"></i>
                        Visibility Settings
                    </h3>
                </div>
                <div class="form-card-body">
                    <div class="form-check">
                        {{ form.is_public|add_class:"form-check-input" }}
                        <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                            {{ form.is_public.label }}
                        </label>
                        {% if form.is_public.help_text %}
                            <div class="form-text">{{ form.is_public.help_text }}</div>
                        {% endif %}
                        {% if form.is_public.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.is_public.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Action buttons -->
            <div class="form-actions">
                <button type="submit" class="btn-cw-primary">
                    <i class="fas fa-save"></i>
                    Save Changes
                </button>
                <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                    <i class="fas fa-times"></i>
                    Cancel
                </a>
            </div>
        </form>
    </div>
</section>
{% endblock %}
