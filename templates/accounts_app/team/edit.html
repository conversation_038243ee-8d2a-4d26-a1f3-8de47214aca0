{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Edit Team Member - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Team Edit Form */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Form Section */
    .form-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .form-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Form Header */
    .form-hero {
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        padding: 3rem;
        margin-bottom: 3rem;
        box-shadow: var(--cw-shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .form-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="form-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23form-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .form-hero .content {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 2rem;
    }

    .form-title-section {
        flex: 1;
        min-width: 300px;
    }

    .form-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .form-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Form Card */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .card-header-cw {
        background: var(--cw-gradient-card-subtle);
        padding: 2rem 2.5rem;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .card-title-cw {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .card-title-cw i {
        color: var(--cw-brand-primary);
        font-size: 1.375rem;
    }

    .card-body-cw {
        padding: 2.5rem;
    }

    /* Form Controls */
    .form-control-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
    }

    .form-label-cw {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }

    .form-label-cw i {
        color: var(--cw-brand-primary);
        font-size: 1rem;
    }

    .form-text-cw {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.5rem;
        line-height: 1.4;
    }

    .form-group-cw {
        margin-bottom: 2rem;
    }

    /* Form Check */
    .form-check-cw {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1.25rem;
        background: var(--cw-accent-light);
        border-radius: 0.75rem;
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.2s ease;
    }

    .form-check-cw:hover {
        background: var(--cw-brand-accent);
    }

    .form-check-input-cw {
        width: 1.25rem;
        height: 1.25rem;
        margin: 0;
        accent-color: var(--cw-brand-primary);
    }

    .form-check-label-cw {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-family: var(--cw-font-heading);
        margin: 0;
        flex: 1;
    }

    /* Error Styling */
    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .alert-cw-error {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.75rem;
        padding: 1.25rem;
        margin-bottom: 2rem;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .alert-cw-error i {
        color: #dc2626;
        font-size: 1.125rem;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    /* Form Actions */
    .form-actions-section {
        background: var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-top: 2rem;
        position: relative;
        overflow: hidden;
    }

    .form-actions-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="actions-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,10 Q25,15 20,20 Q15,15 20,10" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23actions-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .form-actions-section .content {
        position: relative;
        z-index: 2;
    }

    .form-actions-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Tips Section */
    .tips-section {
        background: var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 3rem;
        margin-top: 4rem;
        position: relative;
        overflow: hidden;
    }

    .tips-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tips-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,10 Q25,15 20,20 Q15,15 20,10" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23tips-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .tips-section .content {
        position: relative;
        z-index: 2;
    }

    .tips-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .tips-title i {
        color: #d97706;
        font-size: 1.25rem;
    }

    .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .tips-list li:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateX(5px);
    }

    .tips-list li:last-child {
        margin-bottom: 0;
    }

    .tips-list li i {
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    .tips-list li span {
        color: var(--cw-neutral-800);
        font-weight: 500;
        line-height: 1.5;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .form-section {
            padding: 3rem 0;
        }

        .form-container {
            padding: 0 1.5rem;
        }

        .form-hero {
            padding: 2rem;
            text-align: center;
        }

        .form-hero .content {
            flex-direction: column;
            text-align: center;
        }

        .form-title {
            font-size: 2rem;
        }

        .form-actions {
            justify-content: center;
            width: 100%;
        }

        .btn-cw-primary,
        .btn-cw-secondary,
        .btn-cw-danger {
            padding: 0.875rem 1.5rem;
        }

        .form-actions-section {
            padding: 2rem;
            margin-top: 2rem;
        }

        .form-actions-buttons {
            flex-direction: column;
            align-items: center;
        }

        .form-actions-buttons .btn-cw-primary,
        .form-actions-buttons .btn-cw-secondary,
        .form-actions-buttons .btn-cw-danger {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .tips-section {
            padding: 2rem;
            margin-top: 2rem;
        }
    }

    @media (max-width: 576px) {
        .form-container {
            padding: 0 1rem;
        }

        .form-hero {
            padding: 1.5rem;
        }

        .card-body-cw {
            padding: 2rem;
        }

        .form-actions-section {
            padding: 1.5rem;
        }

        .tips-section {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="form-section">
    <div class="form-container">
        <!-- Form Header -->
        <div class="form-hero">
            <div class="content">
                <div class="form-title-section">
                    <h1 class="form-title">Edit Team Member</h1>
                    <p class="form-subtitle">Update {{ team_member.name }}'s information</p>
                </div>
                <div class="form-actions">
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>

      <!-- Display messages -->
      {% if messages %}
        {% for message in messages %}
          <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          </div>
        {% endfor %}
      {% endif %}

        <div class="card-cw">
            <div class="card-header-cw">
                <h2 class="card-title-cw">
                    <i class="fas fa-user-edit"></i>
                    Update Team Member Information
                </h2>
            </div>
            <div class="card-body-cw">
                <form method="post" enctype="multipart/form-data" novalidate id="team-member-edit-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert-cw-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <div>
                            {% for error in form.non_field_errors %}
                            {{ error }}{% if not forloop.last %}<br>{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Name field -->
                    <div class="form-group-cw">
                        <label class="form-label-cw">
                            <i class="fas fa-user"></i>
                            Team Member Name
                        </label>
                        <input type="text"
                               name="name"
                               class="form-control-cw{% if form.name.errors %} is-invalid{% endif %}"
                               placeholder="Enter team member's full name"
                               value="{{ form.name.value|default:team_member.name|default:'' }}">
                        {% if form.name.errors %}
                            {% for error in form.name.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-text-cw">
                            Use the team member's professional name as it should appear to customers
                        </div>
                    </div>

                    <!-- Position field -->
                    <div class="form-group-cw">
                        <label class="form-label-cw">
                            <i class="fas fa-briefcase"></i>
                            Role/Position
                        </label>
                        <input type="text"
                               name="position"
                               class="form-control-cw{% if form.position.errors %} is-invalid{% endif %}"
                               placeholder="e.g., Licensed Massage Therapist, Esthetician"
                               value="{{ form.position.value|default:team_member.position|default:'' }}">
                        {% if form.position.errors %}
                            {% for error in form.position.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-text-cw">
                            Include specific titles, certifications, or specializations to help customers choose
                        </div>
                    </div>

                    <!-- Active Status -->
                    <div class="form-group-cw">
                        <div class="form-check-cw">
                            <input type="checkbox"
                                   name="is_active"
                                   class="form-check-input-cw"
                                   id="is_active"
                                   {% if form.is_active.value or team_member.is_active %}checked{% endif %}>
                            <div>
                                <label class="form-check-label-cw" for="is_active">
                                    Active Team Member
                                </label>
                                <div class="form-text-cw" style="margin-top: 0.25rem;">
                                    Active team members are visible to customers. You can deactivate them temporarily without removing their information.
                                </div>
                            </div>
                        </div>
                        {% if form.is_active.errors %}
                            {% for error in form.is_active.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions-section">
            <div class="content">
                <div class="form-actions-buttons">
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" form="team-member-edit-form" class="btn-cw-primary">
                        <i class="fas fa-save"></i>
                        Update Team Member
                    </button>
                    <button type="button"
                            class="btn-cw-danger"
                            data-bs-toggle="modal"
                            data-bs-target="#deleteModal">
                        <i class="fas fa-trash"></i>
                        Delete Member
                    </button>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" style="border: none; border-radius: 1rem; box-shadow: var(--cw-shadow-lg);">
                    <div class="modal-header border-0" style="background: var(--cw-gradient-card-subtle);">
                        <h5 class="modal-title fw-bold" id="deleteModalLabel" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading);">
                            <i class="fas fa-exclamation-triangle me-2" style="color: #dc2626;"></i>
                            Confirm Delete
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="padding: 2rem;">
                        <p style="color: var(--cw-neutral-800); font-size: 1.125rem; margin-bottom: 1rem;">
                            Are you sure you want to remove <strong style="color: var(--cw-brand-primary);">{{ team_member.name }}</strong> from your team?
                        </p>
                        <p style="color: var(--cw-neutral-600); font-size: 0.95rem; margin: 0;">
                            This action cannot be undone. Consider deactivating the team member instead if you might need their information later.
                        </p>
                    </div>
                    <div class="modal-footer border-0" style="padding: 1rem 2rem 2rem;">
                        <button type="button" class="btn-cw-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                        <form method="post" action="{% url 'accounts_app:team_member_delete' team_member.id %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn-cw-danger">
                                <i class="fas fa-trash"></i>
                                Delete Team Member
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tips Section -->
        <div class="tips-section" style="margin-top: 3rem;">
            <div class="content">
                <h3 class="tips-title">
                    <i class="fas fa-lightbulb"></i>
                    Tips for Managing Team Members
                </h3>
                <ul class="tips-list">
                    <li>
                        <i class="fas fa-edit"></i>
                        <span>Update team member information regularly to keep customer-facing details current and accurate</span>
                    </li>
                    <li>
                        <i class="fas fa-toggle-on"></i>
                        <span>Use the active/inactive toggle to temporarily manage availability without losing team member data</span>
                    </li>
                    <li>
                        <i class="fas fa-certificate"></i>
                        <span>Keep certifications and specializations up to date to help customers make informed choices</span>
                    </li>
                    <li>
                        <i class="fas fa-trash-alt"></i>
                        <span>Consider deactivating instead of deleting if you might need the team member's information in the future</span>
                    </li>
                    <li>
                        <i class="fas fa-users"></i>
                        <span>Professional team profiles build customer confidence and help differentiate your services</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>
{% endblock %}
