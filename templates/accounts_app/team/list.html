{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Team Members - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Team Management */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Team Management Section */
    .team-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .team-container {
        max-width: 1200px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Team Header */
    .team-hero {
        background: var(--cw-gradient-card-subtle);
        border-radius: 1rem;
        padding: 3rem;
        margin-bottom: 3rem;
        box-shadow: var(--cw-shadow-lg);
        position: relative;
        overflow: hidden;
    }

    .team-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="team-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23team-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .team-hero .content {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 2rem;
    }

    .team-title-section {
        flex: 1;
        min-width: 300px;
    }

    .team-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .team-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    .team-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-danger {
        border: 2px solid #dc2626;
        color: #dc2626;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-danger:hover {
        background: #dc2626;
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Team Member Cards */
    .card-cw {
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .card-cw:hover {
        transform: translateY(-5px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-accent);
    }

    .team-member-card {
        padding: 2rem;
        text-align: center;
        position: relative;
    }

    .team-member-photo-container {
        position: relative;
        display: inline-block;
        margin-bottom: 1.5rem;
    }

    .team-member-photo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: var(--cw-shadow-lg);
        transition: all 0.3s ease;
    }

    .team-member-photo-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: var(--cw-gradient-card-subtle);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-neutral-600);
        font-size: 2.5rem;
        border: 4px solid white;
        box-shadow: var(--cw-shadow-lg);
    }

    .status-badge {
        position: absolute;
        bottom: 5px;
        right: 5px;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .status-active {
        background: #059669;
        color: white;
    }

    .status-inactive {
        background: var(--cw-neutral-600);
        color: white;
    }

    .team-member-name {
        font-family: var(--cw-font-heading);
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .team-member-position {
        color: var(--cw-neutral-600);
        font-size: 1rem;
        margin-bottom: 1.5rem;
        font-style: italic;
    }

    .team-member-actions {
        display: flex;
        gap: 0.75rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Empty State */
    .empty-state {
        padding: 5rem 0;
        text-align: center;
    }

    .empty-state-content {
        max-width: 500px;
        margin: 0 auto;
    }

    .empty-state-icon {
        width: 120px;
        height: 120px;
        background: var(--cw-gradient-card-subtle);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        color: var(--cw-neutral-600);
        font-size: 3rem;
        box-shadow: var(--cw-shadow-md);
    }

    .empty-state-title {
        font-family: var(--cw-font-display);
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .empty-state-description {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        line-height: 1.6;
        margin-bottom: 2.5rem;
    }

    /* Tips Section */
    .tips-section {
        background: var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 3rem;
        margin-top: 4rem;
        position: relative;
        overflow: hidden;
    }

    .tips-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tips-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20,10 Q25,15 20,20 Q15,15 20,10" fill="%23f1d4c4" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23tips-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .tips-section .content {
        position: relative;
        z-index: 2;
    }

    .tips-title {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .tips-title i {
        color: #d97706;
        font-size: 1.25rem;
    }

    .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .tips-list li:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateX(5px);
    }

    .tips-list li:last-child {
        margin-bottom: 0;
    }

    .tips-list li i {
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    .tips-list li span {
        color: var(--cw-neutral-800);
        font-weight: 500;
        line-height: 1.5;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .team-section {
            padding: 3rem 0;
        }

        .team-container {
            padding: 0 1.5rem;
        }

        .team-hero {
            padding: 2rem;
            text-align: center;
        }

        .team-hero .content {
            flex-direction: column;
            text-align: center;
        }

        .team-title {
            font-size: 2rem;
        }

        .team-actions {
            justify-content: center;
            width: 100%;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            padding: 0.875rem 1.5rem;
        }

        .empty-state {
            padding: 3rem 0;
        }

        .empty-state-title {
            font-size: 1.875rem;
        }

        .tips-section {
            padding: 2rem;
            margin-top: 3rem;
        }

        .team-member-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .team-member-actions .btn-sm {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .team-container {
            padding: 0 1rem;
        }

        .team-hero {
            padding: 1.5rem;
        }

        .tips-section {
            padding: 1.5rem;
        }

        .team-member-photo,
        .team-member-photo-placeholder {
            width: 100px;
            height: 100px;
        }

        .empty-state-icon {
            width: 100px;
            height: 100px;
            font-size: 2.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="team-section">
    <div class="team-container">
        <!-- Team Header -->
        <div class="team-hero">
            <div class="content">
                <div class="team-title-section">
                    <h1 class="team-title">Team Members</h1>
                    <p class="team-subtitle">Manage your business team and staff</p>
                </div>
                <div class="team-actions">
                    <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Profile
                    </a>
                    {% if can_add_member %}
                    <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                        <i class="fas fa-plus"></i>
                        Add Team Member
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Team Members Grid -->
        {% if team_members %}
        <div class="team-grid">
            <div class="row g-4">
                {% for team_member in team_members %}
                <div class="col-lg-4 col-md-6">
                    <div class="card-cw team-member-card">
                        <div class="team-member-photo-container">
                            {% if team_member.photo %}
                                <img src="{{ team_member.photo.url }}"
                                     alt="{{ team_member.name }}"
                                     class="team-member-photo">
                            {% else %}
                                <div class="team-member-photo-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}

                            {% if team_member.is_active %}
                                <div class="status-badge status-active">
                                    <i class="fas fa-check-circle"></i>
                                    Active
                                </div>
                            {% else %}
                                <div class="status-badge status-inactive">
                                    <i class="fas fa-pause-circle"></i>
                                    Inactive
                                </div>
                            {% endif %}
                        </div>

                        <div class="team-member-info">
                            <h5 class="team-member-name">{{ team_member.name }}</h5>
                            <p class="team-member-position">{{ team_member.position|default:"Team Member" }}</p>

                            <div class="team-member-actions">
                                <a href="{% url 'accounts_app:team_member_edit' team_member.id %}"
                                   class="btn-cw-secondary btn-sm">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </a>
                                <button type="button"
                                        class="btn-cw-danger btn-sm"
                                        data-bs-toggle="modal"
                                        data-bs-target="#deleteModal{{ team_member.id }}">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div class="modal fade" id="deleteModal{{ team_member.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ team_member.id }}" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content" style="border: none; border-radius: 1rem; box-shadow: var(--cw-shadow-lg);">
                            <div class="modal-header border-0" style="background: var(--cw-gradient-card-subtle);">
                                <h5 class="modal-title fw-bold" id="deleteModalLabel{{ team_member.id }}" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading);">
                                    <i class="fas fa-exclamation-triangle me-2" style="color: #dc2626;"></i>
                                    Confirm Delete
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" style="padding: 2rem;">
                                <p style="color: var(--cw-neutral-800); font-size: 1.125rem; margin-bottom: 1rem;">
                                    Are you sure you want to remove <strong style="color: var(--cw-brand-primary);">{{ team_member.name }}</strong> from your team?
                                </p>
                                <p style="color: var(--cw-neutral-600); font-size: 0.95rem; margin: 0;">
                                    This action cannot be undone.
                                </p>
                            </div>
                            <div class="modal-footer border-0" style="padding: 1rem 2rem 2rem;">
                                <button type="button" class="btn-cw-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times"></i>
                                    Cancel
                                </button>
                                <form method="post" action="{% url 'accounts_app:team_member_delete' team_member.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn-cw-danger">
                                        <i class="fas fa-trash"></i>
                                        Delete Team Member
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-state-content">
                <div class="empty-state-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="empty-state-title">No Team Members Yet</h3>
                <p class="empty-state-description">
                    Start building your professional team by adding your first team member.
                    Team members help customers choose the right service provider for their needs.
                </p>
                {% if can_add_member %}
                <a href="{% url 'accounts_app:team_member_add' %}" class="btn-cw-primary">
                    <i class="fas fa-plus"></i>
                    Add Your First Team Member
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Team Management Tips -->
        <div class="tips-section">
            <div class="content">
                <h3 class="tips-title">
                    <i class="fas fa-lightbulb"></i>
                    Team Management Tips
                </h3>
                <ul class="tips-list">
                    <li>
                        <i class="fas fa-camera"></i>
                        <span>Add professional photos to build customer trust and showcase your team's expertise</span>
                    </li>
                    <li>
                        <i class="fas fa-certificate"></i>
                        <span>Include specific titles, certifications, and specializations to help customers choose the right provider</span>
                    </li>
                    <li>
                        <i class="fas fa-sync-alt"></i>
                        <span>Keep team information up to date with current skills, availability, and contact details</span>
                    </li>
                    <li>
                        <i class="fas fa-toggle-on"></i>
                        <span>Use the active/inactive status to manage team member availability without removing them permanently</span>
                    </li>
                    <li>
                        <i class="fas fa-users-cog"></i>
                        <span>Regularly review and update your team structure to reflect your business growth and service offerings</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>
{% endblock %}
