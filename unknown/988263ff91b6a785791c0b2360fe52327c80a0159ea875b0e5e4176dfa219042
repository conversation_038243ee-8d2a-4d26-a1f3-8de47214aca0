from datetime import timed<PERSON><PERSON>
from decimal import Decimal

import pytest
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.urls import reverse
from django.utils import timezone
from model_bakery import baker

from venues_app.models import (
    Category,
    Venue,
    VenueCategory,
    VenueImage,
    VenueFAQ,
    Service,
    OperatingHours,
    FlaggedVenue,
    USCity,
)
from accounts_app.models import CustomUser, ServiceProviderProfile

# All tests use the database
pytestmark = pytest.mark.django_db


# --- Category Model ---

def test_category_creation():
    """Test basic category creation with slug generation."""
    category = Category.objects.create(
        category_name="Spa & Wellness",
        category_description="Full service spa and wellness center"
    )
    assert category.slug == "spa-wellness"
    assert category.is_active is True
    assert str(category) == "Spa & Wellness"


def test_category_slug_uniqueness():
    """Test that duplicate category names get unique slugs."""
    Category.objects.create(category_name="Spa")
    category2 = Category.objects.create(category_name="Spa")
    assert category2.slug == "spa-1"


def test_category_backward_compatibility_properties():
    """Test backward compatibility name and description properties."""
    category = baker.make(
        Category,
        category_name="Massage Therapy",
        category_description="Professional massage services"
    )
    assert category.name == "Massage Therapy"
    assert category.description == "Professional massage services"


def test_category_get_absolute_url():
    """Test category URL generation."""
    category = baker.make(Category, category_name="Facial", slug="facial")
    expected_url = reverse('venues_app:category_venues', kwargs={'category_slug': 'facial'})
    assert category.get_absolute_url() == expected_url


def test_category_ordering():
    """Test category ordering by name."""
    baker.make(Category, category_name="Spa")
    baker.make(Category, category_name="Massage")
    baker.make(Category, category_name="Facial")

    categories = list(Category.objects.all())
    names = [c.category_name for c in categories]
    assert names == ["Facial", "Massage", "Spa"]


# --- Venue Model ---

def test_venue_creation():
    """Test basic venue creation with required fields."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    service_provider = baker.make(ServiceProviderProfile, user=user)

    venue = Venue.objects.create(
        service_provider=service_provider,
        venue_name="Serenity Spa",
        short_description="Relaxing spa experience",
        state="CA",
        county="Los Angeles",
        city="Beverly Hills",
        street_number="123",
        street_name="Rodeo Drive"
    )
    assert venue.slug.startswith("serenity-spa")
    assert venue.approval_status == Venue.PENDING
    assert venue.visibility == Venue.ACTIVE
    assert not venue.is_deleted


def test_venue_slug_generation_and_uniqueness():
    """Test venue slug generation and uniqueness handling."""
    user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    service_provider1 = baker.make(ServiceProviderProfile, user=user1)
    user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    service_provider2 = baker.make(ServiceProviderProfile, user=user2)

    venue1 = baker.make(Venue, service_provider=service_provider1, venue_name="Test Spa")
    venue2 = baker.make(Venue, service_provider=service_provider2, venue_name="Test Spa")

    assert venue1.slug == "test-spa"
    assert venue2.slug == "test-spa-1"


def test_venue_str_representation():
    """Test venue string representation."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    service_provider = baker.make(ServiceProviderProfile, user=user, display_name="Cozy Spa")
    venue = baker.make(Venue, service_provider=service_provider, venue_name="Main Location")

    assert str(venue) == "Main Location - Cozy Spa"


def test_venue_status_properties():
    """Test venue status helper properties."""
    venue = baker.make(Venue, approval_status=Venue.APPROVED, visibility=Venue.ACTIVE)
    assert venue.is_approved is True
    assert venue.is_visible is True

    venue.approval_status = Venue.PENDING
    assert venue.is_approved is False
    assert venue.is_visible is False


def test_venue_full_address_property():
    """Test venue full address property."""
    venue = baker.make(
        Venue,
        street_number="123",
        street_name="Main Street",
        city="Beverly Hills",
        county="Los Angeles",
        state="CA"
    )
    expected = "123 Main Street, Beverly Hills, Los Angeles, CA"
    assert venue.full_address == expected


def test_venue_soft_delete():
    """Test venue soft delete functionality."""
    venue = baker.make(Venue, visibility=Venue.ACTIVE)
    venue.delete()

    venue.refresh_from_db()
    assert venue.is_deleted is True
    assert venue.visibility == Venue.INACTIVE


def test_venue_get_tags_list():
    """Test venue tags parsing and normalization."""
    venue = baker.make(Venue, tags="spa, massage, RELAXATION, wellness, spa")
    tags = venue.get_tags_list()

    # Should be normalized, deduplicated, and lowercased
    expected = {"spa", "massage", "relaxation", "wellness"}
    assert set(tags) == expected


def test_venue_get_tags_list_empty():
    """Test venue tags list when no tags are set."""
    venue = baker.make(Venue, tags="")
    assert venue.get_tags_list() == []

    venue.tags = None
    assert venue.get_tags_list() == []


def test_venue_approval_status_tracking():
    """Test venue approval status tracking and timestamps."""
    venue = baker.make(Venue, approval_status=Venue.PENDING)
    assert venue.approved_at is None

    # Approve venue
    venue.approval_status = Venue.APPROVED
    venue.save()
    assert venue.approved_at is not None

    # Reject venue
    venue.approval_status = Venue.REJECTED
    venue.save()
    assert venue.approved_at is None


def test_venue_get_primary_image():
    """Test venue primary image fallback logic."""
    venue = baker.make(Venue)

    # No images - should return None or default
    result = venue.get_primary_image()
    assert result is None or result == getattr(settings, 'DEFAULT_VENUE_IMAGE_URL', None)


def test_venue_get_absolute_url():
    """Test venue URL generation."""
    venue = baker.make(Venue, venue_name="Test Spa", slug="test-spa")
    expected_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': 'test-spa'})
    assert venue.get_absolute_url() == expected_url


# --- VenueCategory Model ---

def test_venue_category_creation():
    """Test venue-category relationship creation."""
    venue = baker.make(Venue)
    category = baker.make(Category, category_name="Spa")

    venue_category = VenueCategory.objects.create(venue=venue, category=category)
    assert str(venue_category) == f"{venue.venue_name} - Spa"
    assert venue.categories.count() == 1
    assert category.venues.count() == 1


def test_venue_category_uniqueness():
    """Test unique venue-category constraint."""
    venue = baker.make(Venue)
    category = baker.make(Category)

    VenueCategory.objects.create(venue=venue, category=category)
    with pytest.raises(IntegrityError):
        VenueCategory.objects.create(venue=venue, category=category)


# --- VenueCategory Model ---
def test_venue_category_relationship():
    """Test venue-category relationship."""
    venue = baker.make(Venue)
    category = baker.make(Category, category_name="Spa")

    vc = VenueCategory.objects.create(venue=venue, category=category)
    assert str(vc) == f"{venue.venue_name} - Spa"
    assert venue.categories.count() == 1
    assert category.venues.count() == 1


def test_venue_category_uniqueness():
    """Test unique venue-category constraint."""
    venue = baker.make(Venue)
    category = baker.make(Category)

    VenueCategory.objects.create(venue=venue, category=category)
    with pytest.raises(IntegrityError):
        VenueCategory.objects.create(venue=venue, category=category)


# --- VenueImage Model ---

def test_venue_image_creation():
    """Test venue image creation and string representation."""
    venue = baker.make(Venue)
    image = baker.make(VenueImage, venue=venue, order=1)
    assert str(image) == f"{venue.venue_name} - Image 1"
    assert image.is_active is True


def test_venue_image_limit_validation():
    """Test maximum image limit enforcement."""
    venue = baker.make(Venue)

    # Create 5 images (maximum allowed)
    for i in range(1, 6):
        baker.make(VenueImage, venue=venue, order=i)

    # Should not allow 6th image
    with pytest.raises(ValidationError) as e:
        image = VenueImage(venue=venue, order=6)
        image.full_clean()
    assert "Maximum 5 images" in str(e.value)


def test_venue_image_order_uniqueness():
    """Test unique order constraint per venue."""
    venue = baker.make(Venue)
    baker.make(VenueImage, venue=venue, order=1)

    with pytest.raises(IntegrityError):
        baker.make(VenueImage, venue=venue, order=1)


def test_venue_image_ordering():
    """Test venue image ordering."""
    venue = baker.make(Venue)
    image3 = baker.make(VenueImage, venue=venue, order=3)
    image1 = baker.make(VenueImage, venue=venue, order=1)
    image2 = baker.make(VenueImage, venue=venue, order=2)

    images = list(venue.images.all())
    assert images == [image1, image2, image3]


# --- VenueFAQ Model ---

def test_venue_faq_creation():
    """Test venue FAQ creation and string representation."""
    venue = baker.make(Venue)
    faq = baker.make(VenueFAQ, venue=venue, order=1, question="What services do you offer?")
    assert "What services do you offer?" in str(faq)


def test_venue_faq_limit_validation():
    """Test maximum FAQ limit enforcement."""
    venue = baker.make(Venue)

    # Create 5 FAQs (maximum allowed)
    for i in range(1, 6):
        baker.make(VenueFAQ, venue=venue, order=i)

    # Should not allow 6th FAQ
    with pytest.raises(ValidationError) as e:
        faq = VenueFAQ(venue=venue, order=6)
        faq.full_clean()
    assert "Maximum 5 FAQs" in str(e.value)


def test_venue_faq_order_uniqueness():
    """Test unique order constraint per venue."""
    venue = baker.make(Venue)
    baker.make(VenueFAQ, venue=venue, order=1)

    with pytest.raises(IntegrityError):
        baker.make(VenueFAQ, venue=venue, order=1)


def test_venue_faq_ordering():
    """Test venue FAQ ordering."""
    venue = baker.make(Venue)
    faq3 = baker.make(VenueFAQ, venue=venue, order=3)
    faq1 = baker.make(VenueFAQ, venue=venue, order=1)
    faq2 = baker.make(VenueFAQ, venue=venue, order=2)

    faqs = list(venue.faqs.all())
    assert faqs == [faq1, faq2, faq3]


# --- Service Model ---

def test_service_creation():
    """Test service creation and display properties."""
    venue = baker.make(Venue)
    service = Service.objects.create(
        venue=venue,
        service_title="Deep Tissue Massage",
        short_description="60 min massage",
        price_min=Decimal("89.99"),
        duration_minutes=60
    )
    assert service.slug.startswith("deep-tissue-massage")
    assert service.price_display == "$89.99"
    assert service.duration_display == "1h 0m"
    assert str(service) == "Deep Tissue Massage"


def test_service_slug_generation_and_uniqueness():
    """Test service slug generation and uniqueness handling."""
    venue = baker.make(Venue)
    service1 = baker.make(Service, venue=venue, service_title="Massage Therapy")
    service2 = baker.make(Service, venue=venue, service_title="Massage Therapy")

    assert service1.slug == "massage-therapy"
    assert service2.slug == "massage-therapy-1"


def test_service_variable_pricing():
    """Test service with variable pricing display."""
    service = baker.make(
        Service,
        price_min=Decimal("75.00"),
        price_max=Decimal("125.00")
    )
    assert service.price_display == "$75.00 - $125.00"


def test_service_duration_display():
    """Test service duration display formatting."""
    service = baker.make(Service, duration_minutes=90)
    assert service.duration_display == "1h 30m"

    service.duration_minutes = 45
    assert service.duration_display == "45m"

    service.duration_minutes = 120
    assert service.duration_display == "2h 0m"


def test_service_price_validation():
    """Test service price validation rules."""
    venue = baker.make(Venue)

    # Valid pricing
    service = baker.make(Service, venue=venue, price_min=Decimal("50.00"))
    service.full_clean()

    # Price max < min
    service.price_max = Decimal("40.00")
    with pytest.raises(ValidationError) as e:
        service.full_clean()
    assert "less than minimum price" in str(e.value)

    # Price min <= 0
    service.price_min = Decimal("0.00")
    with pytest.raises(ValidationError) as e:
        service.full_clean()
    assert "greater than zero" in str(e.value)


def test_service_limit_validation():
    """Test maximum service limit enforcement per venue."""
    venue = baker.make(Venue)

    # Create 7 services (maximum allowed)
    for i in range(7):
        baker.make(Service, venue=venue, service_title=f"Service {i}")

    # Should not allow 8th service
    with pytest.raises(ValidationError) as e:
        service = Service(venue=venue, service_title="Extra Service", price_min=Decimal("50.00"))
        service.full_clean()
    assert "Maximum 7 services" in str(e.value)


def test_service_backward_compatibility_property():
    """Test backward compatibility price property."""
    service = baker.make(Service, price_min=Decimal("75.00"))
    assert service.price == Decimal("75.00")


# --- OperatingHours Model ---

def test_operating_hours_creation():
    """Test operating hours creation and string representation."""
    venue = baker.make(Venue)
    hours = OperatingHours.objects.create(
        venue=venue,
        day=0,  # Monday
        opening="09:00:00",
        closing="17:00:00"
    )
    assert "Monday" in str(hours)
    assert not hours.is_closed


def test_operating_hours_closed_day():
    """Test marking venue as closed for a day."""
    venue = baker.make(Venue)
    hours = OperatingHours.objects.create(
        venue=venue,
        day=6,  # Sunday
        is_closed=True
    )
    assert "Closed" in str(hours)
    assert hours.is_closed


def test_operating_hours_validation():
    """Test operating hours validation rules."""
    venue = baker.make(Venue)

    # Missing times when not closed
    hours = OperatingHours(venue=venue, day=0)
    with pytest.raises(ValidationError) as e:
        hours.full_clean()
    assert "required when not closed" in str(e.value)

    # Closing before opening
    hours = OperatingHours(
        venue=venue,
        day=0,
        opening="18:00:00",
        closing="09:00:00"
    )
    with pytest.raises(ValidationError) as e:
        hours.full_clean()
    assert "before closing time" in str(e.value)


def test_operating_hours_uniqueness():
    """Test unique day constraint per venue."""
    venue = baker.make(Venue)
    baker.make(OperatingHours, venue=venue, day=0)

    with pytest.raises(IntegrityError):
        baker.make(OperatingHours, venue=venue, day=0)


@pytest.mark.parametrize(
    "day, expected_name",
    [
        (0, "Monday"),
        (1, "Tuesday"),
        (2, "Wednesday"),
        (3, "Thursday"),
        (4, "Friday"),
        (5, "Saturday"),
        (6, "Sunday"),
    ],
)
def test_operating_hours_day_names(day, expected_name):
    """Test day name display for all days of the week."""
    venue = baker.make(Venue)
    hours = baker.make(OperatingHours, venue=venue, day=day, is_closed=True)
    assert expected_name in str(hours)


# --- FlaggedVenue Model ---

def test_flagged_venue_creation():
    """Test flagged venue creation and workflow."""
    venue = baker.make(Venue)
    customer_user = baker.make(CustomUser, role=CustomUser.CUSTOMER)

    flag = FlaggedVenue.objects.create(
        venue=venue,
        flagged_by=customer_user,
        reason="Inappropriate content"
    )
    assert flag.status == FlaggedVenue.PENDING
    assert str(flag) == f"Flag: {venue.venue_name} by {customer_user.email}"
    assert not flag.is_resolved


def test_flagged_venue_resolution():
    """Test flagged venue resolution workflow."""
    flag = baker.make(FlaggedVenue, status=FlaggedVenue.PENDING)
    admin_user = baker.make(CustomUser, is_staff=True)

    # Resolve flag
    flag.status = FlaggedVenue.RESOLVED
    flag.reviewed_by = admin_user
    flag.save()

    assert flag.reviewed_at is not None
    assert flag.is_resolved is True


def test_flagged_venue_cleanup():
    """Test old flagged venue cleanup."""
    # Create old flag
    old_flag = baker.make(FlaggedVenue)
    FlaggedVenue.objects.filter(pk=old_flag.pk).update(
        created_at=timezone.now() - timedelta(days=91)
    )

    # Create recent flag
    recent_flag = baker.make(FlaggedVenue)

    # Cleanup old flags
    FlaggedVenue.cleanup_old_flags(days=90)

    assert not FlaggedVenue.objects.filter(pk=old_flag.pk).exists()
    assert FlaggedVenue.objects.filter(pk=recent_flag.pk).exists()


def test_flagged_venue_uniqueness():
    """Test unique flag constraint per venue and user."""
    venue = baker.make(Venue)
    customer_user = baker.make(CustomUser, role=CustomUser.CUSTOMER)

    baker.make(FlaggedVenue, venue=venue, flagged_by=customer_user)

    with pytest.raises(IntegrityError):
        baker.make(FlaggedVenue, venue=venue, flagged_by=customer_user)




# --- USCity Model ---

def test_uscity_creation():
    """Test US city creation and string representation."""
    city = USCity.objects.create(
        city="Los Angeles",
        state_id="CA",
        state_name="California",
        county_name="Los Angeles County",
        latitude=Decimal("34.0522"),
        longitude=Decimal("-118.2437"),
        zip_codes="90001 90002 90003",
        city_id="ca-los-angeles"
    )
    assert str(city) == "Los Angeles, CA"
    assert city.get_full_location() == "Los Angeles, Los Angeles County, California"


def test_uscity_uniqueness():
    """Test unique city_id constraint."""
    baker.make(USCity, city_id="ca-san-francisco")

    with pytest.raises(IntegrityError):
        baker.make(USCity, city_id="ca-san-francisco")


def test_uscity_ordering():
    """Test US city ordering by state and city name."""
    baker.make(USCity, city="San Francisco", state_name="California")
    baker.make(USCity, city="Los Angeles", state_name="California")
    baker.make(USCity, city="New York", state_name="New York")

    cities = list(USCity.objects.all())
    # Should be ordered by state_name, then city
    expected_order = ["Los Angeles", "San Francisco", "New York"]
    actual_order = [city.city for city in cities]
    assert actual_order == expected_order


# --- Model Validation Tests ---

def test_venue_coordinate_validation():
    """Test venue coordinate field validation."""
    venue = baker.make(Venue)

    # Valid coordinates
    venue.latitude = Decimal("34.0522")
    venue.longitude = Decimal("-118.2437")
    venue.full_clean()

    # Test coordinate precision limits
    venue.latitude = Decimal("12.12345678")  # 8 decimal places (max)
    venue.longitude = Decimal("-123.12345678")  # 8 decimal places (max)
    venue.full_clean()


def test_venue_address_fields_required():
    """Test that required address fields are validated."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    service_provider = baker.make(ServiceProviderProfile, user=user)

    # Missing required fields should raise ValidationError
    venue = Venue(
        service_provider=service_provider,
        venue_name="Test Venue"
        # Missing state, county, city
    )
    with pytest.raises(ValidationError):
        venue.full_clean()


def test_service_duration_validation():
    """Test service duration field validation."""
    service = baker.make(Service, duration_minutes=30)

    # Valid duration
    service.full_clean()

    # Test minimum duration
    service.duration_minutes = 0
    with pytest.raises(ValidationError):
        service.full_clean()


def test_venue_image_file_validation():
    """Test venue image file field validation."""
    venue = baker.make(Venue)
    image = VenueImage(venue=venue, order=1)

    # Should require image file
    with pytest.raises(ValidationError):
        image.full_clean()


# --- Model Relationship Tests ---

def test_venue_service_provider_relationship():
    """Test venue-service provider relationship."""
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    service_provider = baker.make(ServiceProviderProfile, user=user)
    venue = baker.make(Venue, service_provider=service_provider)

    assert venue.service_provider == service_provider
    assert service_provider.venue == venue


def test_venue_categories_many_to_many():
    """Test venue categories many-to-many relationship."""
    venue = baker.make(Venue)
    category1 = baker.make(Category, category_name="Spa")
    category2 = baker.make(Category, category_name="Massage")

    # Add categories through VenueCategory
    VenueCategory.objects.create(venue=venue, category=category1)
    VenueCategory.objects.create(venue=venue, category=category2)

    assert venue.categories.count() == 2
    assert category1 in venue.categories.all()
    assert category2 in venue.categories.all()


def test_venue_services_relationship():
    """Test venue-services one-to-many relationship."""
    venue = baker.make(Venue)
    service1 = baker.make(Service, venue=venue, service_title="Massage")
    service2 = baker.make(Service, venue=venue, service_title="Facial")

    assert venue.services.count() == 2
    assert service1 in venue.services.all()
    assert service2 in venue.services.all()


def test_venue_images_relationship():
    """Test venue-images one-to-many relationship."""
    venue = baker.make(Venue)
    image1 = baker.make(VenueImage, venue=venue, order=1)
    image2 = baker.make(VenueImage, venue=venue, order=2)

    assert venue.images.count() == 2
    assert image1 in venue.images.all()
    assert image2 in venue.images.all()


def test_venue_operating_hours_relationship():
    """Test venue-operating hours one-to-many relationship."""
    venue = baker.make(Venue)
    hours1 = baker.make(OperatingHours, venue=venue, day=0)  # Monday
    hours2 = baker.make(OperatingHours, venue=venue, day=1)  # Tuesday

    assert venue.operating_hours_set.count() == 2
    assert hours1 in venue.operating_hours_set.all()
    assert hours2 in venue.operating_hours_set.all()


# --- Model Method Tests ---

def test_category_save_method():
    """Test category save method slug generation."""
    category = Category(category_name="Spa & Wellness Center")
    category.save()

    assert category.slug == "spa-wellness-center"


def test_venue_save_method_status_tracking():
    """Test venue save method status tracking."""
    venue = baker.make(Venue, approval_status=Venue.PENDING)
    original_log_length = len(venue.status_log)

    # Change status
    venue.approval_status = Venue.APPROVED
    venue.save()

    # Status log should be updated
    assert len(venue.status_log) > original_log_length
    assert venue.approved_at is not None


def test_service_save_method():
    """Test service save method slug generation."""
    service = Service(
        venue=baker.make(Venue),
        service_title="Deep Tissue Massage Therapy",
        price_min=Decimal("75.00")
    )
    service.save()

    assert service.slug == "deep-tissue-massage-therapy"