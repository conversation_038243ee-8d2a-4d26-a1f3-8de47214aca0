{% extends 'review_app/base_review.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block review_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>{{ page_title }}</h1>
            
            {% if reviews.object_list %}
                <div class="reviews-list">
                    {% for review in reviews %}
                        <div class="review-item card mb-3">
                            <div class="card-body">
                                <div class="review-header d-flex justify-content-between">
                                    <div>
                                        <h5>{{ review.venue.venue_name }}</h5>
                                        <span class="rating">{{ review.rating }}/5 stars</span>
                                    </div>
                                    <div class="text-right">
                                        <small class="text-muted">{{ review.created_at|date:"M d, Y" }}</small>
                                        <div class="review-actions mt-1">
                                            <a href="{% url 'review_app:edit_review' review.slug %}" class="btn btn-sm btn-outline-primary">Edit</a>
                                            <a href="{% url 'review_app:venue_reviews' review.venue.id %}" class="btn btn-sm btn-outline-secondary">View Venue</a>
                                        </div>
                                    </div>
                                </div>
                                
                                <p class="review-text mt-2">{{ review.written_review }}</p>
                                
                                {% if review.response %}
                                    <div class="provider-response mt-3 p-3 bg-light">
                                        <strong>Response from {{ review.venue.venue_name }}:</strong>
                                        <p>{{ review.response.response_text }}</p>
                                        <small class="text-muted">{{ review.response.created_at|date:"M d, Y" }}</small>
                                    </div>
                                {% endif %}
                                
                                {% if review.is_flagged %}
                                    <div class="alert alert-warning mt-2">
                                        <small><i class="fas fa-flag"></i> This review has been flagged and is under moderation.</small>
                                    </div>
                                {% endif %}
                                
                                {% if not review.is_approved %}
                                    <div class="alert alert-info mt-2">
                                        <small><i class="fas fa-clock"></i> This review is pending approval.</small>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                {% if reviews.has_other_pages %}
                    <nav aria-label="Reviews pagination">
                        <ul class="pagination">
                            {% if reviews.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ reviews.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">{{ reviews.number }} of {{ reviews.paginator.num_pages }}</span>
                            </li>
                            
                            {% if reviews.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="no-reviews text-center py-5">
                    <h3>No reviews found</h3>
                    <p>You haven't written any reviews yet.</p>
                    <a href="{% url 'venues_app:venue_list' %}" class="btn btn-primary">Browse Venues</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
