"""Forms for venue operating hours management."""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import OperatingHours


class OperatingHoursForm(forms.ModelForm):
    """Form for managing individual operating hours."""

    class Meta:
        model = OperatingHours
        fields = ['day', 'opening', 'closing', 'is_closed']
        widgets = {
            'day': forms.Select(attrs={
                'class': 'form-control',
            }),
            'opening': forms.Select(attrs={
                'class': 'form-control',
            }, choices=[
                ('', 'Select opening time'),
                ('06:00', '6:00 AM'),
                ('07:00', '7:00 AM'),
                ('08:00', '8:00 AM'),
                ('09:00', '9:00 AM'),
                ('10:00', '10:00 AM'),
                ('11:00', '11:00 AM'),
                ('12:00', '12:00 PM'),
                ('13:00', '1:00 PM'),
                ('14:00', '2:00 PM'),
                ('15:00', '3:00 PM'),
                ('16:00', '4:00 PM'),
                ('17:00', '5:00 PM'),
                ('18:00', '6:00 PM'),
                ('19:00', '7:00 PM'),
                ('20:00', '8:00 PM'),
                ('21:00', '9:00 PM'),
                ('22:00', '10:00 PM'),
            ]),
            'closing': forms.Select(attrs={
                'class': 'form-control',
            }, choices=[
                ('', 'Select closing time'),
                ('14:00', '2:00 PM'),
                ('15:00', '3:00 PM'),
                ('16:00', '4:00 PM'),
                ('17:00', '5:00 PM'),
                ('18:00', '6:00 PM'),
                ('19:00', '7:00 PM'),
                ('20:00', '8:00 PM'),
                ('21:00', '9:00 PM'),
                ('22:00', '10:00 PM'),
                ('23:00', '11:00 PM'),
                ('00:00', '12:00 AM'),
            ]),
            'is_closed': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
            }),
        }

    def clean(self):
        """Validate operating hours."""
        cleaned_data = super().clean()
        opening = cleaned_data.get('opening')
        closing = cleaned_data.get('closing')
        is_closed = cleaned_data.get('is_closed')

        if not is_closed:
            if not opening or not closing:
                raise ValidationError(_('Opening and closing times are required when not closed.'))
            
            if opening >= closing:
                raise ValidationError(_('Opening time must be before closing time.'))

        return cleaned_data


class OperatingHoursFormSet(forms.BaseFormSet):
    """Formset for managing all 7 days of operating hours."""

    def clean(self):
        """Validate the entire formset."""
        if any(self.errors):
            return

        # Check for duplicate days
        days = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                day = form.cleaned_data.get('day')
                if day in days:
                    raise ValidationError(_('Each day can only have one set of operating hours.'))
                days.append(day)


# Create the formset factory
OperatingHoursFormSetFactory = forms.formset_factory(
    OperatingHoursForm,
    formset=OperatingHoursFormSet,
    extra=7,  # One for each day of the week
    max_num=7,
    validate_max=True,
    can_delete=False
)



