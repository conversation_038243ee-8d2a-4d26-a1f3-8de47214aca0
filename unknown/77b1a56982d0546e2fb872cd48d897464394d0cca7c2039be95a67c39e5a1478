"""Utility functions for discount calculations and tracking."""

# --- Standard Library Imports ---
from decimal import Decimal
import time

# --- Third-Party Imports ---
from django.utils import timezone

# --- Local App Imports ---
from utils.logging_utils import get_app_logger, log_error, log_performance, log_user_activity
from .logging_utils import log_discount_calculation_performance, log_discount_error
from .models import DiscountUsage, PlatformDiscount, ServiceDiscount, VenueDiscount


def get_applicable_discounts(service, user=None):
    """Return applicable discounts ordered by final price."""
    start_time = time.time()
    start_time = time.time()

    try:
        now = timezone.now()
        applicable_discounts = []
        original_price = getattr(service, 'price', None)
        if original_price is None:
            original_price = getattr(service, 'price_min', None)
        if original_price is None:
            raise AttributeError('Service must have price or price_min field')

        # Check for service-specific discounts
        service_discounts = ServiceDiscount.objects.filter(
            service=service,
            is_approved=True,
            start_date__lte=now,
            end_date__gte=now
        )
    
        for discount in service_discounts:
            if getattr(discount, 'usage_limit_reached', lambda: False)():
                continue
            discount_amount = discount.calculate_discount_amount(original_price)
            final_price = discount.calculate_discounted_price(original_price)
            applicable_discounts.append((discount, discount_amount, final_price))

        # Check for venue-wide discounts
        venue_discounts = VenueDiscount.objects.filter(
            venue=service.venue,
            is_approved=True,
            start_date__lte=now,
            end_date__gte=now,
            min_booking_value__lte=original_price
        )

        for discount in venue_discounts:
            if getattr(discount, 'usage_limit_reached', lambda: False)():
                continue
            discount_amount = discount.calculate_discount_amount(original_price)

            # Apply max discount amount if set
            if hasattr(discount, 'max_discount_amount') and discount.max_discount_amount and discount_amount > discount.max_discount_amount:
                discount_amount = discount.max_discount_amount

            final_price = original_price - discount_amount
            applicable_discounts.append((discount, discount_amount, final_price))

        # Check for platform-wide discounts
        platform_discounts = PlatformDiscount.objects.filter(
            start_date__lte=now,
            end_date__gte=now,
            min_booking_value__lte=original_price
        )

        # Filter by category if applicable
        if hasattr(service.venue, 'category') and service.venue.category:
            platform_discounts = platform_discounts.filter(
                category__isnull=True
            ) | platform_discounts.filter(
                category=service.venue.category
            )

        for discount in platform_discounts:
            if getattr(discount, 'usage_limit_reached', lambda: False)():
                continue
            discount_amount = discount.calculate_discount_amount(original_price)

            # Apply max discount amount if set
            if hasattr(discount, 'max_discount_amount') and discount.max_discount_amount and discount_amount > discount.max_discount_amount:
                discount_amount = discount.max_discount_amount

            final_price = original_price - discount_amount
            applicable_discounts.append((discount, discount_amount, final_price))
    
        # Sort by final price (lowest first)
        applicable_discounts.sort(key=lambda x: x[2])

        # Log performance metrics
        duration = time.time() - start_time
        log_discount_calculation_performance(
            operation='get_applicable_discounts',
            duration=duration,
            service_id=service.id,
            discounts_found=len(applicable_discounts),
            user=user
        )

        return applicable_discounts

    except Exception as e:
        duration = time.time() - start_time
        log_discount_error(
            error_type='discount_calculation_error',
            error_message='Failed to calculate applicable discounts',
            user=user,
            exception=e,
            discount_context={
                'service_id': service.id if hasattr(service, 'id') else None,
                'operation_duration': duration,
                'operation': 'get_applicable_discounts'
            }
        )
        return []


def get_best_discount(service, user=None):
    """
    Get the best discount for a service.
    Returns a tuple (discount_object, discount_amount, final_price) or None if no discount applies.
    """
    applicable_discounts = get_applicable_discounts(service, user)
    
    if applicable_discounts:
        return applicable_discounts[0]  # Return the discount with the lowest final price
    
    return None


def record_discount_usage(discount, user, booking, original_price, discount_amount, final_price):
    """
    Record the usage of a discount for analytics and tracking.
    """
    try:
        if isinstance(discount, VenueDiscount):
            discount_type = 'VenueDiscount'
        elif isinstance(discount, ServiceDiscount):
            discount_type = 'ServiceDiscount'
        elif isinstance(discount, PlatformDiscount):
            discount_type = 'PlatformDiscount'
        else:
            raise ValueError("Invalid discount type")

        # Generate booking reference from booking object
        booking_reference = str(booking.id) if booking and hasattr(booking, 'id') else 'test-booking-ref'

        usage = DiscountUsage.objects.create(
            user=user,
            discount_type=discount_type,
            discount_id=discount.id,
            booking_reference=booking_reference,
            original_price=original_price,
            discount_amount=discount_amount,
            final_price=final_price
        )

        # Log successful discount usage
        log_user_activity(
            app_name='discount_app',
            activity_type='discount_usage_recorded',
            user=user,
            details={
                'discount_type': discount_type,
                'discount_id': discount.id,
                'discount_name': discount.name,
                'booking_id': booking.id if booking else None,
                'original_price': float(original_price),
                'discount_amount': float(discount_amount),
                'final_price': float(final_price),
                'savings_percentage': round((float(discount_amount) / float(original_price)) * 100, 2) if original_price > 0 else 0
            }
        )

        return usage

    except Exception as e:
        log_discount_error(
            error_type='discount_usage_recording_error',
            error_message='Failed to record discount usage',
            user=user,
            exception=e,
            discount_context={
                'discount_type': type(discount).__name__,
                'discount_id': discount.id if hasattr(discount, 'id') else None,
                'original_price': float(original_price) if original_price else None,
                'discount_amount': float(discount_amount) if discount_amount else None,
                'final_price': float(final_price) if final_price else None,
                'operation': 'record_discount_usage'
            }
        )
        raise


def calculate_cart_discounts(cart_items, user=None):
    """
    Calculate discounts for all items in a cart.
    Returns a dictionary with discount information for each item.
    """
    cart_discounts = {}
    total_original_price = 0
    total_discounted_price = 0
    total_savings = 0
    
    for item in cart_items:
        service_price = getattr(item.service, 'price', None)
        if service_price is None:
            service_price = getattr(item.service, 'price_min', None)
        if service_price is None:
            raise AttributeError('Service must have price or price_min field')
        original_price = service_price * item.quantity
        best_discount_tuple = get_best_discount(item.service, user)
        
        if best_discount_tuple:
            discount, discount_amount, final_price_per_unit = best_discount_tuple
            final_price = final_price_per_unit * item.quantity
            savings = original_price - final_price
            
            cart_discounts[item.id] = {
                'item': item,
                'original_price': original_price,
                'discounted_price': final_price,
                'savings': savings,
                'discount': discount,
                'has_discount': True,
            }
        else:
            cart_discounts[item.id] = {
                'item': item,
                'original_price': original_price,
                'discounted_price': original_price,
                'savings': 0,
                'discount': None,
                'has_discount': False,
            }
        
        total_original_price += cart_discounts[item.id]['original_price']
        total_discounted_price += cart_discounts[item.id]['discounted_price']
        total_savings += cart_discounts[item.id]['savings']
    
    return {
        'items': cart_discounts,
        'total_original_price': total_original_price,
        'total_discounted_price': total_discounted_price,
        'total_savings': total_savings,
    }


def get_venue_discount_summary(venue):
    """
    Get a summary of all active discounts for a venue.
    Returns counts of different discount types.
    """
    now = timezone.now()
    
    # Count service discounts
    service_discounts_count = ServiceDiscount.objects.filter(
        service__venue=venue,
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True,
        service__is_active=True
    ).count()
    
    # Count venue discounts
    venue_discounts_count = VenueDiscount.objects.filter(
        venue=venue,
        start_date__lte=now,
        end_date__gte=now,
        is_approved=True
    ).count()
    
    # Count platform discounts
    platform_discounts_count = PlatformDiscount.objects.filter(
        start_date__lte=now,
        end_date__gte=now
    ).count()
    
    total_discounts = service_discounts_count + venue_discounts_count + platform_discounts_count
    
    return {
        'service_discounts_count': service_discounts_count,
        'venue_discounts_count': venue_discounts_count,
        'platform_discounts_count': platform_discounts_count,
        'total_discounts': total_discounts,
        'has_discounts': total_discounts > 0,
    }


def format_discount_display(discount):
    """Format discount for display purposes."""

    if not discount:
        return ""

    # Format to one decimal place
    value = discount.discount_value.quantize(Decimal('0.0'))

    if discount.discount_type == 'percentage':
        return f"{value}% off"
    else:
        return f"${value} off"


def validate_discount_eligibility(discount, service, user=None):
    """
    Validate if a discount is eligible for a specific service and user.
    Returns (is_eligible, reason) tuple.
    """
    try:
        now = timezone.now()

        # Check if discount is active
        if discount.start_date > now:
            reason = "Discount has not started yet"
            log_user_activity(
                app_name='discount_app',
                activity_type='discount_eligibility_check_failed',
                user=user,
                details={
                    'discount_id': discount.id,
                    'discount_name': discount.name,
                    'service_id': service.id,
                    'reason': reason,
                    'start_date': discount.start_date.isoformat(),
                    'current_time': now.isoformat()
                }
            )
            return False, reason

        if discount.end_date < now:
            reason = "Discount has expired"
            log_user_activity(
                app_name='discount_app',
                activity_type='discount_eligibility_check_failed',
                user=user,
                details={
                    'discount_id': discount.id,
                    'discount_name': discount.name,
                    'service_id': service.id,
                    'reason': reason,
                    'end_date': discount.end_date.isoformat(),
                    'current_time': now.isoformat()
                }
            )
            return False, reason

        if not discount.is_approved:
            reason = "Discount is not approved"
            log_user_activity(
                app_name='discount_app',
                activity_type='discount_eligibility_check_failed',
                user=user,
                details={
                    'discount_id': discount.id,
                    'discount_name': discount.name,
                    'service_id': service.id,
                    'reason': reason,
                    'is_approved': discount.is_approved
                }
            )
            return False, reason

        # Check minimum booking value for venue and platform discounts
        if isinstance(discount, (VenueDiscount, PlatformDiscount)):
            service_price = getattr(service, 'price', getattr(service, 'price_min', None))
            if hasattr(discount, 'min_booking_value') and service_price is not None and discount.min_booking_value > service_price:
                reason = f"Minimum booking value of ${discount.min_booking_value} not met"
                log_user_activity(
                    app_name='discount_app',
                    activity_type='discount_eligibility_check_failed',
                    user=user,
                    details={
                        'discount_id': discount.id,
                        'discount_name': discount.name,
                        'service_id': service.id,
                        'reason': reason,
                        'min_booking_value': float(discount.min_booking_value),
                        'service_price': float(service_price) if service_price is not None else None
                    }
                )
                return False, reason

        # Check category restrictions for platform discounts
        if isinstance(discount, PlatformDiscount):
            if hasattr(discount, 'category') and discount.category and discount.category != service.venue.category:
                reason = "Discount not applicable to this venue category"
                log_user_activity(
                    app_name='discount_app',
                    activity_type='discount_eligibility_check_failed',
                    user=user,
                    details={
                        'discount_id': discount.id,
                        'discount_name': discount.name,
                        'service_id': service.id,
                        'reason': reason,
                        'discount_category': discount.category.name if discount.category else None,
                        'venue_category': service.venue.category.name if hasattr(service.venue, 'category') and service.venue.category else None
                    }
                )
                return False, reason

        # Log successful eligibility check
        log_user_activity(
            app_name='discount_app',
            activity_type='discount_eligibility_check_passed',
            user=user,
            details={
                'discount_id': discount.id,
                'discount_name': discount.name,
                'service_id': service.id,
                'service_name': service.service_title,
                'discount_type': type(discount).__name__
            }
        )

        return True, "Eligible"

    except Exception as e:
        log_discount_error(
            error_type='discount_eligibility_validation_error',
            error_message='Failed to validate discount eligibility',
            user=user,
            exception=e,
            discount_context={
                'discount_id': discount.id if hasattr(discount, 'id') else None,
                'service_id': service.id if hasattr(service, 'id') else None,
                'operation': 'validate_discount_eligibility'
            }
        )
        return False, "Validation error occurred"
