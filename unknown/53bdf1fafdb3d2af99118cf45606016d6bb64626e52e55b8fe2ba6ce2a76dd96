from .common import *
# ===== PROVIDER VIEWS =====


@login_required
def provider_availability_list_view(request):
    """
    Display all services and their availability for the service provider.
    Shows overview of availability management for all provider's services.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_provider_access',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_view': 'provider_availability_list'}
            )
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    try:
        # Log user activity
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'provider_availability_list_viewed',
                user=request.user,
                request=request,
                details={'action': 'view_availability_list'}
            )

        # Get all services for the provider's venues
        from venues_app.models import Service, Venue
        provider_profile = request.user.service_provider_profile
        services = Service.objects.filter(
            venue__service_provider=provider_profile,
            venue__visibility=Venue.ACTIVE,
            is_active=True
        ).select_related('venue').prefetch_related('availability_slots')

        # Add availability stats for each service
        services_with_stats = []
        for service in services:
            total_slots = service.availability_slots.count()
            available_slots = service.availability_slots.filter(is_available=True).count()
            future_slots = service.availability_slots.filter(
                available_date__gte=timezone.now().date()
            ).count()

            services_with_stats.append({
                'service': service,
                'total_slots': total_slots,
                'available_slots': available_slots,
                'future_slots': future_slots,
            })

        context = {
            'services_with_stats': services_with_stats,
        }

        return render(request, 'booking_cart_app/provider/availability_list.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'provider_availability_list_error',
                f"Error loading availability list: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred while loading your availability. Please try again.")
        return redirect('home')


@login_required
def provider_service_availability_view(request, service_id):
    """
    Display availability slots for a specific service.
    Shows all time slots with booking status and management options.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the service
    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    # Get availability slots for the service
    availability_slots = ServiceAvailability.objects.filter(
        service=service
    ).order_by('available_date', 'start_time')

    # Filter by date range if provided
    date_filter = request.GET.get('date_filter', 'upcoming')
    today = timezone.now().date()

    if date_filter == 'upcoming':
        availability_slots = availability_slots.filter(available_date__gte=today)
    elif date_filter == 'past':
        availability_slots = availability_slots.filter(available_date__lt=today)
    elif date_filter == 'today':
        availability_slots = availability_slots.filter(available_date=today)

    # Pagination
    paginator = Paginator(availability_slots, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'service': service,
        'availability_slots': page_obj,
        'date_filter': date_filter,
    }

    return render(request, 'booking_cart_app/provider/service_availability.html', context)


@login_required
def provider_availability_calendar_view(request, service_id):
    """Calendar view of availability for easier overview."""
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    slots = ServiceAvailability.objects.filter(service=service).order_by('available_date', 'start_time')

    calendar = {}
    for slot in slots:
        calendar.setdefault(slot.available_date, []).append(slot)

    context = {
        'service': service,
        'calendar': calendar,
    }

    return render(request, 'booking_cart_app/provider/availability_calendar.html', context)


@login_required
def provider_add_availability_view(request, service_id):
    """
    Add a new availability slot for a service.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the service
    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    if request.method == 'POST':
        form = ServiceAvailabilityForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    availability = form.save(commit=False)
                    availability.service = service
                    availability.save()

                    messages.success(request, "Availability slot added successfully.")
                    return redirect('booking_cart_app:provider_service_availability', service_id=service.id)

            except Exception as e:
                messages.error(request, f"Error adding availability slot: {str(e)}")
    else:
        form = ServiceAvailabilityForm()

    context = {
        'form': form,
        'service': service,
    }

    return render(request, 'booking_cart_app/provider/add_availability.html', context)


@login_required
def provider_bulk_availability_view(request, service_id):
    """
    Bulk add availability slots for a service over a date range.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the service
    from venues_app.models import Service
    provider_profile = request.user.service_provider_profile
    service = get_object_or_404(
        Service,
        id=service_id,
        venue__service_provider=provider_profile,
        is_active=True
    )

    if request.method == 'POST':
        form = DateRangeAvailabilityForm(request.POST)
        if form.is_valid():
            try:
                with transaction.atomic():
                    start_date = form.cleaned_data['start_date']
                    end_date = form.cleaned_data['end_date']
                    start_time = form.cleaned_data['start_time']
                    end_time = form.cleaned_data['end_time']
                    interval = form.cleaned_data['interval']
                    max_bookings = form.cleaned_data['max_bookings']
                    is_available = form.cleaned_data['is_available']

                    # Generate time slots
                    from datetime import datetime, timedelta
                    current_date = start_date
                    slots_created = 0

                    while current_date <= end_date:
                        current_time = datetime.combine(current_date, start_time)
                        end_datetime = datetime.combine(current_date, end_time)

                        while current_time < end_datetime:
                            slot_end_time = current_time + timedelta(minutes=interval)
                            if slot_end_time > end_datetime:
                                break

                            # Check if slot already exists
                            existing_slot = ServiceAvailability.objects.filter(
                                service=service,
                                available_date=current_date,
                                start_time=current_time.time()
                            ).first()

                            if not existing_slot:
                                ServiceAvailability.objects.create(
                                    service=service,
                                    available_date=current_date,
                                    start_time=current_time.time(),
                                    end_time=slot_end_time.time(),
                                    max_bookings=max_bookings,
                                    is_available=is_available
                                )
                                slots_created += 1

                            current_time = slot_end_time

                        current_date += timedelta(days=1)

                    messages.success(request, f"Successfully created {slots_created} availability slots.")
                    return redirect('booking_cart_app:provider_service_availability', service_id=service.id)

            except Exception as e:
                messages.error(request, f"Error creating availability slots: {str(e)}")
    else:
        form = DateRangeAvailabilityForm()

    context = {
        'form': form,
        'service': service,
    }

    return render(request, 'booking_cart_app/provider/bulk_availability.html', context)


@login_required
def provider_edit_availability_view(request, availability_id):
    """
    Edit an existing availability slot.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the availability slot
    provider_profile = request.user.service_provider_profile
    availability = get_object_or_404(
        ServiceAvailability,
        id=availability_id,
        service__venue__service_provider=provider_profile
    )

    if request.method == 'POST':
        form = ServiceAvailabilityForm(request.POST, instance=availability)
        if form.is_valid():
            try:
                with transaction.atomic():
                    form.save()
                    messages.success(request, "Availability slot updated successfully.")
                    return redirect('booking_cart_app:provider_service_availability', service_id=availability.service.id)

            except Exception as e:
                messages.error(request, f"Error updating availability slot: {str(e)}")
    else:
        form = ServiceAvailabilityForm(instance=availability)

    context = {
        'form': form,
        'availability': availability,
        'service': availability.service,
    }

    return render(request, 'booking_cart_app/provider/edit_availability.html', context)


@login_required
@require_http_methods(["POST"])
def provider_delete_availability_view(request, availability_id):
    """
    Delete an availability slot.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the availability slot
    provider_profile = request.user.service_provider_profile
    availability = get_object_or_404(
        ServiceAvailability,
        id=availability_id,
        service__venue__service_provider=provider_profile
    )

    service_id = availability.service.id

    try:
        with transaction.atomic():
            # Check if there are any bookings for this slot
            if availability.current_bookings > 0:
                messages.error(request, "Cannot delete availability slot with existing bookings.")
                return redirect('booking_cart_app:provider_service_availability', service_id=service_id)

            availability.delete()
            messages.success(request, "Availability slot deleted successfully.")

    except Exception as e:
        messages.error(request, f"Error deleting availability slot: {str(e)}")

    return redirect('booking_cart_app:provider_service_availability', service_id=service_id)


@login_required
def provider_booking_list_view(request):
    """
    Display all bookings for the service provider's venues.
    Shows pending, confirmed, and recent bookings with filtering options.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get filter parameters
    status_filter = request.GET.get('status', 'all')
    date_filter = request.GET.get('date_filter', 'upcoming')

    # Get all bookings for the provider's venues
    provider_profile = request.user.service_provider_profile
    bookings = (
        Booking.objects.filter(venue__service_provider=provider_profile)
        .prefetch_related('items__service', 'customer', 'venue')
    )

    # Apply status filter
    if status_filter != 'all':
        bookings = bookings.filter(status=status_filter)

    # Apply date filter
    today = timezone.now().date()
    if date_filter == 'upcoming':
        bookings = bookings.filter(
            items__scheduled_date__gte=today
        ).distinct()
    elif date_filter == 'past':
        bookings = bookings.filter(
            items__scheduled_date__lt=today
        ).distinct()
    elif date_filter == 'today':
        bookings = bookings.filter(
            items__scheduled_date=today
        ).distinct()

    # Order by booking date (newest first)
    bookings = bookings.order_by('-booking_date')

    # Separate bookings by status for quick stats
    pending_count = bookings.filter(status=Booking.PENDING).count()
    confirmed_count = bookings.filter(status=Booking.CONFIRMED).count()
    total_count = bookings.count()

    # Pagination
    paginator = Paginator(bookings, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'bookings': page_obj,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'status_choices': Booking.STATUS_CHOICES,
        'pending_count': pending_count,
        'confirmed_count': confirmed_count,
        'total_count': total_count,
    }

    return render(request, 'booking_cart_app/provider/booking_list.html', context)


@login_required
def provider_booking_detail_view(request, booking_slug):
    """
    Display detailed information about a specific booking for the provider.
    Shows customer information, booking items, and action options.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Get all booking items
    booking_items = booking.items.all().order_by('scheduled_date', 'scheduled_time')

    # Check if booking can be accepted or declined
    can_accept = booking.status == Booking.PENDING
    can_decline = booking.status == Booking.PENDING

    context = {
        'booking': booking,
        'booking_items': booking_items,
        'can_accept': can_accept,
        'can_decline': can_decline,
    }

    return render(request, 'booking_cart_app/provider/booking_detail.html', context)


@login_required
def provider_accept_booking_view(request, booking_slug):
    """
    Accept a pending booking.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Check if booking can be accepted
    if booking.status != Booking.PENDING:
        messages.error(request, "Only pending bookings can be accepted.")
        return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

    if request.method == 'POST':
        form = BookingActionForm(request.POST)
        if form.is_valid():
            action_reason = form.cleaned_data.get('action_reason', '')

            try:
                with transaction.atomic():
                    # Accept the booking
                    booking.confirm_booking()

                    # Update availability slots
                    for item in booking.items.all():
                        try:
                            availability = ServiceAvailability.objects.get(
                                service=item.service,
                                available_date=item.scheduled_date,
                                start_time=item.scheduled_time
                            )
                            availability.book_slot()
                        except ServiceAvailability.DoesNotExist:
                            pass  # Availability record doesn't exist, skip

                    # Log booking acceptance
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_accepted',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'customer_email': booking.customer.email,
                                'total_price': str(booking.total_price),
                                'action_reason': action_reason
                            }
                        )

                    # Send notification
                    if NOTIFICATIONS_ENABLED:
                        # notify_booking_accepted(booking)  # This would be implemented in notifications_app
                        pass

                    messages.success(request, "Booking accepted successfully.")
                    return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_accept_error',
                        f"Error accepting booking {booking.booking_id}: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'booking_id': str(booking.booking_id)}
                    )
                messages.error(request, f"Error accepting booking: {str(e)}")
                return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)
    else:
        form = BookingActionForm()

    context = {
        'form': form,
        'booking': booking,
        'action': 'accept',
    }

    return render(request, 'booking_cart_app/provider/booking_action.html', context)


@login_required
def provider_decline_booking_view(request, booking_slug):
    """
    Decline a pending booking.
    """
    # Check if user is a service provider
    if not hasattr(request.user, 'is_service_provider') or not request.user.is_service_provider:
        messages.error(request, "Only service providers can access this page.")
        return redirect('home')

    # Get the booking
    provider_profile = request.user.service_provider_profile
    booking = get_object_or_404(
        Booking,
        slug=booking_slug,
        venue__service_provider=provider_profile
    )

    # Check if booking can be declined
    if booking.status != Booking.PENDING:
        messages.error(request, "Only pending bookings can be declined.")
        return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

    if request.method == 'POST':
        form = BookingActionForm(request.POST)
        if form.is_valid():
            action_reason = form.cleaned_data.get('action_reason', '')

            try:
                with transaction.atomic():
                    # Decline the booking
                    booking.decline_booking(action_reason)

                    # Log booking decline
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_declined',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'customer_email': booking.customer.email,
                                'total_price': str(booking.total_price),
                                'action_reason': action_reason
                            }
                        )

                    # Send notification
                    if NOTIFICATIONS_ENABLED:
                        # notify_booking_declined(booking)  # This would be implemented in notifications_app
                        pass

                    messages.success(request, "Booking declined successfully.")
                    return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_decline_error',
                        f"Error declining booking {booking.booking_id}: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'booking_id': str(booking.booking_id)}
                    )
                messages.error(request, f"Error declining booking: {str(e)}")
                return redirect('booking_cart_app:provider_booking_detail', booking_slug=booking_slug)
    else:
        form = BookingActionForm()

    context = {
        'form': form,
        'booking': booking,
        'action': 'decline',
    }

    return render(request, 'booking_cart_app/provider/booking_action.html', context)


