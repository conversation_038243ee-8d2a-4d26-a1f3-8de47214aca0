{% extends 'admin_app/base.html' %}

{% block title %}User Detail{% endblock %}

{% block admin_content %}
<h1 class="h4 mb-4">User Detail</h1>
<div class="mb-4">
    <h2 class="h6">{{ user_obj.first_name }} {{ user_obj.last_name }}</h2>
    <p class="mb-0"><strong>Email:</strong> {{ user_obj.email }}</p>
    <p class="mb-0"><strong>Role:</strong> {{ user_obj.get_role_display }}</p>
    <p class="mb-0"><strong>Status:</strong> {% if user_obj.is_active %}Active{% else %}Inactive{% endif %}</p>
    <p class="mb-0"><strong>Joined:</strong> {{ user_obj.date_joined }}</p>
    <p class="mb-0"><strong>Last Login:</strong> {{ user_obj.last_login|default:"Never" }}</p>
</div>
{% if customer_profile %}
<div class="mb-4">
    <h3 class="h6">Customer Profile</h3>
    <p class="mb-0"><strong>Phone:</strong> {{ customer_profile.phone_number }}</p>
    <p class="mb-0"><strong>Address:</strong> {{ customer_profile.default_address }}</p>
    <p class="mb-0"><strong>City:</strong> {{ customer_profile.city }}</p>
</div>
{% endif %}
{% if provider_profile %}
<div class="mb-4">
    <h3 class="h6">Service Provider Profile</h3>
    <p class="mb-0"><strong>Business Name:</strong> {{ provider_profile.business_name }}</p>
    <p class="mb-0"><strong>Business Phone:</strong> {{ provider_profile.business_phone_number }}</p>
    <p class="mb-0"><strong>Business Address:</strong> {{ provider_profile.business_address }}</p>
</div>
{% endif %}
{% if venues %}
<div class="mb-4">
    <h3 class="h6">Venues</h3>
    {% for venue in venues %}
    <div class="border-bottom pb-2 mb-2">
        <h4 class="h6">{{ venue.venue_name }}</h4>
        <p class="mb-0">{{ venue.short_description }}</p>
        <p class="mb-0"><strong>Status:</strong> {{ venue.get_approval_status_display }}</p>
    </div>
    {% endfor %}
</div>
{% endif %}
{% if recent_bookings %}
<div class="mb-4">
    <h3 class="h6">Recent Bookings</h3>
    {% for booking in recent_bookings %}
    <div class="border-bottom pb-2 mb-2">
        <p class="mb-0"><strong>Booking ID:</strong> {{ booking.booking_id }}</p>
        <p class="mb-0"><strong>Status:</strong> {{ booking.get_status_display }}</p>
        <p class="mb-0"><strong>Date:</strong> {{ booking.booking_date }}</p>
        <p class="mb-0"><strong>Total:</strong> ${{ booking.total_price }}</p>
    </div>
    {% endfor %}
</div>
{% endif %}
<div class="mt-4">
    <a href="{% url 'admin_app:user_list' %}">Back to User List</a>
</div>
{% endblock %}
