{% extends 'base.html' %}

{% block title %}All Notifications - Admin{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}">
                            <i class="fas fa-bell me-1"></i>Notification Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">All Notifications</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h2 mb-0">All Notifications</h1>
                <div class="d-flex gap-2">
                    <a href="{% url 'notifications_app:admin_create_announcement' %}" class="btn btn-primary">
                        <i class="fas fa-bullhorn me-2"></i>Create Announcement
                    </a>
                    <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Notifications</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Search notifications..." 
                                           value="{{ search_query|default:'' }}">
                                    <button class="btn btn-outline-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select name="notification_type" class="form-select">
                                    <option value="">All Types</option>
                                    <option value="booking" {% if request.GET.notification_type == 'booking' %}selected{% endif %}>Booking</option>
                                    <option value="payment" {% if request.GET.notification_type == 'payment' %}selected{% endif %}>Payment</option>
                                    <option value="review" {% if request.GET.notification_type == 'review' %}selected{% endif %}>Review</option>
                                    <option value="announcement" {% if request.GET.notification_type == 'announcement' %}selected{% endif %}>Announcement</option>
                                    <option value="system" {% if request.GET.notification_type == 'system' %}selected{% endif %}>System</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="read_status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="unread" {% if request.GET.read_status == 'unread' %}selected{% endif %}>Unread Only</option>
                                    <option value="read" {% if request.GET.read_status == 'read' %}selected{% endif %}>Read Only</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" name="date_from" class="form-control" 
                                       value="{{ request.GET.date_from|default:'' }}" 
                                       placeholder="From Date">
                            </div>
                            <div class="col-md-2">
                                <input type="date" name="date_to" class="form-control" 
                                       value="{{ request.GET.date_to|default:'' }}" 
                                       placeholder="To Date">
                            </div>
                            <div class="col-md-1">
                                <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-redo"></i>
                                </a>
                            </div>
                        </div>
                    </form>

                    {% if notifications %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-muted">
                                Showing {{ notifications.start_index }}-{{ notifications.end_index }} of {{ total_notifications }} notifications
                            </span>
                        </div>

                        <!-- Notifications Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead style="background-color: black; color: white;">
                                    <tr>
                                        <th style="color: white;">User</th>
                                        <th style="color: white;">Type</th>
                                        <th style="color: white;">Title</th>
                                        <th style="color: white;">Status</th>
                                        <th style="color: white;">Created</th>
                                        <th style="color: white;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for notification in notifications %}
                                        <tr class="{% if notification.read_status == 'unread' %}border-start border-3{% endif %}" style="{% if notification.read_status == 'unread' %}background-color: white; border-left-color: black !important;{% endif %}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <div class="fw-bold">{{ notification.user.get_full_name|default:notification.user.email }}</div>
                                                        <small style="color: black; opacity: 0.6;">{{ notification.user.email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge" style="background-color: white; color: black; border: 2px solid black;">{{ notification.get_notification_type_display }}</span>
                                            </td>
                                            <td>
                                                <div>
                                                    {% if notification.read_status == 'unread' %}
                                                        <span class="badge bg-primary me-1">New</span>
                                                    {% endif %}
                                                    {{ notification.title|truncatechars:50 }}
                                                </div>
                                                <small class="text-muted">{{ notification.message|truncatechars:80 }}</small>
                                            </td>
                                            <td>
                                                {% if notification.read_status == 'read' %}
                                                    <span class="badge bg-success">Read</span>
                                                    {% if notification.read_at %}
                                                        <br><small class="text-muted">{{ notification.read_at|date:"M d, g:i a" }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-warning text-dark">Unread</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div>{{ notification.created_at|date:"M d, Y" }}</div>
                                                <small class="text-muted">{{ notification.created_at|date:"g:i a" }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'notifications_app:admin_notification_detail' notification.id %}" 
                                                       class="btn btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if notifications.has_other_pages %}
                            <nav aria-label="Notification pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if notifications.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ notifications.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for i in notifications.paginator.page_range %}
                                        {% if notifications.number == i %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ i }}</span>
                                            </li>
                                        {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if notifications.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ notifications.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ notifications.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}

                    {% else %}
                        <!-- Empty State -->
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-bell-slash fa-4x text-muted"></i>
                            </div>
                            <h4 class="text-muted">No Notifications Found</h4>
                            <p class="text-muted">No notifications match your current filters.</p>
                            <a href="{% url 'notifications_app:admin_notification_list' %}" class="btn btn-outline-primary">
                                <i class="fas fa-redo me-1"></i>Clear Filters
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit filter form when select changes
    const filterSelects = document.querySelectorAll('select[name="notification_type"], select[name="read_status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Auto-hide success messages after 3 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
{% endblock %}
