"""Customer dashboard views."""

# --- Standard Library Imports ---
import csv
from datetime import date, timedelta
from decimal import Decimal

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Avg, Count, Q, Sum
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views import View
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView


# --- Local App Imports ---
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)
from booking_cart_app.models import Booking, BookingItem
from venues_app.models import Service, Venue
from ..forms import DateRangeForm
from ..helpers import get_date_range, get_int_param, get_valid_param
from ..logging_utils import (
    log_admin_dashboard_access,
    log_admin_data_access,
    log_analytics_access,
    log_booking_status_access,
    log_dashboard_access,
    log_dashboard_activity,
    log_dashboard_filter_usage,
    log_error,
    log_favorite_venue_event,
    log_profile_access,
    log_provider_dashboard_activity,
    log_provider_earnings_access,
    log_provider_service_stats_access,
    log_security_event,
    log_system_health_check,
    performance_monitor,
)
from ..decorators import customer_required, provider_required, staff_required
from ..mixins import ProviderRequiredMixin
from ..models import FavoriteVenue
from ..constants import (
    CUSTOMER_ONLY_ERROR,
    PROVIDER_ONLY_ERROR,
    ADMIN_ONLY_ERROR,
    FAVORITE_ADDED_SUCCESS,
    FAVORITE_REMOVED_SUCCESS,
    FAVORITE_ALREADY_EXISTS,
    DASHBOARD_ACCESS_SUCCESS,
    VENUE_NOT_FOUND_ERROR,
    PERMISSION_DENIED_ERROR,
)


# --- Customer Dashboard Views ---
@customer_required
@performance_monitor("customer_dashboard_access")
def customer_dashboard_view(request):
    """
    Main customer dashboard view showing overview of bookings, favorites, and profile.
    """
    # Role check handled by customer_required decorator

    try:
        # Get customer profile
        customer_profile, created = CustomerProfile.objects.get_or_create(user=request.user)

        # Get recent bookings (last 5)
        recent_bookings = Booking.objects.filter(
            customer=request.user
        ).order_by('-booking_date')[:5]

        # Get upcoming bookings
        today = timezone.now().date()
        upcoming_bookings = Booking.objects.filter(
            customer=request.user,
            status__in=['pending', 'confirmed']
        ).order_by('booking_date')[:5]

        # Get favorite venues count
        favorite_venues_count = FavoriteVenue.objects.filter(customer=request.user).count()

        # Get booking statistics
        total_bookings = Booking.objects.filter(customer=request.user).count()
        pending_bookings = Booking.objects.filter(
            customer=request.user,
            status='pending'
        ).count()
        confirmed_bookings = Booking.objects.filter(
            customer=request.user,
            status='confirmed'
        ).count()

        # Log dashboard access with enhanced context
        log_dashboard_access(
            user=request.user,
            dashboard_type='customer',
            request=request,
            details={
                'total_bookings': total_bookings,
                'favorite_venues_count': favorite_venues_count,
                'pending_bookings': pending_bookings,
                'confirmed_bookings': confirmed_bookings,
                'profile_created': created
            }
        )

        context = {
            'customer_profile': customer_profile,
            'recent_bookings': recent_bookings,
            'upcoming_bookings': upcoming_bookings,
            'favorite_venues_count': favorite_venues_count,
            'total_bookings': total_bookings,
            'pending_bookings': pending_bookings,
            'confirmed_bookings': confirmed_bookings,
        }

        return render(request, 'dashboard_app/customer/dashboard.html', context)

    except Exception as e:
        log_error(
            error_type='dashboard_access',
            error_message="Error loading customer dashboard",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading your dashboard. Please try again.')
        return redirect('venues_app:home')


@customer_required
@performance_monitor("customer_booking_status_access")
def customer_booking_status_view(request):
    """
    Customer booking status view showing all bookings with filtering options.
    """
    # Role check handled by customer_required decorator

    try:
        # Get filter parameters with validation
        status_filter = get_valid_param(
            request,
            'status',
            {'all', 'pending', 'confirmed', 'completed', 'cancelled'},
            'all'
        )
        date_filter = get_valid_param(
            request,
            'date',
            {'all', 'upcoming', 'past'},
            'all'
        )

        # Base queryset
        bookings = Booking.objects.filter(customer=request.user)

        # Apply status filter
        if status_filter != 'all':
            bookings = bookings.filter(status=status_filter)

        # Apply date filter
        today = timezone.now().date()
        if date_filter == 'upcoming':
            bookings = bookings.filter(booking_date__gte=today)
        elif date_filter == 'past':
            bookings = bookings.filter(booking_date__lt=today)

        # Order by booking date (most recent first)
        bookings = bookings.order_by('-booking_date')

        # Pagination
        paginator = Paginator(bookings, 10)  # Show 10 bookings per page
        page_number = get_int_param(request, 'page', 1)
        page_obj = paginator.get_page(page_number)

        # Get booking counts for filter display
        total_count = Booking.objects.filter(customer=request.user).count()
        pending_count = Booking.objects.filter(customer=request.user, status='pending').count()
        confirmed_count = Booking.objects.filter(customer=request.user, status='confirmed').count()
        completed_count = Booking.objects.filter(customer=request.user, status='completed').count()
        cancelled_count = Booking.objects.filter(customer=request.user, status='cancelled').count()

        # Log booking status access with filter usage
        log_booking_status_access(
            user=request.user,
            booking_count=bookings.count(),
            filter_type=f"{status_filter}_{date_filter}",
            request=request
        )

        # Log filter usage for analytics
        if status_filter != 'all' or date_filter != 'all':
            log_dashboard_filter_usage(
                user=request.user,
                dashboard_section='customer_booking_status',
                filters_applied={
                    'status_filter': status_filter,
                    'date_filter': date_filter
                },
                results_count=bookings.count(),
                request=request
            )

        context = {
            'page_obj': page_obj,
            'bookings': page_obj.object_list,
            'status_filter': status_filter,
            'date_filter': date_filter,
            'total_count': total_count,
            'pending_count': pending_count,
            'confirmed_count': confirmed_count,
            'completed_count': completed_count,
            'cancelled_count': cancelled_count,
        }

        return render(request, 'dashboard_app/customer/booking_status.html', context)

    except Exception as e:
        log_error(
            error_type='booking_status_access',
            error_message="Error loading customer booking status",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading your bookings. Please try again.')
        return redirect('dashboard_app:customer_dashboard')


@customer_required
@performance_monitor("customer_profile_edit_redirect")
def customer_profile_edit_view(request):
    """
    Customer profile edit view - redirects to accounts_app profile edit.
    """
    # Role check handled by customer_required decorator

    # Log profile access
    log_profile_access(
        user=request.user,
        access_type='edit_redirect',
        request=request
    )

    # Redirect to accounts_app profile edit
    return redirect('accounts_app:customer_profile_edit')


@customer_required
@performance_monitor("customer_favorite_venues_access")
def customer_favorite_venues_view(request):
    """
    Customer favorite venues view showing all favorited venues.
    """
    # Role check handled by customer_required decorator

    try:
        # Get favorite venues with related venue data
        favorite_venues = FavoriteVenue.objects.filter(
            customer=request.user
        ).select_related('venue').order_by('-added_date')

        # Pagination
        paginator = Paginator(favorite_venues, 12)  # Show 12 venues per page
        page_number = get_int_param(request, 'page', 1)
        page_obj = paginator.get_page(page_number)

        # Log favorites access
        log_dashboard_activity(
            activity_type='favorite_venues_access',
            user=request.user,
            request=request,
            details={
                'favorite_count': favorite_venues.count()
            }
        )

        context = {
            'page_obj': page_obj,
            'favorite_venues': page_obj.object_list,
            'total_favorites': favorite_venues.count(),
        }

        return render(request, 'dashboard_app/customer/favorite_venues.html', context)

    except Exception as e:
        log_error(
            error_type='favorite_venues_access',
            error_message="Error loading customer favorite venues",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, 'There was an error loading your favorite venues. Please try again.')
        return redirect('dashboard_app:customer_dashboard')


@customer_required
@require_http_methods(["POST"])
@performance_monitor("add_favorite_venue")
def add_favorite_venue_view(request, venue_id):
    """
    Add a venue to customer's favorites via AJAX.
    """
    # Role check handled by customer_required decorator

    try:
        # Get the venue
        try:
            venue = Venue.objects.get(id=venue_id, approval_status='approved', visibility='active')
        except Venue.DoesNotExist:
            log_error(
                error_type='venue_not_found',
                error_message=f"Venue with id {venue_id} not found for favorite add",
                user=request.user,
                request=request,
                details={'venue_id': venue_id}
            )
            return JsonResponse({
                'success': False,
                'message': VENUE_NOT_FOUND_ERROR
            }, status=404)

        # Check if already favorited with locking
        with transaction.atomic():
            favorite, created = FavoriteVenue.objects.select_for_update().get_or_create(
                customer=request.user,
                venue=venue
            )

        if created:
            # Log favorite added
            log_favorite_venue_event(
                event_type='added',
                user=request.user,
                venue_name=venue.venue_name,
                venue_id=venue.id,
                request=request
            )

            return JsonResponse({
                'success': True,
                'message': FAVORITE_ADDED_SUCCESS,
                'action': 'added'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': FAVORITE_ALREADY_EXISTS,
                'action': 'exists'
            })

    except Exception as e:
        log_error(
            error_type='favorite_add',
            error_message="Error adding venue to favorites",
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id}
        )
        return JsonResponse({
            'success': False,
            'message': 'There was an error adding the venue to your favorites. Please try again.'
        }, status=500)


@customer_required
@require_http_methods(["POST"])
@performance_monitor("remove_favorite_venue")
def remove_favorite_venue_view(request, venue_id):
    """
    Remove a venue from customer's favorites via AJAX.
    """
    # Role check handled by customer_required decorator

    try:
        # Get the venue
        try:
            venue = Venue.objects.get(id=venue_id)
        except Venue.DoesNotExist:
            log_error(
                error_type='venue_not_found',
                error_message=f"Venue with id {venue_id} not found for favorite remove",
                user=request.user,
                request=request,
                details={'venue_id': venue_id}
            )
            return JsonResponse({
                'success': False,
                'message': VENUE_NOT_FOUND_ERROR
            }, status=404)

        # Try to remove from favorites
        try:
            with transaction.atomic():
                favorite = FavoriteVenue.objects.select_for_update().get(
                    customer=request.user,
                    venue=venue
                )
                favorite.delete()

            # Log favorite removed
            log_favorite_venue_event(
                event_type='removed',
                user=request.user,
                venue_name=venue.venue_name,
                venue_id=venue.id,
                request=request
            )

            return JsonResponse({
                'success': True,
                'message': FAVORITE_REMOVED_SUCCESS,
                'action': 'removed'
            })

        except FavoriteVenue.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'This venue is not in your favorites.',
                'action': 'not_found'
            })

    except Exception as e:
        log_error(
            error_type='favorite_remove',
            error_message="Error removing venue from favorites",
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id}
        )
        return JsonResponse({
            'success': False,
            'message': 'There was an error removing the venue from your favorites. Please try again.'
        }, status=500)


@customer_required
@performance_monitor("check_favorite_status")
def check_favorite_status_view(request, venue_id):
    """
    Check if a venue is in customer's favorites (AJAX endpoint).
    """
    # Role check handled by customer_required decorator

    try:
        # Check if venue exists and is favorited
        try:
            venue = Venue.objects.get(id=venue_id)
        except Venue.DoesNotExist:
            return JsonResponse({
                'success': False,
                'is_favorite': False,
                'message': VENUE_NOT_FOUND_ERROR
            }, status=404)
        is_favorite = FavoriteVenue.objects.filter(
            customer=request.user,
            venue=venue
        ).exists()

        return JsonResponse({
            'success': True,
            'is_favorite': is_favorite,
            'venue_id': venue_id
        })

    except Exception as e:
        log_error(
            error_type='favorite_status_check',
            error_message="Error checking favorite status",
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id}
        )
        return JsonResponse({
            'success': False,
            'is_favorite': False,
            'message': 'There was an error checking the favorite status.'
        }, status=500)


