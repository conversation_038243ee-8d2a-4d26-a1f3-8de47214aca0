{% extends 'venues_app/base_venues.html' %}

{% block title %}Manage Venue Images - CozyWish{% endblock %}

{% block venues_content %}
    <!-- Header -->
    <div class="venues-header d-flex align-items-center mb-4">
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h2>Manage Venue Images</h2>
            <p class="mb-0">Upload and organize images for {{ venue.venue_name }}</p>
        </div>
    </div>

    <!-- Upload Section -->
    {% if can_add_image %}
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Upload New Image</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="image-upload-form">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.image.id_for_label }}" class="form-label">Image File</label>
                            <input type="file" name="{{ form.image.name }}" id="{{ form.image.id_for_label }}" class="form-control {% if form.image.errors %}is-invalid{% endif %}" accept="image/jpeg,image/jpg,image/png" required>
                            {% if form.image.errors %}
                            <div class="invalid-feedback">
                                {{ form.image.errors }}
                            </div>
                            {% endif %}
                            <div class="form-text">JPG or PNG format, maximum 5MB</div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.caption.id_for_label }}" class="form-label">Caption (Optional)</label>
                                    <input type="text" name="{{ form.caption.name }}" id="{{ form.caption.id_for_label }}" class="form-control {% if form.caption.errors %}is-invalid{% endif %}" placeholder="Optional caption for this image">
                                    {% if form.caption.errors %}
                                    <div class="invalid-feedback">
                                        {{ form.caption.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.order.id_for_label }}" class="form-label">Display Order</label>
                                    <input type="number" name="{{ form.order.name }}" id="{{ form.order.id_for_label }}" class="form-control {% if form.order.errors %}is-invalid{% endif %}" min="1" max="7" value="{{ images.count|add:1 }}">
                                    {% if form.order.errors %}
                                    <div class="invalid-feedback">
                                        {{ form.order.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>Upload Image
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Guidelines</h5>
                </div>
                <div class="card-body">
                    <h6 class="mb-3">Image Requirements</h6>
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> JPG or PNG format only
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> Maximum file size: 5MB
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2"></i> High resolution recommended
                        </li>
                        <li>
                            <i class="fas fa-check-circle me-2"></i> Maximum 7 images per venue
                        </li>
                    </ul>

                    <h6 class="mb-3">Tips</h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb me-2"></i> Use high-quality, well-lit photos
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb me-2"></i> Show different areas of your venue
                        </li>
                        <li>
                            <i class="fas fa-lightbulb me-2"></i> Set your best image as primary
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                You have reached the maximum limit of {{ max_images }} images per venue.
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Current Images Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Current Images ({{ images.count }}/{{ max_images }})</h5>
                    {% if images.count > 1 %}
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="reorder-mode-btn">
                        <i class="fas fa-sort me-2"></i>Reorder Images
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if images %}
                    <div id="images-grid" class="row">
                        {% for image in images %}
                        <div class="col-lg-4 col-md-6 mb-4" data-image-id="{{ image.id }}" data-order="{{ image.order }}">
                            <div class="card h-100 image-card">
                                <div class="position-relative">
                                    <img src="{{ image.image.url }}" class="card-img-top" alt="{{ image.caption|default:'Venue image' }}" style="height: 200px; object-fit: cover;">
                                    
                                    <!-- Primary badge -->
                                    {% if image.is_primary %}
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-success">Primary</span>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Order badge -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-secondary">{{ image.order }}</span>
                                    </div>
                                    
                                    <!-- Reorder handle (hidden by default) -->
                                    <div class="position-absolute top-50 start-50 translate-middle reorder-handle d-none">
                                        <i class="fas fa-grip-vertical fa-2x text-white" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);"></i>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h6 class="card-title">Image {{ image.order }}</h6>
                                    {% if image.caption %}
                                    <p class="card-text small text-muted">{{ image.caption }}</p>
                                    {% endif %}
                                    
                                    <div class="btn-group w-100" role="group">
                                        {% if not image.is_primary %}
                                        <button type="button" class="btn btn-outline-primary btn-sm set-primary-btn" data-image-id="{{ image.id }}">
                                            <i class="fas fa-star me-1"></i>Set Primary
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-danger btn-sm delete-image-btn" data-image-id="{{ image.id }}">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Reorder controls (hidden by default) -->
                    <div id="reorder-controls" class="d-none mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Drag and drop images to reorder them. Click "Save Order" when finished.
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="save-order-btn">
                                <i class="fas fa-save me-2"></i>Save Order
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancel-reorder-btn">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No images uploaded yet</h5>
                        <p class="text-muted">Upload your first venue image to get started.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Back to venue management -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Venue Management
            </a>
        </div>
    </div>
{% endblock %}

{% block venues_extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    let sortable = null;
    let isReorderMode = false;
    
    // Reorder mode toggle
    const reorderBtn = document.getElementById('reorder-mode-btn');
    const reorderControls = document.getElementById('reorder-controls');
    const imagesGrid = document.getElementById('images-grid');
    const reorderHandles = document.querySelectorAll('.reorder-handle');
    
    if (reorderBtn) {
        reorderBtn.addEventListener('click', function() {
            toggleReorderMode();
        });
    }
    
    function toggleReorderMode() {
        isReorderMode = !isReorderMode;
        
        if (isReorderMode) {
            // Enable reorder mode
            reorderControls.classList.remove('d-none');
            reorderHandles.forEach(handle => handle.classList.remove('d-none'));
            reorderBtn.innerHTML = '<i class="fas fa-times me-2"></i>Cancel Reorder';
            
            // Initialize sortable
            sortable = Sortable.create(imagesGrid, {
                animation: 150,
                handle: '.reorder-handle',
                ghostClass: 'sortable-ghost'
            });
        } else {
            // Disable reorder mode
            reorderControls.classList.add('d-none');
            reorderHandles.forEach(handle => handle.classList.add('d-none'));
            reorderBtn.innerHTML = '<i class="fas fa-sort me-2"></i>Reorder Images';
            
            if (sortable) {
                sortable.destroy();
                sortable = null;
            }
        }
    }
    
    // Save order
    document.getElementById('save-order-btn')?.addEventListener('click', function() {
        const imageCards = document.querySelectorAll('[data-image-id]');
        const imageOrders = [];
        
        imageCards.forEach((card, index) => {
            imageOrders.push({
                id: parseInt(card.dataset.imageId),
                order: index + 1
            });
        });
        
        fetch('{% url "venues_app:reorder_venue_images" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({image_orders: imageOrders})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while saving the order.');
        });
    });
    
    // Cancel reorder
    document.getElementById('cancel-reorder-btn')?.addEventListener('click', function() {
        location.reload();
    });
    
    // Set primary image
    document.querySelectorAll('.set-primary-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const imageId = this.dataset.imageId;
            
            fetch(`/venues/provider/images/${imageId}/set-primary/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while setting the primary image.');
            });
        });
    });
    
    // Delete image
    document.querySelectorAll('.delete-image-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete this image? This action cannot be undone.')) {
                const imageId = this.dataset.imageId;
                
                fetch(`/venues/provider/images/${imageId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the image.');
                });
            }
        });
    });
});
</script>

<style>
.sortable-ghost {
    opacity: 0.4;
}

.image-card {
    transition: transform 0.2s;
}

.image-card:hover {
    transform: translateY(-2px);
}

.reorder-handle {
    cursor: move;
}
</style>
{% endblock %}
