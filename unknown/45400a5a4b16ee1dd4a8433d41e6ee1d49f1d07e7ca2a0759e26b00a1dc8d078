"""
Unit tests for review_app URL routing.

This module contains comprehensive unit tests for all URL patterns in the review_app,
including URL resolution, reverse URL generation, and access permissions.
"""

# Django imports
from django.test import TestCase
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model

# Local imports
from review_app import views

User = get_user_model()


class ReviewAppURLsTest(TestCase):
    """Test URL routing for review_app."""

    def test_venue_reviews_url(self):
        """Test venue reviews URL."""
        url = reverse('review_app:venue_reviews', kwargs={'venue_id': 1})
        self.assertEqual(url, '/reviews/venue/1/reviews/')
        self.assertEqual(resolve(url).func, views.venue_reviews_view)

    def test_submit_review_url(self):
        """Test submit review URL."""
        url = reverse('review_app:submit_review', kwargs={'venue_id': 1})
        self.assertEqual(url, '/reviews/venue/1/submit/')
        self.assertEqual(resolve(url).func, views.submit_review_view)

    def test_edit_review_url(self):
        """Test edit review URL."""
        url = reverse('review_app:edit_review', kwargs={'review_slug': 'test-slug'})
        self.assertEqual(url, '/reviews/review/test-slug/edit/')
        self.assertEqual(resolve(url).func, views.edit_review_view)

    def test_flag_review_url(self):
        """Test flag review URL."""
        url = reverse('review_app:flag_review', kwargs={'review_slug': 'test-slug'})
        self.assertEqual(url, '/reviews/review/test-slug/flag/')
        self.assertEqual(resolve(url).func, views.flag_review_view)

    def test_customer_review_history_url(self):
        """Test customer review history URL."""
        url = reverse('review_app:customer_review_history')
        self.assertEqual(url, '/reviews/customer/history/')
        self.assertEqual(resolve(url).func, views.customer_review_history_view)

    def test_provider_venue_reviews_url(self):
        """Test provider venue reviews URL."""
        url = reverse('review_app:provider_venue_reviews')
        self.assertEqual(url, '/reviews/provider/reviews/')
        self.assertEqual(resolve(url).func.view_class, views.ProviderVenueReviewsView)

    def test_provider_review_summary_url(self):
        """Test provider review summary URL."""
        url = reverse('review_app:provider_review_summary')
        self.assertEqual(url, '/reviews/provider/summary/')
        self.assertEqual(resolve(url).func, views.provider_review_summary_view)

    def test_provider_respond_to_review_url(self):
        """Test provider respond to review URL."""
        url = reverse('review_app:provider_respond_to_review', kwargs={'review_slug': 'test-slug'})
        self.assertEqual(url, '/reviews/provider/respond/test-slug/')
        self.assertEqual(resolve(url).func, views.provider_respond_to_review_view)

    def test_provider_edit_response_url(self):
        """Test provider edit response URL."""
        url = reverse('review_app:provider_edit_response', kwargs={'response_id': 1})
        self.assertEqual(url, '/reviews/provider/response/1/edit/')
        self.assertEqual(resolve(url).func, views.provider_edit_response_view)

    def test_admin_review_moderation_url(self):
        """Test admin review moderation URL."""
        url = reverse('review_app:admin_review_moderation')
        self.assertEqual(url, '/reviews/admin/moderation/')
        self.assertEqual(resolve(url).func, views.admin_review_moderation_view)

    def test_admin_moderate_review_url(self):
        """Test admin moderate review URL."""
        url = reverse('review_app:admin_moderate_review', kwargs={'review_slug': 'test-slug'})
        self.assertEqual(url, '/reviews/admin/review/test-slug/moderate/')
        self.assertEqual(resolve(url).func, views.admin_moderate_review_view)

    def test_admin_flag_resolution_url(self):
        """Test admin flag resolution URL."""
        url = reverse('review_app:admin_flag_resolution', kwargs={'flag_id': 1})
        self.assertEqual(url, '/reviews/admin/flag/1/resolve/')
        self.assertEqual(resolve(url).func, views.admin_flag_resolution_view)

    def test_url_namespacing(self):
        """Test that all URLs are properly namespaced."""
        # Test that URLs are accessible through the review_app namespace
        urls_to_test = [
            ('venue_reviews', {'venue_id': 1}),
            ('submit_review', {'venue_id': 1}),
            ('edit_review', {'review_slug': 'slug'}),
            ('flag_review', {'review_slug': 'slug'}),
            ('customer_review_history', {}),
            ('provider_venue_reviews', {}),
            ('provider_review_summary', {}),
            ('provider_respond_to_review', {'review_slug': 'slug'}),
            ('provider_edit_response', {'response_id': 1}),
            ('admin_review_moderation', {}),
            ('admin_moderate_review', {'review_slug': 'slug'}),
            ('admin_flag_resolution', {'flag_id': 1}),
        ]
        
        for url_name, kwargs in urls_to_test:
            try:
                url = reverse(f'review_app:{url_name}', kwargs=kwargs)
                self.assertTrue(url.startswith('/reviews/'))
            except Exception as e:
                self.fail(f"Failed to reverse URL {url_name}: {e}")

    def test_url_parameters(self):
        """Test that URLs with parameters work correctly."""
        # Test with different parameter values
        test_cases = [
            ('venue_reviews', {'venue_id': 123}),
            ('submit_review', {'venue_id': 456}),
            ('edit_review', {'review_slug': 's789'}),
            ('flag_review', {'review_slug': 's101'}),
            ('provider_respond_to_review', {'review_slug': 's202'}),
            ('provider_edit_response', {'response_id': 303}),
            ('admin_moderate_review', {'review_slug': 's404'}),
            ('admin_flag_resolution', {'flag_id': 505}),
        ]

        for url_name, kwargs in test_cases:
            url = reverse(f'review_app:{url_name}', kwargs=kwargs)
            resolved = resolve(url)

            # Check that parameters are correctly captured
            for key, value in kwargs.items():
                self.assertEqual(resolved.kwargs[key], value)

    def test_url_patterns_coverage(self):
        """Test that all view functions have corresponding URL patterns."""
        # List of view functions that should have URLs
        view_functions = [
            'venue_reviews_view',
            'submit_review_view',
            'edit_review_view',
            'flag_review_view',
            'customer_review_history_view',
            'ProviderVenueReviewsView',
            'provider_review_summary_view',
            'provider_respond_to_review_view',
            'provider_edit_response_view',
            'admin_review_moderation_view',
            'admin_moderate_review_view',
            'admin_flag_resolution_view',
        ]
        
        for view_func_name in view_functions:
            # Check that the view function exists
            self.assertTrue(
                hasattr(views, view_func_name),
                f"View function {view_func_name} not found in views module"
            )


class URLAccessTest(TestCase):
    """Test URL access permissions and redirects."""

    def setUp(self):
        """Set up test data."""
        # Import required models
        from venues_app.models import Venue, Category
        from accounts_app.models import ServiceProviderProfile

        # Create test users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.CUSTOMER
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.SERVICE_PROVIDER
        )

        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            role=User.ADMIN,
            is_staff=True,
            is_superuser=True
        )

        # Create service provider profile for the provider
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name="Test Business",
            business_phone_number="+**********",
            contact_person_name="Test Contact",
            business_address="123 Business St",
            city="Test City",
            state="CA",
            zip_code="12345"
        )

        # Create test category and venue
        self.category = Category.objects.create(
            category_name="Test Category",
            slug="test-category"
        )

        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Venue",
            short_description="A test venue for testing",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
            state="Test State",
            county="Test County",
            city="Test City",
            street_number="123",
            street_name="Test Street"
        )

        # Note: Some tests may fail due to missing test data (reviews, responses, flags)
        # but the main URL resolution and permission tests should pass

    def test_public_urls(self):
        """Test URLs that should be accessible without authentication."""
        public_urls = [
            reverse('review_app:venue_reviews', kwargs={'venue_id': self.venue.id}),
        ]

        for url in public_urls:
            response = self.client.get(url)
            # Should not redirect to login (may return 404 for non-existent objects)
            self.assertNotEqual(response.status_code, 302)

    def test_customer_only_urls(self):
        """Test URLs that require customer authentication."""
        customer_urls = [
            reverse('review_app:submit_review', kwargs={'venue_id': self.venue.id}),
            reverse('review_app:edit_review', kwargs={'review_slug': 'test-slug'}),
            reverse('review_app:flag_review', kwargs={'review_slug': 'test-slug'}),
            reverse('review_app:customer_review_history'),
        ]
        
        # Test unauthenticated access
        for url in customer_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test authenticated customer access
        self.client.login(email='<EMAIL>', password='testpass123')
        for url in customer_urls:
            response = self.client.get(url)
            # Should not redirect to login (may return 404 for non-existent objects)
            self.assertNotEqual(response.status_code, 302)

    def test_provider_only_urls(self):
        """Test URLs that require provider authentication."""
        provider_urls = [
            reverse('review_app:provider_venue_reviews'),
            reverse('review_app:provider_review_summary'),
            reverse('review_app:provider_respond_to_review', kwargs={'review_slug': 'test-slug'}),
            reverse('review_app:provider_edit_response', kwargs={'response_id': 1}),
        ]
        
        # Test unauthenticated access
        for url in provider_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test customer trying to access provider URLs
        self.client.login(email='<EMAIL>', password='testpass123')
        for url in provider_urls:
            response = self.client.get(url)
            # Provider views check role and return 403 for non-providers
            self.assertEqual(response.status_code, 403)  # Forbidden
        
        self.client.logout()
        
        # Test authenticated provider access
        self.client.login(email='<EMAIL>', password='testpass123')
        for url in provider_urls:
            response = self.client.get(url)
            # Should not redirect to login (may return 404 for non-existent objects)
            self.assertNotEqual(response.status_code, 302)

    def test_admin_only_urls(self):
        """Test URLs that require admin authentication."""
        admin_urls = [
            reverse('review_app:admin_review_moderation'),
            reverse('review_app:admin_moderate_review', kwargs={'review_slug': 'test-slug'}),
            reverse('review_app:admin_flag_resolution', kwargs={'flag_id': 1}),
        ]
        
        # Test unauthenticated access
        for url in admin_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test non-admin users trying to access admin URLs
        non_admin_users = [
            ('<EMAIL>', 'testpass123'),
            ('<EMAIL>', 'testpass123'),
        ]
        
        for email, password in non_admin_users:
            self.client.login(email=email, password=password)
            for url in admin_urls:
                response = self.client.get(url)
                self.assertEqual(response.status_code, 403)  # Forbidden
            self.client.logout()
        
        # Test authenticated admin access
        self.client.login(email='<EMAIL>', password='testpass123')
        for url in admin_urls:
            response = self.client.get(url)
            # Should not redirect to login (may return 404 for non-existent objects)
            self.assertNotEqual(response.status_code, 302)

    def test_url_redirect_patterns(self):
        """Test URL redirect patterns for authentication."""
        # URLs that should redirect to login when not authenticated
        protected_urls = [
            reverse('review_app:submit_review', kwargs={'venue_id': self.venue.id}),
            reverse('review_app:customer_review_history'),
            reverse('review_app:provider_venue_reviews'),
            reverse('review_app:admin_review_moderation'),
        ]

        for url in protected_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)
            # Check that redirect goes to login page
            self.assertTrue(response.url.startswith('/accounts/customer/login/'))

    def test_url_parameter_validation(self):
        """Test URL parameter validation."""
        # Test with invalid parameter types that should not match URL patterns
        invalid_urls = [
            '/reviews/venue/abc/reviews/',  # Non-numeric venue_id
            '/reviews/admin/flag/invalid/resolve/',  # Non-numeric flag_id
        ]

        for url in invalid_urls:
            response = self.client.get(url)
            # These should return 404 because they don't match the URL patterns
            self.assertEqual(response.status_code, 404)  # Should not match URL pattern
