# Django imports
from django import forms
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from datetime import timedelta

# Local imports
from ..models import CartItem, Booking


class AddToCartForm(forms.ModelForm):
    """Form for adding a service to the customer's cart."""

    class Meta:
        model = CartItem
        fields = ["selected_date", "selected_time_slot", "quantity"]
        widgets = {
            "selected_date": forms.DateInput(
                attrs={
                    "class": "form-control",
                    "type": "date",
                    "min": timezone.now().date().isoformat(),
                    "max": (timezone.now().date() + timedelta(days=30)).isoformat(),
                    "id": "id_selected_date",
                }
            ),
            "selected_time_slot": forms.Select(
                attrs={"class": "form-control", "id": "id_selected_time_slot"}
            ),
            "quantity": forms.NumberInput(
                attrs={"class": "form-control", "min": 1, "max": 10, "value": 1}
            ),
        }
        labels = {
            "selected_date": _("Select Date"),
            "selected_time_slot": _("Select Time Slot"),
            "quantity": _("Number of Appointments"),
        }
        help_texts = {
            "selected_date": _("Choose a date within the next 30 days"),
            "selected_time_slot": _("Available time slots will appear after selecting a date"),
            "quantity": _("Maximum 10 appointments per booking"),
        }

    def __init__(self, service=None, user=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = service
        self.user = user

        self.fields["selected_time_slot"].choices = [("", "Select a date first")]

        today = timezone.now().date()
        max_date = today + timedelta(days=30)
        self.fields["selected_date"].widget.attrs.update({"min": today.isoformat(), "max": max_date.isoformat()})

    def clean(self):
        cleaned_data = super().clean()
        selected_date = cleaned_data.get("selected_date")
        selected_time_slot = cleaned_data.get("selected_time_slot")
        quantity = cleaned_data.get("quantity")

        if not all([selected_date, selected_time_slot, quantity]):
            return cleaned_data

        if selected_date < timezone.now().date():
            self.add_error('selected_date', _("You cannot book a service in the past."))

        if self.user and hasattr(self.user, "is_service_provider") and self.user.is_service_provider:
            raise ValidationError(_("Service providers cannot add items to cart."))

        return cleaned_data

    def save(self, cart=None, commit=True):
        """Save the cart item with the provided cart and service."""
        cart_item = super().save(commit=False)

        if cart:
            cart_item.cart = cart
        if self.service:
            cart_item.service = self.service
            cart_item.price_per_item = self.service.price  # Use the price property

        if commit:
            cart_item.save()

        return cart_item


class UpdateCartItemForm(forms.ModelForm):
    """Form for updating the quantity of a cart item."""

    class Meta:
        model = CartItem
        fields = ["quantity"]
        widgets = {
            "quantity": forms.NumberInput(attrs={"class": "form-control", "min": 1, "max": 10})
        }
        labels = {"quantity": _("Number of Appointments")}
        help_texts = {"quantity": _("Maximum 10 appointments per booking")}


class CheckoutForm(forms.ModelForm):
    """Form used during checkout."""

    class Meta:
        model = Booking
        fields = ["notes"]
        widgets = {
            "notes": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Any special requests or notes for the service provider?",
                    "maxlength": 500,
                }
            )
        }
        labels = {"notes": _("Special Requests (Optional)")}
        help_texts = {"notes": _("Maximum 500 characters")}


class BookingCancellationForm(forms.Form):
    """Form for cancelling a booking."""

    cancellation_reason = forms.CharField(
        label=_("Reason for Cancellation"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Please provide a reason for cancellation (optional)",
                "maxlength": 500,
            }
        ),
        required=False,
        help_text=_("Maximum 500 characters"),
    )

    def clean_cancellation_reason(self):
        reason = self.cleaned_data.get("cancellation_reason", "")
        return reason.strip()
