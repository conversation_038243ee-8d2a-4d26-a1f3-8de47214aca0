{% extends 'venues_app/base_venues.html' %}

{% block title %}{{ venue.name }} - Provider Dashboard - CozyWish{% endblock %}

{% block venues_extra_css %}
<style>
    /* Fix for venue detail page specific styling */
    .venues-wrapper {
        background-color: white !important;
        color: black !important;
    }

    /* Ensure all text is black */
    .venues-wrapper h1, .venues-wrapper h2, .venues-wrapper h3,
    .venues-wrapper h4, .venues-wrapper h5, .venues-wrapper h6,
    .venues-wrapper p, .venues-wrapper span, .venues-wrapper div,
    .venues-wrapper li, .venues-wrapper a {
        color: black !important;
    }

    /* Card styling fixes */
    .venues-wrapper .card {
        background-color: white !important;
        border: 2px solid black !important;
    }

    /* Remove conflicting card-header styles - let base template handle this */
    .venues-wrapper .card-body {
        background-color: white !important;
        color: black !important;
    }

    /* Minimal styling - let base template handle most styling */
    .venues-wrapper .text-muted {
        color: rgba(0, 0, 0, 0.6) !important;
    }

    .venues-wrapper .fw-bold {
        color: black !important;
        font-weight: 600 !important;
    }

    /* Rating stars */
    .venues-wrapper .rating span {
        color: black !important;
    }
</style>
{% endblock %}

{% block venues_content %}
    <div class="row">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>{{ venue.venue_name }}</h1>
                <span class="badge {% if venue.approval_status == 'approved' %}badge-approved{% elif venue.approval_status == 'pending' %}badge-pending{% else %}badge-rejected{% endif %} fs-6 px-3 py-2">
                    {{ venue.get_approval_status_display }}
                </span>
            </div>

            {% if venue.approval_status == 'rejected' %}
            <div class="alert alert-danger mb-4">
                <h5 class="alert-heading">Rejection Reason:</h5>
                <p class="mb-0">{{ venue.rejection_reason }}</p>
            </div>
            {% endif %}

            <!-- Venue Images Gallery -->
            {% if images %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Images</h5>
                </div>
                <div class="card-body">
                    <div id="venueCarousel" class="carousel slide mb-3" data-bs-ride="carousel">
                        <div class="carousel-inner rounded overflow-hidden ratio ratio-21x9">
                            {% if primary_image %}
                            <div class="carousel-item active">
                                <img src="{{ primary_image.image.url }}" alt="{{ primary_image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                                {% if primary_image.caption %}
                                <div class="carousel-caption d-none d-md-block">
                                    <p class="mb-0">{{ primary_image.caption }}</p>
                                </div>
                                {% endif %}
                            </div>
                            {% elif venue.main_image %}
                            <div class="carousel-item active">
                                <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                            </div>
                            {% endif %}

                            {% for image in gallery_images %}
                            <div class="carousel-item {% if not primary_image and not venue.main_image and forloop.first %}active{% endif %}">
                                <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                                {% if image.caption %}
                                <div class="carousel-caption d-none d-md-block">
                                    <p class="mb-0">{{ image.caption }}</p>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Carousel controls (only show if there are multiple images) -->
                        {% if images.count > 1 %}
                        <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>

                        <!-- Carousel indicators -->
                        <div class="carousel-indicators">
                            {% if primary_image or venue.main_image %}
                            <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Primary image"></button>
                            {% endif %}
                            {% for image in gallery_images %}
                            <button type="button" data-bs-target="#venueCarousel" data-bs-slide-to="{% if primary_image or venue.main_image %}{{ forloop.counter }}{% else %}{{ forloop.counter0 }}{% endif %}" aria-label="Image {{ forloop.counter }}"></button>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Image thumbnails -->
                    {% if images.count > 1 %}
                    <div class="row">
                        {% for image in images %}
                        <div class="col-2 mb-2">
                            <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}"
                                 class="img-thumbnail w-100 {% if image.is_primary %}border-primary border-3{% endif %}"
                                 style="height: 60px; object-fit: cover; cursor: pointer;"
                                 onclick="document.querySelector('[data-bs-slide-to=&quot;{{ forloop.counter0 }}{% if primary_image and not image.is_primary %}{{ forloop.counter }}{% endif %}&quot;]').click()">
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% else %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Images</h5>
                </div>
                <div class="card-body text-center py-5">
                    <i class="fas fa-image fa-4x text-muted mb-3"></i>
                    <p class="text-muted">No images uploaded yet. Add some images to showcase your venue!</p>
                </div>
            </div>
            {% endif %}

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Venue Details</h5>
                    <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                </div>
                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-1 fw-bold">Business Name</p>
                            <p>{{ venue.service_provider.business_name }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1 fw-bold">Venue Type</p>
                            <p>{{ venue.get_venue_type_display }}</p>
                        </div>
                    </div>

                    <!-- Categories -->
                    {% if venue.categories.all %}
                    <div class="mb-3">
                        <p class="mb-1 fw-bold">Categories</p>
                        <div>
                            {% for category in venue.categories.all %}
                            <span class="badge badge-approved me-1 mb-1">{{ category.category_name }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Price Range -->
                    {% if price_range %}
                    <div class="mb-3">
                        <p class="mb-1 fw-bold">Price Range</p>
                        <span class="badge badge-approved fs-6 px-3 py-2">{{ price_range }}</span>
                    </div>
                    {% endif %}

                    <!-- Address -->
                    <div class="mb-3">
                        <p class="mb-1 fw-bold">Address</p>
                        <p>{{ venue.full_address }}</p>
                    </div>

                    <!-- Contact Information -->
                    <div class="row mb-3">
                        {% if venue.phone %}
                        <div class="col-md-6">
                            <p class="mb-1 fw-bold">Phone</p>
                            <p><i class="fas fa-phone me-2"></i>{{ venue.phone }}</p>
                        </div>
                        {% endif %}
                        {% if venue.email %}
                        <div class="col-md-6">
                            <p class="mb-1 fw-bold">Email</p>
                            <p><i class="fas fa-envelope me-2"></i>{{ venue.email }}</p>
                        </div>
                        {% endif %}
                    </div>

                    {% if venue.website_url %}
                    <div class="mb-3">
                        <p class="mb-1 fw-bold">Website</p>
                        <p><i class="fas fa-globe me-2"></i><a href="{{ venue.website_url }}" target="_blank" rel="noopener">{{ venue.website_url }}</a></p>
                    </div>
                    {% endif %}

                    <!-- Description -->
                    <div class="mb-3">
                        <p class="mb-1 fw-bold">Description</p>
                        <p>{{ venue.short_description|default:"No description provided"|linebreaks }}</p>
                    </div>

                    <!-- Tags -->
                    <div class="mb-3">
                        <p class="mb-1 fw-bold">Tags</p>
                        <div>
                            {% if venue.tags %}
                            <span class="text-muted">{{ venue.tags }}</span>
                            {% else %}
                            <span class="text-muted">No tags</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Status Information -->
                    <div class="row mb-0">
                        <div class="col-md-4">
                            <p class="mb-1 fw-bold">Visibility</p>
                            <p>{% if venue.visibility == 'active' %}Active{% else %}Inactive{% endif %}</p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1 fw-bold">Created</p>
                            <p>{{ venue.created_at|date:"M d, Y" }}</p>
                        </div>
                        <div class="col-md-4">
                            <p class="mb-1 fw-bold">Last Updated</p>
                            <p>{{ venue.updated_at|date:"M d, Y" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amenities Section -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Amenities & Features</h5>
                    <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-star me-1"></i>Manage Amenities
                    </a>
                </div>
                <div class="card-body">
                    {% if amenities %}
                    <div class="row">
                        {% for amenity in amenities %}
                        <div class="col-md-6 col-lg-4 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>
                                    {% if amenity.custom_name %}
                                        {{ amenity.custom_name }}
                                    {% else %}
                                        {{ amenity.get_amenity_type_display }}
                                    {% endif %}
                                </span>
                            </div>
                            {% if amenity.description %}
                            <small class="text-muted ms-4">{{ amenity.description }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="mb-0 text-muted">No amenities added yet. Click "Manage Amenities" to add features that make your venue special!</p>
                    {% endif %}
                </div>
            </div>

            <!-- FAQs Section -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Frequently Asked Questions</h5>
                    <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-question-circle me-1"></i>Manage FAQs
                    </a>
                </div>
                <div class="card-body">
                    {% if faqs %}
                    <div class="accordion" id="faqAccordion">
                        {% for faq in faqs %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqHeading{{ faq.id }}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse{{ faq.id }}" aria-expanded="false" aria-controls="faqCollapse{{ faq.id }}">
                                    {{ faq.order }}. {{ faq.question }}
                                </button>
                            </h2>
                            <div id="faqCollapse{{ faq.id }}" class="accordion-collapse collapse" aria-labelledby="faqHeading{{ faq.id }}" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <p class="mb-3">{{ faq.answer|linebreaks }}</p>
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'venues_app:edit_faq' faq.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit me-1"></i>Edit
                                        </a>
                                        <a href="{% url 'venues_app:delete_faq' faq.id %}" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="mb-3 text-muted">No FAQs added yet. Add your first FAQ below or click "Manage FAQs" for more options.</p>

                    {% if can_add_faq %}
                    <!-- Quick Add FAQ Form -->
                    <div class="border-top pt-3">
                        <h6 class="mb-3">Quick Add FAQ</h6>
                        <form method="post" action="{% url 'venues_app:manage_faqs' %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="id_question" class="form-label">Question</label>
                                <input type="text" class="form-control" id="id_question" name="question" maxlength="255" placeholder="What are your operating hours?" required>
                            </div>
                            <div class="mb-3">
                                <label for="id_answer" class="form-label">Answer</label>
                                <textarea class="form-control" id="id_answer" name="answer" rows="3" maxlength="500" placeholder="We are open Monday to Friday from 9 AM to 6 PM..." required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="id_order" class="form-label">Display Order</label>
                                <select class="form-select" id="id_order" name="order" required>
                                    <option value="1">1 - First</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5 - Last</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>Add FAQ
                            </button>
                        </form>
                    </div>
                    {% endif %}
                    {% endif %}
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Services</h5>
                    <a href="{% url 'venues_app:service_create' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>Add Service
                    </a>
                </div>
                <div class="card-body">
                    {% if services %}
                    <div class="list-group">
                        {% for service in services %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ service.service_title }}</h6>
                                    <p class="mb-1 small text-muted">{{ service.short_description|truncatechars:100 }}</p>
                                </div>
                                <div class="text-end">
                                    <div class="d-flex flex-column align-items-end mb-2">
                                        {% if service.price_max and service.price_max != service.price_min %}
                                        <span class="fw-bold">${{ service.price_min }} - ${{ service.price_max }}</span>
                                        {% else %}
                                        <span class="fw-bold">${{ service.price_min }}</span>
                                        {% endif %}
                                        <small class="text-muted">{{ service.duration_minutes }} min</small>
                                    </div>
                                    <a href="{% url 'venues_app:service_edit' pk=service.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i>Edit
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="mb-0">No services added yet. Click the "Add Service" button to create your first service.</p>
                    {% endif %}
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Reviews</h5>
                </div>
                <div class="card-body">
                    {% if venue.reviews.exists %}
                    <div class="reviews">
                        {% for review in venue.reviews.all|slice:":5" %}
                        <div class="review mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-0">{{ review.user.get_full_name|default:review.user.email }}</h6>
                                    <div class="rating">
                                        {% for i in "12345" %}
                                        <span class="{% if forloop.counter <= review.rating %}fw-bold{% else %}text-muted{% endif %}">★</span>
                                        {% endfor %}
                                        <span class="ms-1 text-muted">{{ review.created_at|date:"M d, Y" }}</span>
                                    </div>
                                </div>
                                <span class="badge {% if review.is_approved %}badge-approved{% else %}badge-pending{% endif %}">
                                    {% if review.is_approved %}Approved{% else %}Pending{% endif %}
                                </span>
                            </div>
                            <p class="mb-0">{{ review.comment }}</p>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="mb-0">No reviews yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Venue Status Management -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Status</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1">Current Status</h6>
                            <span class="badge {% if venue.approval_status == 'approved' %}bg-success{% elif venue.approval_status == 'pending' %}bg-warning{% elif venue.approval_status == 'rejected' %}bg-danger{% else %}bg-secondary{% endif %} fs-6 px-3 py-2">
                                {{ venue.get_approval_status_display }}
                            </span>
                        </div>
                    </div>

                    {% if venue.approval_status == 'draft' %}
                        {% if is_ready_for_approval %}
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Ready for Review!</strong> Your venue has all the required information and can be submitted for admin approval.
                            </div>
                            <form method="post" action="{% url 'venues_app:change_venue_status' venue.id %}" class="d-grid gap-2">
                                {% csrf_token %}
                                <button type="submit" name="action" value="submit_for_approval" class="btn btn-success">
                                    <i class="fas fa-paper-plane me-2"></i>Submit for Admin Approval
                                </button>
                            </form>
                        {% else %}
                            <div class="alert alert-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Almost Ready!</strong> Complete the following to submit for approval:
                                <ul class="mb-0 mt-2">
                                    {% if not services.count %}<li>Add at least one service</li>{% endif %}
                                    {% if not images.count %}<li>Upload at least one image</li>{% endif %}
                                </ul>
                            </div>
                        {% endif %}
                    {% elif venue.approval_status == 'pending' %}
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-clock me-2"></i>
                            <strong>Under Review</strong> Your venue is being reviewed by our admin team. You'll be notified once it's approved.
                        </div>
                        <form method="post" action="{% url 'venues_app:change_venue_status' venue.id %}" class="d-grid gap-2">
                            {% csrf_token %}
                            <button type="submit" name="action" value="save_as_draft" class="btn btn-outline-secondary">
                                <i class="fas fa-edit me-2"></i>Move Back to Draft
                            </button>
                        </form>
                    {% elif venue.approval_status == 'approved' %}
                        <div class="alert alert-success mb-3">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Approved!</strong> Your venue is live and visible to customers.
                        </div>
                    {% elif venue.approval_status == 'rejected' %}
                        <div class="alert alert-danger mb-3">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>Needs Changes</strong> Please review the feedback and make necessary updates.
                            {% if venue.admin_notes %}
                                <div class="mt-2"><strong>Admin Notes:</strong> {{ venue.admin_notes }}</div>
                            {% endif %}
                        </div>
                        <form method="post" action="{% url 'venues_app:change_venue_status' venue.id %}" class="d-grid gap-2">
                            {% csrf_token %}
                            <button type="submit" name="action" value="save_as_draft" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>Move to Draft for Editing
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if venue.approval_status == 'approved' %}
                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>View Public Page
                        </a>
                        {% endif %}
                        <a href="{% url 'venues_app:venue_edit' %}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Edit Venue Details
                        </a>
                        <a href="{% url 'venues_app:manage_venue_images' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-images me-2"></i>Manage Images
                        </a>
                        <a href="{% url 'venues_app:service_create' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-plus me-2"></i>Add Service
                        </a>
                        <a href="{% url 'venues_app:manage_faqs' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-question-circle me-2"></i>Manage FAQs
                        </a>
                        <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-star me-2"></i>Manage Amenities
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h3>{{ services.count }}</h3>
                            <p class="text-muted mb-0">Services</p>
                        </div>
                        <div class="col-6 mb-3">
                            <h3>{{ images.count }}</h3>
                            <p class="text-muted mb-0">Images</p>
                        </div>
                        <div class="col-6 mb-3">
                            <h3>{{ faqs.count }}</h3>
                            <p class="text-muted mb-0">FAQs</p>
                        </div>
                        <div class="col-6 mb-3">
                            <h3>{{ amenities.count }}</h3>
                            <p class="text-muted mb-0">Amenities</p>
                        </div>
                        <div class="col-6">
                            <h3>{{ venue.categories.count }}</h3>
                            <p class="text-muted mb-0">Categories</p>
                        </div>
                        <div class="col-6">
                            <h3>{{ opening_hours.count }}</h3>
                            <p class="text-muted mb-0">Operating Days</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Opening Hours</h5>
                    <a href="{% url 'venues_app:manage_operating_hours' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-clock me-1"></i>Edit Hours
                    </a>
                </div>
                <div class="card-body">
                    {% if opening_hours %}
                    <ul class="list-unstyled">
                        {% for hour in opening_hours %}
                        <li class="d-flex justify-content-between py-1">
                            <span class="fw-bold">{{ hour.get_day_display }}</span>
                            {% if hour.is_closed %}
                            <span class="text-muted">Closed</span>
                            {% else %}
                            <span>{{ hour.opening|time:"g:i A" }} - {{ hour.closing|time:"g:i A" }}</span>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted mb-0">No opening hours set. Click "Edit Hours" to set your operating schedule.</p>
                    {% endif %}
                </div>
            </div>

            <!-- Venue Completion Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Venue Completion</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Basic Information</span>
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                        <div class="progress mb-1" style="height: 4px;">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Images ({{ images.count }}/5)</span>
                            {% if images.count > 0 %}
                            <i class="fas fa-check-circle text-success"></i>
                            {% else %}
                            <i class="fas fa-exclamation-circle text-warning"></i>
                            {% endif %}
                        </div>
                        <div class="progress mb-1" style="height: 4px;">
                            <div class="progress-bar {% if images.count > 0 %}bg-success{% else %}bg-warning{% endif %}" style="width: {% if images.count >= 5 %}100{% elif images.count == 4 %}80{% elif images.count == 3 %}60{% elif images.count == 2 %}40{% elif images.count == 1 %}20{% else %}0{% endif %}%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Services ({{ services.count }})</span>
                            {% if services.count > 0 %}
                            <i class="fas fa-check-circle text-success"></i>
                            {% else %}
                            <i class="fas fa-exclamation-circle text-warning"></i>
                            {% endif %}
                        </div>
                        <div class="progress mb-1" style="height: 4px;">
                            <div class="progress-bar {% if services.count > 0 %}bg-success{% else %}bg-warning{% endif %}" style="width: {% if services.count > 0 %}100{% else %}0{% endif %}%"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Operating Hours</span>
                            {% if opening_hours.count > 0 %}
                            <i class="fas fa-check-circle text-success"></i>
                            {% else %}
                            <i class="fas fa-exclamation-circle text-warning"></i>
                            {% endif %}
                        </div>
                        <div class="progress mb-1" style="height: 4px;">
                            <div class="progress-bar {% if opening_hours.count > 0 %}bg-success{% else %}bg-warning{% endif %}" style="width: {% if opening_hours.count > 0 %}100{% else %}0{% endif %}%"></div>
                        </div>
                    </div>

                    <div class="mb-0">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>FAQs ({{ faqs.count }}/5)</span>
                            {% if faqs.count > 0 %}
                            <i class="fas fa-check-circle text-success"></i>
                            {% else %}
                            <i class="fas fa-info-circle text-muted"></i>
                            {% endif %}
                        </div>
                        <div class="progress mb-1" style="height: 4px;">
                            <div class="progress-bar {% if faqs.count > 0 %}bg-success{% else %}bg-light{% endif %}" style="width: {% if faqs.count >= 5 %}100{% elif faqs.count == 4 %}80{% elif faqs.count == 3 %}60{% elif faqs.count == 2 %}40{% elif faqs.count == 1 %}20{% else %}0{% endif %}%"></div>
                        </div>
                        <small class="text-muted">Optional but recommended</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
