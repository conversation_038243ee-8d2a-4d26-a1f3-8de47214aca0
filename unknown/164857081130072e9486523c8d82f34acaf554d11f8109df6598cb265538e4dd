{% extends 'venues_app/base_venues.html' %}
{% load static %}

{% block title %}Venue Approval - {{ venue.venue_name }} - CozyWish Admin{% endblock %}

{% block venues_content %}
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'venues_app:admin_venue_approval_dashboard' %}" class="text-decoration-none">
                            Admin Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'venues_app:admin_pending_venues' %}" class="text-decoration-none">
                            Pending Venues
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Venue Approval</li>
                </ol>
            </nav>

            <div class="d-flex justify-content-between align-items-center">
                <h1>Venue Approval Review</h1>
                <div>
                    <a href="{% url 'venues_app:admin_pending_venues' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Pending
                    </a>
                    <a href="{% url 'venues_app:admin_venue_detail' venue_id=venue.id %}" class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Venue Status Alert -->
    <div class="row mb-4">
        <div class="col-12">
            {% if venue.approval_status == 'pending' %}
                <div class="alert alert-warning">
                    <i class="fas fa-clock"></i>
                    <strong>Pending Approval:</strong> This venue is awaiting your review and approval decision.
                </div>
            {% elif venue.approval_status == 'approved' %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Already Approved:</strong> This venue was approved on {{ venue.approved_at|date:"M d, Y H:i" }}.
                </div>
            {% elif venue.approval_status == 'rejected' %}
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>Already Rejected:</strong> This venue was rejected.
                    {% if venue.admin_notes %}
                        <br><strong>Reason:</strong> {{ venue.admin_notes }}
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <!-- Venue Information Panel -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building"></i> Venue Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Venue Name:</strong></td>
                                    <td>{{ venue.venue_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Service Provider:</strong></td>
                                    <td>
                                        {{ venue.service_provider.business_name }}<br>
                                        <small class="text-muted">{{ venue.service_provider.user.email }}</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Venue Type:</strong></td>
                                    <td>{{ venue.get_venue_type_display|default:"Not specified" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ venue.phone_number|default:"Not provided" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ venue.email|default:"Not provided" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Submitted:</strong></td>
                                    <td>{{ venue.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Modified:</strong></td>
                                    <td>{{ venue.last_modified_at|date:"M d, Y H:i" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Status:</strong></td>
                                    <td>
                                        {% if venue.approval_status == 'pending' %}
                                            <span class="badge bg-warning text-dark">Pending</span>
                                        {% elif venue.approval_status == 'approved' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif venue.approval_status == 'rejected' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Visibility:</strong></td>
                                    <td>
                                        {% if venue.visibility == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ venue.get_visibility_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Description -->
                    {% if venue.short_description %}
                    <div class="mt-3">
                        <h6><strong>Description:</strong></h6>
                        <p class="text-muted">{{ venue.short_description|linebreaks }}</p>
                    </div>
                    {% endif %}

                    <!-- Location Information -->
                    <div class="mt-3">
                        <h6><strong>Location:</strong></h6>
                        <address class="text-muted">
                            {% if venue.street_number and venue.street_name %}
                                {{ venue.street_number }} {{ venue.street_name }}<br>
                            {% endif %}
                            {{ venue.city }}, {{ venue.county }}, {{ venue.state }}
                            {% if venue.zip_code %} {{ venue.zip_code }}{% endif %}
                        </address>
                    </div>

                    <!-- Categories -->
                    {% if venue.categories.exists %}
                    <div class="mt-3">
                        <h6><strong>Categories:</strong></h6>
                        {% for category in venue.categories.all %}
                            <span class="badge bg-info me-2">{{ category.category_name }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Services Information -->
            {% if venue.services.exists %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-concierge-bell"></i> Services ({{ venue.services.count }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for service in venue.services.all %}
                        <div class="col-md-6 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="mb-2">{{ service.service_title }}</h6>
                                <p class="text-muted small mb-2">{{ service.short_description|default:"No description" }}</p>
                                <div class="d-flex justify-content-between">
                                    <span class="text-success">
                                        <strong>${{ service.price_min }}{% if service.price_max and service.price_max != service.price_min %} - ${{ service.price_max }}{% endif %}</strong>
                                    </span>
                                    <span class="text-muted small">{{ service.duration_minutes }} min</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Images -->
            {% if venue.images.exists %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-images"></i> Images ({{ venue.images.count }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for image in venue.images.all %}
                        <div class="col-md-4 mb-3">
                            <div class="position-relative">
                                <img src="{{ image.image.url }}" class="img-fluid rounded" alt="Venue Image" style="height: 150px; width: 100%; object-fit: cover;">
                                {% if image.is_primary %}
                                    <span class="position-absolute top-0 start-0 badge bg-primary m-2">Primary</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Operating Hours -->
            {% if venue.operating_hours_set.exists %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> Operating Hours
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for hours in venue.operating_hours_set.all %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span>{{ hours.get_day_display }}:</span>
                                <span>
                                    {% if hours.is_closed %}
                                        <span class="text-muted">Closed</span>
                                    {% else %}
                                        {{ hours.opening|time:"g:i A" }} - {{ hours.closing|time:"g:i A" }}
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- FAQs -->
            {% if venue.faqs.exists %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle"></i> FAQs ({{ venue.faqs.count }})
                    </h5>
                </div>
                <div class="card-body">
                    {% for faq in venue.faqs.all %}
                    <div class="mb-3">
                        <h6 class="mb-2">{{ faq.question }}</h6>
                        <p class="text-muted">{{ faq.answer }}</p>
                    </div>
                    {% if not forloop.last %}<hr>{% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Approval Actions Panel -->
        <div class="col-lg-4">
            {% if venue.approval_status == 'pending' %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-gavel"></i> Approval Decision
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" id="approvalForm">
                        {% csrf_token %}

                        <!-- Admin Notes -->
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">
                                <strong>Admin Notes</strong>
                                <small class="text-muted">(Optional for approval, required for rejection)</small>
                            </label>
                            <textarea
                                class="form-control"
                                id="admin_notes"
                                name="admin_notes"
                                rows="4"
                                placeholder="Add notes about your decision..."
                            >{{ venue.admin_notes }}</textarea>
                            <div class="form-text">
                                These notes will be included in the notification to the service provider.
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button
                                type="submit"
                                name="action"
                                value="approve"
                                class="btn btn-success btn-lg"
                                onclick="return confirm('Are you sure you want to approve this venue? This will make it visible to customers.')"
                            >
                                <i class="fas fa-check"></i> Approve Venue
                            </button>

                            <button
                                type="submit"
                                name="action"
                                value="reject"
                                class="btn btn-danger btn-lg"
                                onclick="return validateRejection()"
                            >
                                <i class="fas fa-times"></i> Reject Venue
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}

            <!-- Previous Admin Notes -->
            {% if venue.admin_notes and venue.approval_status != 'pending' %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note"></i> Admin Notes
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ venue.admin_notes|linebreaks }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Venue Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> Venue Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="mb-0 text-primary">{{ venue.services.count }}</h4>
                                <small class="text-muted">Services</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="mb-0 text-info">{{ venue.images.count }}</h4>
                            <small class="text-muted">Images</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="mb-0 text-warning">{{ venue.reviews.count }}</h4>
                                <small class="text-muted">Reviews</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="mb-0 text-success">{{ venue.faqs.count }}</h4>
                            <small class="text-muted">FAQs</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function validateRejection() {
            const adminNotes = document.getElementById('admin_notes').value.trim();
            if (!adminNotes) {
                alert('Please provide a reason for rejection in the Admin Notes field.');
                document.getElementById('admin_notes').focus();
                return false;
            }
            return confirm('Are you sure you want to reject this venue? The service provider will be notified with your reason.');
        }
    </script>
{% endblock %}