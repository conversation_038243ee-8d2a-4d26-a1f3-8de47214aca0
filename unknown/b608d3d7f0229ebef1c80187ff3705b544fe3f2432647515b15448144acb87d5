{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Your Cart - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Your Cart</h4>
                    {% if cart.items.count > 0 %}
                    <small class="text-muted">{{ cart.items.count }} item{{ cart.items.count|pluralize }} in your cart</small>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if cart.items.count > 0 %}
                        {% for item in cart.items.all %}
                        <div class="border p-3 mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>{{ item.service.service_title }}</h6>
                                    <p class="text-muted">{{ item.service.short_description }}</p>
                                    <p><strong>Venue:</strong> {{ item.service.venue.venue_name }}</p>
                                    <p><strong>Date:</strong> {{ item.selected_date|date:"F d, Y" }}</p>
                                    <p><strong>Time:</strong> {{ item.selected_time_slot }}</p>
                                </div>
                                <div class="col-md-4 text-right">
                                    <p><strong>Quantity:</strong> {{ item.quantity }}</p>
                                    <p><strong>Price:</strong> ${{ item.price_per_item }} each</p>
                                    <p class="h6"><strong>Total:</strong> ${{ item.total_price }}</p>
                                    <form method="post" action="{% url 'booking_cart_app:remove_from_cart' item.id %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                onclick="return confirm('Remove this item from cart?')">
                                            Remove
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <div class="text-right">
                            <h5>Total: ${{ cart.total_price }}</h5>
                            <a href="{% url 'booking_cart_app:checkout' %}" class="btn btn-primary btn-lg">
                                Proceed to Checkout
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5>Your cart is empty</h5>
                            <p class="text-muted">Browse our services and add them to your cart to get started.</p>
                            <a href="/" class="btn btn-primary">
                                Browse Services
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            {% if cart.items.count > 0 %}
            <div class="card">
                <div class="card-header">
                    <h6>Cart Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <span>Items:</span>
                        <span>{{ cart.items.count }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Subtotal:</span>
                        <span>${{ cart.total_price }}</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between h6">
                        <strong>Total:</strong>
                        <strong>${{ cart.total_price }}</strong>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-body">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i>
                        Cart expires in {{ cart.expires_at|timeuntil }}
                    </small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
