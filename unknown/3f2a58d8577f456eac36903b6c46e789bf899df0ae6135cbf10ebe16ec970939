{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - CozyWish{% endblock %}

{% block extra_css %}
    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* CozyWish Dashboard App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Dashboard wrapper - clean white background */
        .dashboard-wrapper {
            background-color: white;
            min-height: 100vh;
            font-family: var(--font-primary);
        }

        /* Sidebar styling */
        .dashboard-sidebar {
            background-color: white;
            border-right: 2px solid black;
            min-height: calc(100vh - 56px);
            padding: 2rem 0;
        }

        .dashboard-content {
            padding: 2rem;
            background-color: white;
        }

        /* Navigation links */
        .dashboard-sidebar .nav-link {
            color: black;
            padding: 0.75rem 1rem;
            border-radius: 0.75rem;
            margin-bottom: 0.5rem;
            font-family: var(--font-primary);
            font-weight: 500;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .dashboard-sidebar .nav-link:hover {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-sidebar .nav-link.active {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Brand text */
        .brand-text {
            color: black;
            font-family: var(--font-heading);
            font-weight: 700;
        }

        /* Typography */
        .dashboard-wrapper h1, .dashboard-wrapper h2, .dashboard-wrapper h3,
        .dashboard-wrapper h4, .dashboard-wrapper h5, .dashboard-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .dashboard-wrapper p, .dashboard-wrapper span, .dashboard-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .dashboard-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .dashboard-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .dashboard-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .dashboard-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-outline-secondary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-outline-secondary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-success {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-success:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-danger {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-danger:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-warning {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-warning:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-info {
            background-color: white;
            color: black;
            border-color: black;
        }

        .dashboard-wrapper .btn-info:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Tables */
        .dashboard-wrapper .table {
            color: black;
            font-family: var(--font-primary);
        }

        .dashboard-wrapper .table th {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            border-bottom: 2px solid black;
            background-color: white;
        }

        .dashboard-wrapper .table td {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* Badges */
        .dashboard-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .dashboard-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .dashboard-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .dashboard-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .dashboard-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .dashboard-wrapper .badge.bg-info {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Text colors */
        .dashboard-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .dashboard-wrapper .text-primary {
            color: black !important;
        }

        .dashboard-wrapper .text-success {
            color: black !important;
        }

        .dashboard-wrapper .text-danger {
            color: black !important;
        }

        .dashboard-wrapper .text-warning {
            color: black !important;
        }

        .dashboard-wrapper .text-info {
            color: black !important;
        }

        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: none;
            z-index: 1000;
            background-color: white;
            color: black;
            border: 2px solid black;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-to-top.show {
            display: flex;
        }

        .back-to-top:hover {
            background-color: white;
            color: black;
            border-color: black;
        }

        /* Toast notifications */
        .dashboard-wrapper .toast {
            border: 2px solid black;
            border-radius: 0.75rem;
            background-color: white;
            color: black;
        }

        /* Mobile responsive */
        @media (max-width: 767.98px) {
            .dashboard-sidebar {
                position: fixed;
                top: 56px;
                left: -100%;
                width: 280px;
                height: calc(100vh - 56px);
                z-index: 1000;
                transition: left 0.3s ease;
                background-color: white;
                border-right: 2px solid black;
            }

            .dashboard-sidebar.show {
                left: 0;
            }

            .dashboard-content {
                margin-left: 0;
                padding: 1rem;
            }
        }
    </style>
    {% block dashboard_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="dashboard-wrapper">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block dashboard-sidebar">
                <div class="position-sticky pt-4">
                    <div class="text-center mb-4">
                        <h4 class="brand-text mb-0">CozyWish</h4>
                        <p class="text-muted small">Dashboard</p>
                    </div>
                    <ul class="nav flex-column px-3">
                        {% block sidebar_content %}
                        <!-- Sidebar content will be provided by child templates -->
                        {% endblock %}
                    </ul>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9 col-lg-10 dashboard-content">
                <!-- Mobile sidebar toggle -->
                <div class="d-md-none mb-3">
                    <button class="btn btn-outline-secondary sidebar-toggle" type="button">
                        <i class="fas fa-bars"></i> Menu
                    </button>
                </div>

                {% if messages %}
                <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;">
                    {% for message in messages %}
                    <div class="toast mb-2" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">{{ message }}</div>
                            <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-4">
                    <h1 class="h2 brand-text">{% block dashboard_title %}Dashboard{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {% block dashboard_actions %}
                        <!-- Dashboard actions will be provided by child templates -->
                        {% endblock %}
                    </div>
                </div>

                {% block dashboard_content %}
                <!-- Dashboard content will be provided by child templates -->
                {% endblock %}
            </div>
        </div>
    </div>
    <a href="#" id="back-to-top" class="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Common dashboard JavaScript functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Mobile sidebar toggle
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                document.querySelector('.dashboard-sidebar').classList.toggle('show');
            });
        }


        // Back to top button
        const backBtn = document.getElementById('back-to-top');
        if (backBtn) {
            window.addEventListener('scroll', function(){
                if(window.scrollY > 200){
                    backBtn.classList.add('show');
                } else {
                    backBtn.classList.remove('show');
                }
            });
            backBtn.addEventListener('click', function(e){
                e.preventDefault();
                window.scrollTo({top:0, behavior:'smooth'});
            });
        }
    });
</script>
{% block dashboard_js %}
<!-- Dashboard-specific JavaScript will be provided by child templates -->
{% endblock %}
{% endblock %}
