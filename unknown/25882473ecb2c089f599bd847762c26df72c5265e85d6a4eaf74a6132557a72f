"""Admin views for :mod:`notifications_app`."""

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db.models import Q
from django.http import Http404
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_http_methods

# --- Local Imports ---
from ..forms import AdminAnnouncementForm, NotificationFilterForm
from ..models import AdminAnnouncement, Notification
from .common import (
    is_admin_user,
    log_error,
    log_security_event,
    log_system_announcement_created,
    log_user_activity,
    performance_monitor,
)


# --- Admin Notification Views ---


@login_required
@user_passes_test(is_admin_user)
@require_http_methods(["GET"])
def admin_notification_dashboard(request):
    """Admin dashboard for managing notifications and announcements."""
    try:
        log_user_activity(
            app_name='notifications_app',
            activity_type='admin_notification_dashboard_access',
            user=request.user,
            request=request,
            details={'admin_role': 'superuser' if request.user.is_superuser else 'admin'},
        )

        total_notifications = Notification.objects.count()
        unread_notifications = Notification.objects.filter(read_status=Notification.UNREAD).count()
        pending_announcements = AdminAnnouncement.get_pending_count()

        recent_notifications = Notification.objects.select_related('user').order_by('-created_at')[:10]
        recent_announcements = AdminAnnouncement.objects.select_related('created_by').order_by('-created_at')[:5]

        notification_types = {}
        for choice in Notification.TYPE_CHOICES:
            count = Notification.objects.filter(notification_type=choice[0]).count()
            notification_types[choice[1]] = count

        context = {
            'total_notifications': total_notifications,
            'unread_notifications': unread_notifications,
            'pending_announcements': pending_announcements,
            'recent_notifications': recent_notifications,
            'recent_announcements': recent_announcements,
            'notification_types': notification_types,
        }

        return render(request, 'notifications_app/admin/dashboard.html', context)

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='admin_dashboard_error',
            error_message="Failed to load admin notification dashboard",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, 'Unable to load admin dashboard. Please try again.')
        return redirect('dashboard_app:admin_dashboard')


@login_required
@user_passes_test(is_admin_user)
@require_http_methods(["GET", "POST"])
def admin_create_announcement(request):
    """Admin view to create and send system-wide announcements."""
    try:
        if request.method == 'POST':
            form = AdminAnnouncementForm(request.POST)
            if form.is_valid():
                announcement = form.save(commit=False)
                announcement.created_by = request.user
                announcement.save()

                success = announcement.send_announcement()

                if success:
                    log_user_activity(
                        app_name='notifications_app',
                        activity_type='admin_announcement_created',
                        user=request.user,
                        request=request,
                        details={
                            'announcement_id': announcement.id,
                            'target_audience': announcement.target_audience,
                            'recipients_count': announcement.total_recipients,
                        },
                    )
                    messages.success(request, 'Announcement sent successfully.')
                else:
                    log_error(
                        app_name='notifications_app',
                        error_type='announcement_send_failed',
                        error_message='Failed to send announcement',
                        user=request.user,
                        request=request,
                        details={'announcement_id': announcement.id},
                    )
                    messages.error(request, 'Failed to send announcement. Please try again.')
            else:
                messages.error(request, 'Please correct the errors below.')
        else:
            form = AdminAnnouncementForm()

        context = {
            'form': form,
        }

        return render(request, 'notifications_app/admin/create_announcement.html', context)

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='admin_create_announcement_error',
            error_message="Failed to create announcement",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, 'Unable to create announcement. Please try again.')
        return redirect('notifications_app:admin_notification_dashboard')


@login_required
@user_passes_test(is_admin_user)
@require_http_methods(["GET"])
def admin_notification_list(request):
    """Admin view to list all notifications with filtering and search."""
    try:
        log_user_activity(
            app_name='notifications_app',
            activity_type='admin_notification_list_access',
            user=request.user,
            request=request,
        )

        notifications = Notification.objects.select_related('user').order_by('-created_at')

        search_query = request.GET.get('search', '').strip()
        if search_query:
            notifications = notifications.filter(
                Q(title__icontains=search_query) |
                Q(message__icontains=search_query) |
                Q(user__email__icontains=search_query)
            )

        filter_form = NotificationFilterForm(request.GET)
        if filter_form.is_valid():
            notification_type = filter_form.cleaned_data.get('notification_type')
            read_status = filter_form.cleaned_data.get('read_status')
            date_from = filter_form.cleaned_data.get('date_from')
            date_to = filter_form.cleaned_data.get('date_to')

            if notification_type:
                notifications = notifications.filter(notification_type=notification_type)

            if read_status == 'unread':
                notifications = notifications.filter(read_status=Notification.UNREAD)
            elif read_status == 'read':
                notifications = notifications.filter(read_status=Notification.READ)

            if date_from:
                notifications = notifications.filter(created_at__date__gte=date_from)

            if date_to:
                notifications = notifications.filter(created_at__date__lte=date_to)

        paginator = Paginator(notifications, 25)
        page = request.GET.get('page')

        try:
            notifications_page = paginator.page(page)
        except PageNotAnInteger:
            notifications_page = paginator.page(1)
        except EmptyPage:
            notifications_page = paginator.page(paginator.num_pages)

        context = {
            'notifications': notifications_page,
            'filter_form': filter_form,
            'search_query': search_query,
            'total_notifications': notifications.count(),
        }

        return render(request, 'notifications_app/admin/notification_list.html', context)

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='admin_notification_list_error',
            error_message="Failed to load admin notification list",
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, 'Unable to load notification list. Please try again.')
        return redirect('notifications_app:admin_notification_dashboard')


@login_required
@user_passes_test(is_admin_user)
@require_http_methods(["GET"])
def admin_notification_detail(request, notification_id):
    """Admin view to see detailed information about a specific notification."""
    try:
        notification = get_object_or_404(Notification.objects.select_related('user'), id=notification_id)

        log_user_activity(
            app_name='notifications_app',
            activity_type='admin_notification_detail_access',
            user=request.user,
            request=request,
            details={
                'notification_id': notification.id,
                'notification_user': notification.user.email,
            },
        )

        context = {
            'notification': notification,
        }

        return render(request, 'notifications_app/admin/notification_detail.html', context)

    except Http404:
        log_security_event(
            app_name='notifications_app',
            event_type='admin_notification_not_found',
            user_email=request.user.email,
            request=request,
            details={'attempted_notification_id': notification_id},
        )
        messages.error(request, 'Notification not found.')
        return redirect('notifications_app:admin_notification_list')

    except Exception as e:
        log_error(
            app_name='notifications_app',
            error_type='admin_notification_detail_error',
            error_message="Failed to load admin notification detail",
            user=request.user,
            request=request,
            exception=e,
            details={'notification_id': notification_id},
        )
        messages.error(request, 'Unable to load notification details. Please try again.')
        return redirect('notifications_app:admin_notification_list')

