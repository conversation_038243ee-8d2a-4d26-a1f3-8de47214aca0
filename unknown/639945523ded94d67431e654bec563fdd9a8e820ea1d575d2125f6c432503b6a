# Django imports
from django import forms
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Local imports
from ..models import ServiceAvailability
from ..form_mixins import AvailabilityValidationMixin


class ServiceAvailabilityForm(AvailabilityValidationMixin, forms.ModelForm):
    """Form for managing service availability."""

    class Meta:
        model = ServiceAvailability
        fields = [
            "available_date",
            "start_time",
            "end_time",
            "max_bookings",
            "is_available",
        ]
        widgets = {
            "available_date": forms.DateInput(
                attrs={"class": "form-control", "type": "date", "min": timezone.now().date().isoformat()}
            ),
            "start_time": forms.TimeInput(attrs={"class": "form-control", "type": "time"}),
            "end_time": forms.TimeInput(attrs={"class": "form-control", "type": "time"}),
            "max_bookings": forms.NumberInput(attrs={"class": "form-control", "min": 1, "max": 20}),
            "is_available": forms.CheckboxInput(attrs={"class": "form-check-input"}),
        }
        labels = {
            "available_date": _("Date"),
            "start_time": _("Start Time"),
            "end_time": _("End Time"),
            "max_bookings": _("Maximum Bookings"),
            "is_available": _("Available"),
        }
        help_texts = {
            "available_date": _("Date when service is available"),
            "start_time": _("Start time of availability slot"),
            "end_time": _("End time of availability slot"),
            "max_bookings": _("Maximum number of bookings for this slot"),
            "is_available": _("Whether this slot is available for booking"),
        }

    def clean(self):
        cleaned_data = super().clean()
        available_date = cleaned_data.get("available_date")
        start_time = cleaned_data.get("start_time")
        end_time = cleaned_data.get("end_time")

        # Validate future date with field-specific error
        if available_date and available_date <= timezone.now().date():
            self.add_error('available_date', _('Date must be in the future.'))

        # Validate start/end time with field-specific error
        if start_time and end_time and end_time <= start_time:
            self.add_error('end_time', _('End time must be after start time.'))

        return cleaned_data

    def save(self, service=None, commit=True):
        """Save the service availability with the provided service."""
        availability = super().save(commit=False)

        if service:
            availability.service = service

        # If is_available was not provided in form data, default to True
        if 'is_available' not in self.data:
            availability.is_available = True

        if commit:
            availability.save()

        return availability


class DateRangeAvailabilityForm(AvailabilityValidationMixin, forms.Form):
    """Form for setting availability for a date range."""

    start_date = forms.DateField(
        label=_("Start Date"),
        widget=forms.DateInput(
            attrs={"class": "form-control", "type": "date", "min": timezone.now().date().isoformat()}
        ),
        help_text=_("First date to set availability for"),
    )
    end_date = forms.DateField(
        label=_("End Date"),
        widget=forms.DateInput(
            attrs={"class": "form-control", "type": "date", "min": timezone.now().date().isoformat()}
        ),
        help_text=_("Last date to set availability for"),
    )
    start_time = forms.TimeField(
        label=_("Start Time"),
        widget=forms.TimeInput(attrs={"class": "form-control", "type": "time"}),
        help_text=_("Start time for availability slots"),
    )
    end_time = forms.TimeField(
        label=_("End Time"),
        widget=forms.TimeInput(attrs={"class": "form-control", "type": "time"}),
        help_text=_("End time for availability slots"),
    )
    interval = forms.IntegerField(
        label=_("Time Interval (minutes)"),
        widget=forms.NumberInput(
            attrs={"class": "form-control", "min": 15, "max": 120, "step": 15}
        ),
        help_text=_("Time interval in minutes (e.g., 30 for half-hour slots)"),
        initial=60,
    )
    max_bookings = forms.IntegerField(
        label=_("Maximum Bookings per Slot"),
        widget=forms.NumberInput(attrs={"class": "form-control", "min": 1, "max": 20}),
        help_text=_("Maximum number of bookings per time slot"),
        initial=1,
    )
    is_available = forms.BooleanField(
        label=_("Available"),
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
        required=False,
        initial=True,
        help_text=_("Whether these slots are available for booking"),
    )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        start_time = cleaned_data.get("start_time")
        end_time = cleaned_data.get("end_time")

        if not all([start_date, end_date, start_time, end_time]):
            return cleaned_data

        self.validate_future_date(start_date)
        if end_date < start_date:
            raise ValidationError(_("End date must be after start date."))

        self.validate_start_end(start_time, end_time)

        return cleaned_data


class BookingActionForm(forms.Form):
    """Form for provider booking actions."""

    action_reason = forms.CharField(
        label=_("Reason (Optional)"),
        widget=forms.Textarea(
            attrs={
                "class": "form-control",
                "rows": 3,
                "placeholder": "Provide a reason for this action (optional)",
                "maxlength": 500,
            }
        ),
        required=False,
        help_text=_("Maximum 500 characters"),
    )

    def clean_action_reason(self):
        reason = self.cleaned_data.get("action_reason", "")
        return reason.strip()
