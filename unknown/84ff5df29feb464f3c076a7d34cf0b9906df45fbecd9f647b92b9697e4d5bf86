"""Shared helpers and constants for ``venues_app`` views."""

# --- Django Imports ---
from django.contrib import messages
from django.shortcuts import redirect


MAX_SERVICES_PER_VENUE = 7
MAX_FAQS_PER_VENUE = 5


class ServiceProviderRequiredMixin:
    """Ensure that only authenticated service providers can access a view."""

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts_app:provider_login')
        if not hasattr(request.user, 'service_provider_profile'):
            messages.error(request, 'Only service providers can access this page.')
            return redirect('home')
        return super().dispatch(request, *args, **kwargs)

