from django.test import TestCase, RequestFactory
from unittest.mock import patch

from review_app.logging_utils import performance_monitor

class PerformanceMonitorTest(TestCase):
    def setUp(self):
        self.factory = RequestFactory()

    def test_monitor_logs_duration(self):
        with patch('review_app.logging_utils.log_performance') as mock_log:
            @performance_monitor('op')
            def sample(request):
                return 'ok'

            req = self.factory.get('/')
            req.user = type('U', (), {'is_authenticated': True})()
            result = sample(req)
            self.assertEqual(result, 'ok')
            mock_log.assert_called_once()
            self.assertEqual(mock_log.call_args[1]['operation'], 'op')

