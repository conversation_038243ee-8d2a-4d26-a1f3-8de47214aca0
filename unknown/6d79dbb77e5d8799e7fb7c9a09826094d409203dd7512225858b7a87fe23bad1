"""Unit tests for admin dashboard views."""

from decimal import Decimal
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone

from accounts_app.models import CustomUser, ServiceProviderProfile
from venues_app.models import Venue, Service, Category
from booking_cart_app.models import Booking, BookingItem


class AdminViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.admin = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='pass123',
            role=CustomUser.ADMIN,
            is_staff=True,
            is_superuser=True,
        )

        provider = CustomUser.objects.create_user(
            email='<EMAIL>',
            password='pass123',
            role=CustomUser.SERVICE_PROVIDER,
        )
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=provider,
            business_name='Test Biz',
            business_phone_number='+**********',
            business_address='123 St',
            city='City',
            state='ST',
            zip_code='12345',
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Venue',
            short_description='desc',
            state='ST',
            county='County',
            city='City',
            street_number='1',
            street_name='Street',
            operating_hours='9-5',
            approval_status='approved',
            visibility='active',
        )
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Test Service',
            short_description='sdesc',
            price_min=Decimal('10.00'),
            price_max=Decimal('20.00'),
            duration_minutes=30,
            is_active=True,
        )

        today = timezone.now().date()
        booking = Booking.objects.create(
            customer=self.admin,
            venue=self.venue,
            booking_date=today,
            status='confirmed',
            total_price=Decimal('15.00'),
        )
        BookingItem.objects.create(
            booking=booking,
            service=self.service,
            scheduled_date=today,
            scheduled_time=timezone.now().time(),
            service_price=Decimal('15.00'),
            duration_minutes=30,
            quantity=1,
        )

    def test_platform_overview_view(self):
        self.client.login(email='<EMAIL>', password='pass123')
        response = self.client.get(reverse('dashboard_app:admin_platform_overview'), secure=True)
        self.assertIn(response.status_code, [200, 302])
        if response.status_code == 200:
            self.assertIn('total_users', response.context)

    def test_revenue_tracking_view(self):
        self.client.login(email='<EMAIL>', password='pass123')
        response = self.client.get(reverse('dashboard_app:admin_revenue_tracking'), secure=True)
        self.assertIn(response.status_code, [200, 302])
        if response.status_code == 200:
            self.assertIn('total_revenue', response.context)

    def test_revenue_export_view(self):
        self.client.login(email='<EMAIL>', password='pass123')
        response = self.client.get(reverse('dashboard_app:admin_revenue_export'), secure=True)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')

