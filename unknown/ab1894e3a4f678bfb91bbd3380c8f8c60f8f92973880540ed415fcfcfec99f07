"""Forms for venue search and filtering."""

# --- Django Imports ---
from django import forms
from django.db.models import Q

# --- Local App Imports ---
from ..models import Category, USCity


class VenueSearchForm(forms.Form):
    """Form for searching venues with keyword, location, and category filters."""

    query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'search-input form-control ps-5',
            'placeholder': 'Search venues, services, or categories...',
            'autocomplete': 'off',
            'data-bs-toggle': 'dropdown',
            'aria-expanded': 'false',
            'aria-label': 'Search keywords',
        })
    )

    location = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'search-input form-control ps-5',
            'placeholder': 'City, State, or County',
            'autocomplete': 'off',
            'data-bs-toggle': 'dropdown',
            'aria-expanded': 'false',
            'aria-label': 'Location',
            'list': 'locationList',
        })
    )

    category = forms.ModelChoiceField(
        queryset=None,
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={
            'class': 'form-select',
            'aria-label': 'Category filter',
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from venues_app.models import Category
        self.fields['category'].queryset = Category.objects.filter(is_active=True).order_by('category_name')

    def get_location_suggestions(self, query):
        if not query or len(query) < 2:
            return []

        suggestions = []
        cities = USCity.objects.filter(
            Q(city__icontains=query) |
            Q(state_name__icontains=query) |
            Q(county_name__icontains=query)
        ).distinct()[:10]

        for city in cities:
            suggestions.append({
                'label': f"{city.city}, {city.state_id}",
                'value': f"{city.city}, {city.state_id}",
                'type': 'city',
            })
        return suggestions


class VenueFilterForm(forms.Form):
    """Form for advanced filtering and sorting of venue search results."""

    SORT_CHOICES = [
        ('', 'Sort by'),
        ('rating_high', 'Rating: High to Low'),
        ('rating_low', 'Rating: Low to High'),
        ('price_high', 'Price: High to Low'),
        ('price_low', 'Price: Low to High'),
        ('discount', 'Discount: High to Low'),
        ('newest', 'Newest First'),
        ('name', 'Name A-Z'),
    ]

    VENUE_TYPE_CHOICES = [
        ('', 'All Types'),
        ('all', 'All Genders'),
        ('male', 'Male Only'),
        ('female', 'Female Only'),
    ]

    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': 'Sort by'})
    )

    venue_type = forms.ChoiceField(
        choices=VENUE_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': 'Venue type'})
    )

    has_discount = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )



    state = forms.ChoiceField(
        required=False,
        choices=[],
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': 'State'})
    )

    county = forms.ChoiceField(
        required=False,
        choices=[],
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': 'County'})
    )

    city = forms.ChoiceField(
        required=False,
        choices=[],
        widget=forms.Select(attrs={'class': 'form-select', 'aria-label': 'City'})
    )

    categories = forms.ModelMultipleChoiceField(
        queryset=None,
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['categories'].queryset = Category.objects.filter(is_active=True).order_by('category_name')

        states = list(self.get_states())
        state = self.data.get('state') or self.initial.get('state')
        if state and state not in states:
            states.append(state)
        self.fields['state'].choices = [('', 'Select state')] + [(s, s) for s in states]

        counties = list(self.get_counties(state)) if state else []
        county = self.data.get('county') or self.initial.get('county')
        if county and county not in counties:
            counties.append(county)
        self.fields['county'].choices = [('', 'Select county')] + [(c, c) for c in counties]

        cities = list(self.get_cities(state, county)) if state else []
        city = self.data.get('city') or self.initial.get('city')
        if city and city not in cities:
            cities.append(city)
        self.fields['city'].choices = [('', 'Select city')] + [(c, c) for c in cities]

    def get_states(self):
        return USCity.objects.values_list('state_name', flat=True).distinct().order_by('state_name')

    def get_counties(self, state=None):
        counties = USCity.objects.values_list('county_name', flat=True)
        if state:
            counties = counties.filter(state_name__iexact=state)
        return counties.distinct().order_by('county_name')

    def get_cities(self, state=None, county=None):
        cities = USCity.objects.values_list('city', flat=True)
        if state:
            cities = cities.filter(state_name__iexact=state)
        if county:
            cities = cities.filter(county_name__iexact=county)
        return cities.distinct().order_by('city')
