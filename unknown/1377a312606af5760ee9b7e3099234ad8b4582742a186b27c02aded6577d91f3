# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ImageUploadForm
    from utils.image_service import ImageService
except ImportError:
    ImageUploadForm = None  # type: ignore
    ImageService = None     # type: ignore

# --- Local App Imports ---
from ..logging_utils import log_error
from ..models import TeamMember
from .common import AccessibleFormMixin


# --- Team Member Form ---
class TeamMemberForm(AccessibleFormMixin, forms.ModelForm):
    """
    Form for adding or editing a service provider's team member.

    Features:
    - Staff name and position fields
    - Optional profile picture upload with processing
    - Active status toggle for team members
    """
    staff_name = forms.CharField(
        label=_('Staff Name'),
        max_length=100,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter staff member name'),
            }
        )
    )

    staff_position = forms.CharField(
        label=_('Position/Title'),
        max_length=100,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('e.g., Massage Therapist, Esthetician'),
            }
        )
    )

    staff_profile_picture = forms.ImageField(
        label=_('Profile Picture'),
        required=False,
        widget=forms.FileInput(
            attrs={
                'class': 'form-control',
                'accept': 'image/jpeg,image/png',
            }
        ),
        help_text=_(
            'Upload staff member photo (JPG or PNG). '
            'Image will be automatically resized and optimized.'
        )
    )

    is_active = forms.BooleanField(
        label=_('Active'),
        required=False,
        initial=True,
        widget=forms.CheckboxInput(
            attrs={'class': 'form-check-input'}
        ),
        help_text=_('Uncheck to deactivate this team member')
    )

    class Meta:
        model = TeamMember
        fields = [
            'is_active',
        ]

    def __init__(self, *args, **kwargs):
        """Initialize form and populate custom fields from instance data."""
        super().__init__(*args, **kwargs)

        # If we have an instance (editing), populate the custom fields
        if self.instance and self.instance.pk:
            self.fields['staff_name'].initial = self.instance.name
            self.fields['staff_position'].initial = self.instance.position

    def clean_staff_profile_picture(self) -> forms.ImageField:
        """
        Validate and optionally process a new staff profile picture upload.

        Returns:
            The validated image file or None if not provided.

        Raises:
            ValidationError: On invalid or unsupported image.
        """
        picture = self.cleaned_data.get('staff_profile_picture')
        if not picture:
            return None  # type: ignore

        # Skip validation for existing images
        if hasattr(picture, 'url') and not hasattr(picture, 'content_type'):
            return picture

        # Basic validation: size and format checks
        if hasattr(picture, 'content_type'):
            if picture.size > 5 * 1024 * 1024:
                raise ValidationError(
                    _('Image file too large. Maximum size is 5MB.'),
                    code='file_too_large'
                )
            if picture.content_type not in ('image/jpeg', 'image/png'):
                raise ValidationError(
                    _('Invalid image format. Please use JPG or PNG only.'),
                    code='invalid_format'
                )

        return picture

    def save(self, commit: bool = True) -> TeamMember:
        """
        Save the team member instance, processing the profile picture if needed.

        Returns:
            The saved TeamMember instance.
        """
        team_member = super().save(commit=False)

        # Map form fields to model fields - only if they're provided in form data
        if 'staff_name' in self.cleaned_data:
            team_member.name = self.cleaned_data.get('staff_name')
        if 'staff_position' in self.cleaned_data:
            team_member.position = self.cleaned_data.get('staff_position')

        picture = self.cleaned_data.get('staff_profile_picture')

        if picture and hasattr(picture, 'content_type'):
            # For now, save the image directly to avoid complex processing issues
            # Image processing can be added later as a separate background task
            team_member.photo = picture

        if commit:
            team_member.save()

        return team_member
