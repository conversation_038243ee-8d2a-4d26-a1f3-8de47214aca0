"""
Unit tests for discount_app utility functions.

This module contains comprehensive unit tests for all utility functions in the discount_app,
including discount calculations, eligibility checks, and usage recording.
"""

# Standard library imports
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone

# Local imports
from discount_app.utils import (
    get_applicable_discounts, get_best_discount, record_discount_usage,
    calculate_cart_discounts, get_venue_discount_summary, format_discount_display,
    validate_discount_eligibility
)
from discount_app.models import (
    DiscountType, VenueDiscount, ServiceDiscount, PlatformDiscount, DiscountUsage
)
from venues_app.models import Category, Venue, Service
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


class DiscountUtilsBaseTest(TestCase):
    """Base test class for discount utility functions."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer_user.role = User.CUSTOMER
        self.customer_user.save()

        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider_user.role = User.SERVICE_PROVIDER
        self.provider_user.save()

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.admin_user.role = User.ADMIN
        self.admin_user.save()

        # Create category
        self.category = Category.objects.create(
            name='Spa Services',
            description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            business_name='Test Spa',
            business_phone_number='+**********',
            contact_person_name='John Doe',
            business_address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            venue_address='123 Spa St',
            city='New York',
            state='NY',
            zip_code='10001',
            phone_number='+**********',
            category=self.category,
            is_approved=True
        )

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Massage Therapy',
            service_description='Relaxing massage',
            price_min=Decimal('100.00'),
            price_max=Decimal('200.00'),
            duration_minutes=60,
            is_active=True
        )


class GetApplicableDiscountsTest(DiscountUtilsBaseTest):
    """Test the get_applicable_discounts utility function."""

    def test_get_applicable_discounts_no_discounts(self):
        """Test get_applicable_discounts with no available discounts."""
        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 0)

    def test_get_applicable_discounts_service_discount(self):
        """Test get_applicable_discounts with service discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Massage Special',
            description='Special discount for massage therapy',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 1)
        
        discount, discount_amount, final_price = discounts[0]
        self.assertEqual(discount, service_discount)
        self.assertEqual(discount_amount, Decimal('20.00'))  # 20% of 100
        self.assertEqual(final_price, Decimal('80.00'))

    def test_get_applicable_discounts_venue_discount(self):
        """Test get_applicable_discounts with venue discount."""
        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name='Venue Grand Opening',
            description='Special discount for grand opening',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal('50.00'),
            created_by=self.provider_user,
            is_approved=True
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 1)
        
        discount, discount_amount, final_price = discounts[0]
        self.assertEqual(discount, venue_discount)
        self.assertEqual(discount_amount, Decimal('15.00'))  # 15% of 100
        self.assertEqual(final_price, Decimal('85.00'))

    def test_get_applicable_discounts_venue_discount_min_booking_not_met(self):
        """Test get_applicable_discounts with venue discount min booking not met."""
        VenueDiscount.objects.create(
            venue=self.venue,
            name='High Value Discount',
            description='Discount for high value bookings',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('25.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal('150.00'),  # Higher than service price
            created_by=self.provider_user,
            is_approved=True
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 0)

    def test_get_applicable_discounts_platform_discount(self):
        """Test get_applicable_discounts with platform discount."""
        platform_discount = PlatformDiscount.objects.create(
            name='Summer Sale',
            description='Platform-wide summer discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('10.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            category=self.category,
            min_booking_value=Decimal('50.00'),
            created_by=self.admin_user
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 1)
        
        discount, discount_amount, final_price = discounts[0]
        self.assertEqual(discount, platform_discount)
        self.assertEqual(discount_amount, Decimal('10.00'))  # 10% of 100
        self.assertEqual(final_price, Decimal('90.00'))

    def test_get_applicable_discounts_platform_discount_all_categories(self):
        """Test get_applicable_discounts with platform discount for all categories."""
        platform_discount = PlatformDiscount.objects.create(
            name='Universal Sale',
            description='Platform-wide discount for all categories',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('5.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            category=None,  # All categories
            min_booking_value=Decimal('0.00'),
            created_by=self.admin_user
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 1)
        
        discount, discount_amount, final_price = discounts[0]
        self.assertEqual(discount, platform_discount)

    def test_get_applicable_discounts_multiple_discounts_sorted(self):
        """Test get_applicable_discounts with multiple discounts sorted by final price."""
        # Create multiple discounts with different values
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Service Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('30.00'),  # Best discount
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name='Venue Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),  # Medium discount
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal('50.00'),
            created_by=self.provider_user,
            is_approved=True
        )

        platform_discount = PlatformDiscount.objects.create(
            name='Platform Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('10.00'),  # Lowest discount
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            category=self.category,
            min_booking_value=Decimal('0.00'),
            created_by=self.admin_user
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 3)
        
        # Should be sorted by final price (lowest first)
        self.assertEqual(discounts[0][0], service_discount)  # 70.00 final price
        self.assertEqual(discounts[1][0], venue_discount)   # 80.00 final price
        self.assertEqual(discounts[2][0], platform_discount) # 90.00 final price

    def test_get_applicable_discounts_unapproved_discount_excluded(self):
        """Test get_applicable_discounts excludes unapproved discounts."""
        ServiceDiscount.objects.create(
            service=self.service,
            name='Unapproved Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('50.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False  # Not approved
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 0)

    def test_get_applicable_discounts_expired_discount_excluded(self):
        """Test get_applicable_discounts excludes expired discounts."""
        ServiceDiscount.objects.create(
            service=self.service,
            name='Expired Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('30.00'),
            start_date=timezone.now() - timedelta(days=7),
            end_date=timezone.now() - timedelta(hours=1),  # Expired
            created_by=self.provider_user,
            is_approved=True
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 0)

    def test_get_applicable_discounts_future_discount_excluded(self):
        """Test get_applicable_discounts excludes future discounts."""
        ServiceDiscount.objects.create(
            service=self.service,
            name='Future Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('30.00'),
            start_date=timezone.now() + timedelta(hours=1),  # Future
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 0)

    def test_get_applicable_discounts_venue_discount_with_max_cap(self):
        """Test get_applicable_discounts with venue discount that has max discount cap."""
        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name='Capped Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('50.00'),  # 50% would be $50
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal('0.00'),
            max_discount_amount=Decimal('30.00'),  # Capped at $30
            created_by=self.provider_user,
            is_approved=True
        )

        discounts = get_applicable_discounts(self.service, self.customer_user)
        self.assertEqual(len(discounts), 1)
        
        discount, discount_amount, final_price = discounts[0]
        self.assertEqual(discount, venue_discount)
        self.assertEqual(discount_amount, Decimal('30.00'))  # Capped at max
        self.assertEqual(final_price, Decimal('70.00'))  # 100 - 30

    @patch('discount_app.utils.log_discount_calculation_performance')
    @patch('discount_app.utils.log_discount_error')
    def test_get_applicable_discounts_handles_exceptions(self, mock_log_error, mock_log_performance):
        """Test get_applicable_discounts handles exceptions gracefully."""
        # Mock an exception in the service attribute access
        with patch.object(self.service, 'price_min', side_effect=Exception("Test error")):
            discounts = get_applicable_discounts(self.service, self.customer_user)
            self.assertEqual(len(discounts), 0)
            mock_log_error.assert_called_once()


class GetBestDiscountTest(DiscountUtilsBaseTest):
    """Test the get_best_discount utility function."""

    def test_get_best_discount_no_discounts(self):
        """Test get_best_discount with no available discounts."""
        best_discount = get_best_discount(self.service, self.customer_user)
        self.assertIsNone(best_discount)

    def test_get_best_discount_single_discount(self):
        """Test get_best_discount with single discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Only Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        best_discount = get_best_discount(self.service, self.customer_user)
        self.assertIsNotNone(best_discount)
        
        discount, discount_amount, final_price = best_discount
        self.assertEqual(discount, service_discount)
        self.assertEqual(final_price, Decimal('80.00'))

    def test_get_best_discount_multiple_discounts(self):
        """Test get_best_discount returns the best (lowest final price) discount."""
        # Create multiple discounts
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Best Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('40.00'),  # Best discount
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name='Venue Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal('50.00'),
            created_by=self.provider_user,
            is_approved=True
        )

        best_discount = get_best_discount(self.service, self.customer_user)
        self.assertIsNotNone(best_discount)

        discount, discount_amount, final_price = best_discount
        self.assertEqual(discount, service_discount)  # Should be the best one
        self.assertEqual(final_price, Decimal('60.00'))  # 100 - 40


class RecordDiscountUsageTest(DiscountUtilsBaseTest):
    """Test the record_discount_usage utility function."""

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # Create a service discount for testing
        self.service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Test Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        # Mock booking object
        self.mock_booking = Mock()
        self.mock_booking.id = 123

    @patch('discount_app.utils.log_user_activity')
    def test_record_discount_usage_service_discount(self, mock_log_activity):
        """Test record_discount_usage with service discount."""
        original_price = Decimal('100.00')
        discount_amount = Decimal('20.00')
        final_price = Decimal('80.00')

        usage = record_discount_usage(
            self.service_discount,
            self.customer_user,
            self.mock_booking,
            original_price,
            discount_amount,
            final_price
        )

        self.assertIsInstance(usage, DiscountUsage)
        self.assertEqual(usage.user, self.customer_user)
        self.assertEqual(usage.discount_type, 'ServiceDiscount')
        self.assertEqual(usage.discount_id, self.service_discount.id)
        self.assertEqual(usage.original_price, original_price)
        self.assertEqual(usage.discount_amount, discount_amount)
        self.assertEqual(usage.final_price, final_price)

        # Check that logging was called
        mock_log_activity.assert_called_once()

    @patch('discount_app.utils.log_user_activity')
    def test_record_discount_usage_venue_discount(self, mock_log_activity):
        """Test record_discount_usage with venue discount."""
        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name='Venue Test Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        usage = record_discount_usage(
            venue_discount,
            self.customer_user,
            self.mock_booking,
            Decimal('100.00'),
            Decimal('15.00'),
            Decimal('85.00')
        )

        self.assertEqual(usage.discount_type, 'VenueDiscount')
        self.assertEqual(usage.discount_id, venue_discount.id)

    @patch('discount_app.utils.log_user_activity')
    def test_record_discount_usage_platform_discount(self, mock_log_activity):
        """Test record_discount_usage with platform discount."""
        platform_discount = PlatformDiscount.objects.create(
            name='Platform Test Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('10.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            created_by=self.admin_user
        )

        usage = record_discount_usage(
            platform_discount,
            self.customer_user,
            self.mock_booking,
            Decimal('100.00'),
            Decimal('10.00'),
            Decimal('90.00')
        )

        self.assertEqual(usage.discount_type, 'PlatformDiscount')
        self.assertEqual(usage.discount_id, platform_discount.id)

    @patch('discount_app.utils.log_discount_error')
    def test_record_discount_usage_invalid_discount_type(self, mock_log_error):
        """Test record_discount_usage with invalid discount type."""
        invalid_discount = Mock()
        invalid_discount.id = 999

        with self.assertRaises(ValueError):
            record_discount_usage(
                invalid_discount,
                self.customer_user,
                self.mock_booking,
                Decimal('100.00'),
                Decimal('10.00'),
                Decimal('90.00')
            )

    @patch('discount_app.utils.log_discount_error')
    def test_record_discount_usage_handles_exceptions(self, mock_log_error):
        """Test record_discount_usage handles exceptions gracefully."""
        # Mock DiscountUsage.objects.create to raise an exception
        with patch('discount_app.utils.DiscountUsage.objects.create', side_effect=Exception("Database error")):
            with self.assertRaises(Exception):
                record_discount_usage(
                    self.service_discount,
                    self.customer_user,
                    self.mock_booking,
                    Decimal('100.00'),
                    Decimal('20.00'),
                    Decimal('80.00')
                )

            mock_log_error.assert_called_once()


class FormatDiscountDisplayTest(DiscountUtilsBaseTest):
    """Test the format_discount_display utility function."""

    def test_format_discount_display_percentage(self):
        """Test format_discount_display with percentage discount."""
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Test Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('25.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )

        formatted = format_discount_display(discount)
        self.assertEqual(formatted, "25.0% off")

    def test_format_discount_display_fixed_amount(self):
        """Test format_discount_display with fixed amount discount."""
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Test Discount',
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('30.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )

        formatted = format_discount_display(discount)
        self.assertEqual(formatted, "$30.0 off")

    def test_format_discount_display_none(self):
        """Test format_discount_display with None discount."""
        formatted = format_discount_display(None)
        self.assertEqual(formatted, "")


class ValidateDiscountEligibilityTest(DiscountUtilsBaseTest):
    """Test the validate_discount_eligibility utility function."""

    def setUp(self):
        """Set up test data."""
        super().setUp()

        # Create a service discount for testing
        self.service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Test Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

    @patch('discount_app.utils.log_user_activity')
    def test_validate_discount_eligibility_valid(self, mock_log_activity):
        """Test validate_discount_eligibility with valid discount."""
        is_eligible, reason = validate_discount_eligibility(
            self.service_discount,
            self.service,
            self.customer_user
        )

        self.assertTrue(is_eligible)
        self.assertEqual(reason, "Eligible")
        mock_log_activity.assert_called_once()

    @patch('discount_app.utils.log_user_activity')
    def test_validate_discount_eligibility_not_started(self, mock_log_activity):
        """Test validate_discount_eligibility with discount not started."""
        future_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Future Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        is_eligible, reason = validate_discount_eligibility(
            future_discount,
            self.service,
            self.customer_user
        )

        self.assertFalse(is_eligible)
        self.assertEqual(reason, "Discount has not started yet")
        mock_log_activity.assert_called_once()

    @patch('discount_app.utils.log_user_activity')
    def test_validate_discount_eligibility_expired(self, mock_log_activity):
        """Test validate_discount_eligibility with expired discount."""
        expired_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Expired Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(days=7),
            end_date=timezone.now() - timedelta(hours=1),
            created_by=self.provider_user,
            is_approved=True
        )

        is_eligible, reason = validate_discount_eligibility(
            expired_discount,
            self.service,
            self.customer_user
        )

        self.assertFalse(is_eligible)
        self.assertEqual(reason, "Discount has expired")
        mock_log_activity.assert_called_once()

    @patch('discount_app.utils.log_user_activity')
    def test_validate_discount_eligibility_not_approved(self, mock_log_activity):
        """Test validate_discount_eligibility with unapproved discount."""
        unapproved_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Unapproved Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False
        )

        is_eligible, reason = validate_discount_eligibility(
            unapproved_discount,
            self.service,
            self.customer_user
        )

        self.assertFalse(is_eligible)
        self.assertEqual(reason, "Discount is not approved")
        mock_log_activity.assert_called_once()

    @patch('discount_app.utils.log_user_activity')
    def test_validate_discount_eligibility_min_booking_not_met(self, mock_log_activity):
        """Test validate_discount_eligibility with min booking value not met."""
        venue_discount = VenueDiscount.objects.create(
            venue=self.venue,
            name='High Value Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            min_booking_value=Decimal('200.00'),  # Higher than service price
            created_by=self.provider_user,
            is_approved=True
        )

        is_eligible, reason = validate_discount_eligibility(
            venue_discount,
            self.service,
            self.customer_user
        )

        self.assertFalse(is_eligible)
        self.assertIn("Minimum booking value", reason)
        mock_log_activity.assert_called_once()

    @patch('discount_app.utils.log_discount_error')
    def test_validate_discount_eligibility_handles_exceptions(self, mock_log_error):
        """Test validate_discount_eligibility handles exceptions gracefully."""
        # Mock an exception in the discount attribute access
        with patch.object(self.service_discount, 'start_date', side_effect=Exception("Test error")):
            is_eligible, reason = validate_discount_eligibility(
                self.service_discount,
                self.service,
                self.customer_user
            )

            self.assertFalse(is_eligible)
            self.assertEqual(reason, "Validation error occurred")
            mock_log_error.assert_called_once()


class GetVenueDiscountSummaryTest(DiscountUtilsBaseTest):
    """Test the get_venue_discount_summary utility function."""

    def test_get_venue_discount_summary_no_discounts(self):
        """Test get_venue_discount_summary with no discounts."""
        summary = get_venue_discount_summary(self.venue)

        self.assertEqual(summary['service_discounts_count'], 0)
        self.assertEqual(summary['venue_discounts_count'], 0)
        self.assertEqual(summary['platform_discounts_count'], 0)
        self.assertEqual(summary['total_discounts'], 0)
        self.assertFalse(summary['has_discounts'])

    def test_get_venue_discount_summary_with_discounts(self):
        """Test get_venue_discount_summary with various discounts."""
        # Create service discount
        ServiceDiscount.objects.create(
            service=self.service,
            name='Service Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        # Create venue discount
        VenueDiscount.objects.create(
            venue=self.venue,
            name='Venue Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        # Create platform discount
        PlatformDiscount.objects.create(
            name='Platform Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('10.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            created_by=self.admin_user
        )

        summary = get_venue_discount_summary(self.venue)

        self.assertEqual(summary['service_discounts_count'], 1)
        self.assertEqual(summary['venue_discounts_count'], 1)
        self.assertEqual(summary['platform_discounts_count'], 1)
        self.assertEqual(summary['total_discounts'], 3)
        self.assertTrue(summary['has_discounts'])

    def test_get_venue_discount_summary_excludes_inactive_services(self):
        """Test get_venue_discount_summary excludes discounts for inactive services."""
        # Create inactive service
        inactive_service = Service.objects.create(
            venue=self.venue,
            service_title='Inactive Service',
            service_description='Inactive service',
            price_min=Decimal('50.00'),
            price_max=Decimal('100.00'),
            duration_minutes=30,
            is_active=False  # Inactive
        )

        # Create discount for inactive service
        ServiceDiscount.objects.create(
            service=inactive_service,
            name='Discount for Inactive Service',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        summary = get_venue_discount_summary(self.venue)
        self.assertEqual(summary['service_discounts_count'], 0)  # Should exclude inactive service

    def test_get_venue_discount_summary_excludes_unapproved_discounts(self):
        """Test get_venue_discount_summary excludes unapproved discounts."""
        # Create unapproved service discount
        ServiceDiscount.objects.create(
            service=self.service,
            name='Unapproved Service Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False  # Not approved
        )

        # Create unapproved venue discount
        VenueDiscount.objects.create(
            venue=self.venue,
            name='Unapproved Venue Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False  # Not approved
        )

        summary = get_venue_discount_summary(self.venue)
        self.assertEqual(summary['service_discounts_count'], 0)
        self.assertEqual(summary['venue_discounts_count'], 0)

    def test_get_venue_discount_summary_excludes_expired_discounts(self):
        """Test get_venue_discount_summary excludes expired discounts."""
        # Create expired service discount
        ServiceDiscount.objects.create(
            service=self.service,
            name='Expired Service Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(days=7),
            end_date=timezone.now() - timedelta(hours=1),  # Expired
            created_by=self.provider_user,
            is_approved=True
        )

        summary = get_venue_discount_summary(self.venue)
        self.assertEqual(summary['service_discounts_count'], 0)
