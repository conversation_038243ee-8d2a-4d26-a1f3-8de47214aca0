"""Forms for venue service management."""

# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import Service

# --- Utilities ---
try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class ServiceForm(forms.ModelForm):
    """Form for creating and editing venue services."""

    class Meta:
        model = Service
        fields = [
            'service_title', 'short_description', 'price_min',
            'price_max', 'duration_minutes', 'is_active'
        ]
        widgets = {
            'service_title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Service name',
            }),
            'short_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Brief description of the service (max 500 characters)',
                'maxlength': 500,
            }),
            'price_min': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01',
                'placeholder': 'Minimum price',
            }),
            'price_max': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01',
                'placeholder': 'Maximum price (optional)',
            }),
            'duration_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '1440',
                'placeholder': 'Duration in minutes',
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'style': 'transform: scale(1.2);',
            }),
        }
        help_texts = {
            'price_max': _('Leave blank if service has fixed pricing'),
            'duration_minutes': _('Service duration in minutes (max 24 hours)'),
            'is_active': _('Uncheck to temporarily disable this service for customers'),
        }

    def clean_short_description(self):
        return sanitize_html(self.cleaned_data.get('short_description', ''))

    def clean(self):
        cleaned_data = super().clean()
        price_min = cleaned_data.get('price_min')
        price_max = cleaned_data.get('price_max')
        if price_max and price_min and price_max < price_min:
            raise ValidationError(_('Maximum price cannot be less than minimum price'))
        return cleaned_data
