/**
 * Professional Notifications JavaScript Module
 * Handles real-time notification updates and interactions with enhanced UX
 */

class ProfessionalNotificationManager {
    constructor() {
        this.updateInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.isAuthenticated = document.body.dataset.authenticated === 'true';
        this.lastUpdateTime = Date.now();
        this.isVisible = !document.hidden;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.init();
    }

    init() {
        if (!this.isAuthenticated) return;

        this.startPeriodicUpdates();
        this.bindEvents();
        this.updateNotificationCount();
        this.initializeAnimations();
    }

    initializeAnimations() {
        // Add smooth animations to notification elements
        const notificationElements = document.querySelectorAll('.notification-card, .professional-notification-item');
        notificationElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(10px)';
            setTimeout(() => {
                element.style.transition = 'all 0.3s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 50);
        });
    }

    startPeriodicUpdates() {
        // Update immediately
        this.updateNotificationCount();

        // Set up periodic updates with exponential backoff on errors
        this.intervalId = setInterval(() => {
            if (this.isVisible) {
                this.updateNotificationCount();
            }
        }, this.updateInterval);
    }

    stopPeriodicUpdates() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    async updateNotificationCount() {
        try {
            const response = await fetch('/notifications/unread/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                this.updateBadges(data.unread_count);
                this.updateDropdown(data.recent_notifications);
                this.retryCount = 0; // Reset retry count on success
                this.lastUpdateTime = Date.now();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error updating notification count:', error);
            this.handleUpdateError();
        }
    }

    handleUpdateError() {
        this.retryCount++;
        if (this.retryCount <= this.maxRetries) {
            // Exponential backoff: retry after 2^retryCount seconds
            const retryDelay = Math.pow(2, this.retryCount) * 1000;
            setTimeout(() => {
                this.updateNotificationCount();
            }, retryDelay);
        } else {
            console.warn('Max retries reached for notification updates');
            // Optionally show user-friendly error message
            this.showConnectionError();
        }
    }

    showConnectionError() {
        const errorElement = document.createElement('div');
        errorElement.className = 'notification-error-toast';
        errorElement.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Connection issue. Notifications may not be up to date.</span>
                <button onclick="this.parentElement.parentElement.remove()" class="close-btn">×</button>
            </div>
        `;
        document.body.appendChild(errorElement);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorElement.parentElement) {
                errorElement.remove();
            }
        }, 5000);
    }

    updateBadges(count) {
        // Update navigation badge with animation
        const navBadge = document.getElementById('navNotificationBadge');
        if (navBadge) {
            const currentCount = parseInt(navBadge.textContent) || 0;
            if (currentCount !== count) {
                this.animateBadgeUpdate(navBadge, count);
            }
        }

        // Update dropdown header badge
        const dropdownBadge = document.getElementById('dropdownUnreadBadge');
        if (dropdownBadge) {
            dropdownBadge.textContent = count;
            dropdownBadge.style.display = count > 0 ? 'inline-block' : 'none';
        }

        // Update stats in notification list page
        const unreadStat = document.getElementById('unreadCountStat');
        if (unreadStat) {
            const currentStat = parseInt(unreadStat.textContent) || 0;
            if (currentStat !== count) {
                this.animateCounterUpdate(unreadStat, currentStat, count);
            }
        }

        // Update menu item badges in navigation
        const menuBadges = document.querySelectorAll('.dropdown-menu .badge');
        menuBadges.forEach(badge => {
            if (badge.closest('a[href*="notifications"]')) {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline-block' : 'none';
            }
        });

        // Update mark all read button state
        const markAllBtn = document.querySelector('.action-btn[data-bs-target="#markReadConfirmModal"]');
        if (markAllBtn) {
            markAllBtn.disabled = count === 0;
            markAllBtn.innerHTML = `<i class="fas fa-check-double"></i> Mark All Read (${count})`;
        }
    }

    animateBadgeUpdate(badge, newCount) {
        badge.style.transform = 'scale(1.2)';
        badge.style.transition = 'transform 0.2s ease';

        setTimeout(() => {
            badge.textContent = newCount;
            badge.style.display = newCount > 0 ? 'inline-block' : 'none';
            badge.style.transform = 'scale(1)';
        }, 100);
    }

    animateCounterUpdate(element, fromCount, toCount) {
        const duration = 500;
        const startTime = Date.now();
        const difference = toCount - fromCount;

        const updateCounter = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentCount = Math.round(fromCount + (difference * progress));

            element.textContent = currentCount;

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        };

        requestAnimationFrame(updateCounter);
    }

    updateDropdown(notifications) {
        const dropdownList = document.getElementById('dropdownNotificationList');
        if (!dropdownList) return;

        // Add loading state
        dropdownList.style.opacity = '0.7';

        setTimeout(() => {
            if (notifications && notifications.length > 0) {
                dropdownList.innerHTML = notifications.map((notification, index) =>
                    this.createProfessionalNotificationItem(notification, index)
                ).join('');
            } else {
                dropdownList.innerHTML = this.createEmptyState();
            }

            // Restore opacity and add animations
            dropdownList.style.opacity = '1';
            this.animateDropdownItems();
        }, 200);
    }

    createProfessionalNotificationItem(notification, index) {
        const isUnread = notification.read_status === 'unread';
        const timeAgo = this.timeAgo(new Date(notification.created_at));
        const typeIcon = this.getNotificationIcon(notification.notification_type);

        return `
            <a class="professional-notification-item ${isUnread ? 'unread' : ''}"
               href="/notifications/${notification.id}/"
               style="animation-delay: ${index * 50}ms">
                <div class="notification-item-content">
                    <div class="notification-item-icon">
                        <i class="fas ${typeIcon}"></i>
                    </div>
                    <div class="notification-item-details">
                        <div class="notification-item-title">
                            ${isUnread ? '<span class="unread-indicator"></span>' : ''}
                            ${this.truncate(notification.title, 35)}
                        </div>
                        <div class="notification-item-message">
                            ${this.truncate(notification.message, 70)}
                        </div>
                        <div class="notification-item-meta">
                            <span class="notification-type-badge ${notification.notification_type}">
                                ${notification.notification_type.charAt(0).toUpperCase() + notification.notification_type.slice(1)}
                            </span>
                            <span class="notification-time">
                                ${timeAgo}
                            </span>
                        </div>
                    </div>
                </div>
            </a>
        `;
    }

    createEmptyState() {
        return `
            <div class="empty-notifications">
                <div class="empty-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <div class="empty-text">
                    <h6>No notifications</h6>
                    <p>You're all caught up!</p>
                </div>
            </div>
        `;
    }

    getNotificationIcon(type) {
        const icons = {
            'booking': 'fa-calendar-check',
            'payment': 'fa-credit-card',
            'review': 'fa-star',
            'announcement': 'fa-bullhorn',
            'system': 'fa-cog'
        };
        return icons[type] || 'fa-bell';
    }

    animateDropdownItems() {
        const items = document.querySelectorAll('.professional-notification-item');
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-10px)';
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, index * 50);
        });
    }

    bindEvents() {
        // Handle notification dropdown clicks with professional feedback
        document.addEventListener('click', (e) => {
            const notificationItem = e.target.closest('.professional-notification-item');
            if (notificationItem) {
                this.handleNotificationClick(notificationItem);
            }

            // Handle mark all read forms in dropdown
            const markAllForm = e.target.closest('.mark-all-read-form');
            if (markAllForm) {
                e.preventDefault();
                this.handleMarkAllRead(markAllForm);
            }
        });

        // Handle page visibility changes with smart updates
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (document.hidden) {
                this.stopPeriodicUpdates();
            } else {
                // Update immediately when page becomes visible
                this.startPeriodicUpdates();
                // Check if we've been away for more than 2 minutes
                if (Date.now() - this.lastUpdateTime > 120000) {
                    this.updateNotificationCount();
                }
            }
        });

        // Handle connection status changes
        window.addEventListener('online', () => {
            this.retryCount = 0;
            this.updateNotificationCount();
            this.showConnectionRestored();
        });

        window.addEventListener('offline', () => {
            this.stopPeriodicUpdates();
            this.showOfflineMessage();
        });

        // Handle dropdown open/close events
        const notificationDropdown = document.getElementById('notificationDropdown');
        if (notificationDropdown) {
            notificationDropdown.addEventListener('show.bs.dropdown', () => {
                // Update notifications when dropdown is opened
                this.updateNotificationCount();
            });
        }

        // Add keyboard navigation support
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Close any open notification dropdowns
                const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
                openDropdowns.forEach(dropdown => {
                    const toggle = document.querySelector(`[data-bs-toggle="dropdown"][aria-expanded="true"]`);
                    if (toggle) {
                        bootstrap.Dropdown.getInstance(toggle)?.hide();
                    }
                });
            }
        });
    }

    handleNotificationClick(notificationItem) {
        // Add visual feedback
        notificationItem.style.transform = 'scale(0.98)';
        setTimeout(() => {
            notificationItem.style.transform = 'scale(1)';
        }, 150);

        // Optional: Auto-mark as read when clicked (can be configured)
        if (notificationItem.classList.contains('unread')) {
            // You can implement auto-mark-as-read here if desired
            // this.markAsReadOnClick(notificationItem);
        }
    }

    async handleMarkAllRead(form) {
        const button = form.querySelector('.quick-action-btn');
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Marking...';
        button.disabled = true;

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // Update UI immediately
                    this.updateBadges(0);
                    this.updateDropdown([]);
                    this.showSuccessToast('All notifications marked as read');
                } else {
                    throw new Error(data.message || 'Failed to mark notifications as read');
                }
            } else {
                throw new Error('Network error');
            }
        } catch (error) {
            console.error('Error marking all as read:', error);
            this.showErrorToast('Failed to mark notifications as read');
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    handleNotificationClick(item) {
        // Mark notification as read when clicked
        const href = item.getAttribute('href');
        if (href && item.classList.contains('bg-light')) {
            // Remove unread styling immediately for better UX
            item.classList.remove('bg-light');
            const badge = item.querySelector('.badge.bg-primary');
            if (badge) badge.remove();
        }
    }

    truncate(text, length) {
        if (text.length <= length) return text;
        return text.substring(0, length) + '...';
    }

    timeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' min ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hr ago';
        if (diffInSeconds < 604800) return Math.floor(diffInSeconds / 86400) + ' day' + (Math.floor(diffInSeconds / 86400) > 1 ? 's' : '') + ' ago';
        return date.toLocaleDateString();
    }

    showSuccessToast(message) {
        this.showToast(message, 'success');
    }

    showErrorToast(message) {
        this.showToast(message, 'error');
    }

    showConnectionRestored() {
        this.showToast('Connection restored. Notifications updated.', 'success');
    }

    showOfflineMessage() {
        this.showToast('You are offline. Notifications will update when connection is restored.', 'warning');
    }

    showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let toastContainer = document.querySelector('.professional-toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'professional-toast-container';
            toastContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                pointer-events: none;
            `;
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `professional-toast toast-${type}`;
        toast.style.cssText = `
            background: white;
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: all 0.3s ease;
            pointer-events: auto;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            max-width: 350px;
        `;

        const icon = type === 'success' ? 'fa-check-circle' :
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

        toast.innerHTML = `
            <i class="fas ${icon}" style="color: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#007bff'}"></i>
            <span style="font-family: 'Inter', sans-serif; color: black; font-weight: 500;">${message}</span>
            <button onclick="this.parentElement.remove()" style="background: none; border: none; font-size: 1.2rem; cursor: pointer; color: black; margin-left: auto;">×</button>
        `;

        toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    // Public method to manually refresh notifications
    refresh() {
        this.updateNotificationCount();
    }

    // Public method to get current unread count
    getUnreadCount() {
        const badge = document.getElementById('navNotificationBadge');
        return badge ? parseInt(badge.textContent) || 0 : 0;
    }
}

// Initialize professional notification manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new ProfessionalNotificationManager();

    // Expose refresh method globally for manual updates
    window.refreshNotifications = () => {
        if (window.notificationManager) {
            window.notificationManager.refresh();
        }
    };
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    if (window.notificationManager) {
        window.notificationManager.stopPeriodicUpdates();
    }
});

// Handle browser back/forward navigation
window.addEventListener('pageshow', (event) => {
    if (event.persisted && window.notificationManager) {
        // Page was loaded from cache, refresh notifications
        window.notificationManager.refresh();
    }
});
