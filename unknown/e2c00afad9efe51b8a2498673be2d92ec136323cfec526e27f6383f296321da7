from .common import *

# ===== CUSTOMER VIEWS =====

@method_decorator(login_required, name='dispatch')
class AddToCartView(View):
    """Add a service to the customer's cart with date and time slot selection."""

    def get(self, request, service_id):
        return add_to_cart_view(request, service_id)

    def post(self, request, service_id):
        return add_to_cart_view(request, service_id)


@login_required
def add_to_cart_view(request, service_id):
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_access',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'add_to_cart', 'service_id': service_id}
            )
        messages.error(request, "Only customers can add services to cart.")
        return redirect('/')

    # Get the service
    service = get_object_or_404(Service, id=service_id, is_active=True)

    # Check if venue is active and approved
    if service.venue.visibility != 'active' or service.venue.approval_status != 'approved':
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'add_to_cart_unavailable_service',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'venue_visibility': service.venue.visibility,
                    'venue_status': service.venue.approval_status
                }
            )
        messages.error(request, "This service is not currently available.")
        return redirect('venues_app:venue_list')


    if request.method == 'POST':
        form = AddToCartForm(service=service, user=request.user, data=request.POST)
        if form.is_valid():
            selected_date = form.cleaned_data['selected_date']
            selected_time_slot = form.cleaned_data['selected_time_slot']
            quantity = form.cleaned_data['quantity']

            # Check service availability
            is_available, message = check_service_availability(
                service, selected_date, selected_time_slot, quantity
            )

            if not is_available:
                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'add_to_cart_unavailable_slot',
                        user=request.user,
                        request=request,
                        details={
                            'service_id': service_id,
                            'selected_date': str(selected_date),
                            'selected_time_slot': str(selected_time_slot),
                            'quantity': quantity,
                            'availability_message': message
                        }
                    )
                messages.error(request, message)
                return render(request, 'booking_cart_app/customer/add_to_cart.html', {
                    'form': form,
                    'service': service,
                })

            try:
                with transaction.atomic():
                    # Get or create cart for user
                    cart = get_or_create_cart(request.user)

                    # Check if the service is already in the cart for the same date and time
                    existing_item = CartItem.objects.filter(
                        cart=cart,
                        service=service,
                        selected_date=selected_date,
                        selected_time_slot=selected_time_slot
                    ).first()

                    if existing_item:
                        # Update the quantity
                        old_quantity = existing_item.quantity
                        existing_item.quantity = quantity
                        existing_item.save()

                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'cart_item_updated',
                                user=request.user,
                                request=request,
                                details={
                                    'service_id': service_id,
                                    'service_title': service.service_title,
                                    'selected_date': str(selected_date),
                                    'selected_time_slot': str(selected_time_slot),
                                    'old_quantity': old_quantity,
                                    'new_quantity': quantity,
                                    'price_per_item': str(existing_item.price_per_item)
                                }
                            )
                        messages.success(request, "Cart updated successfully.")
                    else:
                        # Calculate price with any applicable discounts
                        discount_info = get_best_discount(service, request.user)
                        if discount_info:
                            # discount_info is a tuple: (discount_object, discount_amount, final_price)
                            _, _, final_price = discount_info
                            price_per_item = final_price
                        else:
                            price_per_item = service.price_min

                        # Create a new cart item
                        cart_item = CartItem.objects.create(
                            cart=cart,
                            service=service,
                            selected_date=selected_date,
                            selected_time_slot=selected_time_slot,
                            quantity=quantity,
                            price_per_item=price_per_item
                        )

                        if LOGGING_ENABLED:
                            log_user_activity(
                                'booking_cart_app',
                                'item_added_to_cart',
                                user=request.user,
                                request=request,
                                details={
                                    'service_id': service_id,
                                    'service_title': service.service_title,
                                    'venue_name': service.venue.venue_name,
                                    'selected_date': str(selected_date),
                                    'selected_time_slot': str(selected_time_slot),
                                    'quantity': quantity,
                                    'price_per_item': str(price_per_item),
                                    'total_price': str(cart_item.total_price),
                                    'discount_applied': discount_info is not None
                                }
                            )
                        messages.success(request, "Service added to cart successfully.")

                    # Extend cart expiration
                    cart.extend_expiration()

                    # Redirect to cart
                    return redirect('booking_cart_app:cart_view')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'add_to_cart_error',
                        f"Error adding service to cart: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={
                            'service_id': service_id,
                            'selected_date': str(selected_date),
                            'selected_time_slot': str(selected_time_slot),
                            'quantity': quantity
                        }
                    )
                messages.error(request, f"Error adding service to cart: {str(e)}")
                return render(request, 'booking_cart_app/customer/add_to_cart.html', {
                    'form': form,
                    'service': service,
                })
    else:
        # Log cart view access
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'add_to_cart_page_viewed',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'service_title': service.service_title,
                    'venue_name': service.venue.venue_name
                }
            )
        form = AddToCartForm(service=service, user=request.user)

    context = {
        'form': form,
        'service': service,
    }

    return render(request, 'booking_cart_app/customer/add_to_cart.html', context)


@login_required
def get_available_slots_ajax(request, service_id):
    """
    AJAX endpoint to get available time slots for a service on a specific date.
    Returns JSON response with available time slots.
    """
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_ajax_access',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'get_available_slots', 'service_id': service_id}
            )
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    service = get_object_or_404(Service, id=service_id, is_active=True)
    date_str = request.GET.get('date')

    if not date_str:
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'ajax_slots_missing_date',
                user=request.user,
                request=request,
                details={'service_id': service_id}
            )
        return JsonResponse({'error': 'Date parameter required'}, status=400)

    try:
        selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'ajax_slots_invalid_date',
                user=request.user,
                request=request,
                details={'service_id': service_id, 'invalid_date': date_str}
            )
        return JsonResponse({'error': 'Invalid date format'}, status=400)

    try:
        # Get available time slots for the service on the selected date
        available_slots = get_available_time_slots(service, selected_date)

        # Format slots for JSON response
        slots_data = []
        for slot_time, available_spots in available_slots:
            slots_data.append({
                'time': slot_time.strftime('%H:%M'),
                'display': slot_time.strftime('%I:%M %p'),
                'available_spots': available_spots
            })

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'ajax_slots_retrieved',
                user=request.user,
                request=request,
                details={
                    'service_id': service_id,
                    'selected_date': str(selected_date),
                    'slots_count': len(slots_data)
                }
            )

        return JsonResponse({'slots': slots_data})

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'ajax_slots_error',
                f"Error retrieving available slots: {str(e)}",
                user=request.user,
                request=request,
                exception=e,
                details={'service_id': service_id, 'selected_date': date_str}
            )
        return JsonResponse({'error': 'Error retrieving available slots'}, status=500)


@login_required
def cart_view(request):
    """
    Display the customer's cart with all items and total price.
    Allows customers to update quantities or remove items.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_view',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'view_cart'}
            )
        messages.error(request, "Only customers can view cart.")
        return redirect('/')

    try:

        # Get or create cart for user
        cart = get_or_create_cart(request.user)

        # Get cart items with discount information
        cart_items_with_discounts = []
        total_original_price = 0
        total_discounted_price = 0
        total_savings = 0

        for item in cart.items.all():
            # Get discount information
            discount_info = get_best_discount(item.service, request.user)

            original_price = item.service.price_min * item.quantity
            discounted_price = item.total_price
            savings = original_price - discounted_price

            cart_items_with_discounts.append({
                'item': item,
                'original_price': original_price,
                'discounted_price': discounted_price,
                'savings': savings,
                'discount_info': discount_info,
            })

            total_original_price += original_price
            total_discounted_price += discounted_price
            total_savings += savings

        # Log cart view activity
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'cart_viewed',
                user=request.user,
                request=request,
                details={
                    'cart_items_count': cart.total_items,
                    'total_original_price': str(total_original_price),
                    'total_discounted_price': str(total_discounted_price),
                    'total_savings': str(total_savings),
                    'cart_expired': cart.is_expired
                }
            )

        context = {
            'cart': cart,
            'cart_items_with_discounts': cart_items_with_discounts,
            'total_original_price': total_original_price,
            'total_discounted_price': total_discounted_price,
            'total_savings': total_savings,
            'cart_count': cart.total_items,
        }

        return render(request, 'booking_cart_app/customer/cart.html', context)

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'cart_view_error',
                f"Error loading cart: {str(e)}",
                user=request.user,
                request=request,
                exception=e
            )
        messages.error(request, "An error occurred while loading your cart. Please try again.")
        return redirect('/')


@login_required
def update_cart_item_view(request, item_id):
    """
    Update the quantity of a cart item.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_update',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'update_cart_item', 'item_id': item_id}
            )
        messages.error(request, "Only customers can update cart.")
        return redirect('/')

    # Get the cart item
    cart_item = get_object_or_404(CartItem, id=item_id, cart__customer=request.user)

    if request.method == 'POST':
        form = UpdateCartItemForm(request.POST, instance=cart_item)
        if form.is_valid():
            old_quantity = cart_item.quantity
            quantity = form.cleaned_data['quantity']

            # Check service availability for the new quantity
            is_available, message = check_service_availability(
                cart_item.service,
                cart_item.selected_date,
                cart_item.selected_time_slot,
                quantity
            )

            if not is_available:
                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'cart_update_unavailable',
                        user=request.user,
                        request=request,
                        details={
                            'item_id': item_id,
                            'service_id': cart_item.service.id,
                            'old_quantity': old_quantity,
                            'requested_quantity': quantity,
                            'availability_message': message
                        }
                    )
                messages.error(request, message)
                return redirect('booking_cart_app:cart_view')

            try:
                # Update the cart item
                cart_item.quantity = quantity
                cart_item.save()

                # Extend cart expiration
                cart_item.cart.extend_expiration()

                if LOGGING_ENABLED:
                    log_user_activity(
                        'booking_cart_app',
                        'cart_item_quantity_updated',
                        user=request.user,
                        request=request,
                        details={
                            'item_id': item_id,
                            'service_id': cart_item.service.id,
                            'service_title': cart_item.service.service_title,
                            'old_quantity': old_quantity,
                            'new_quantity': quantity,
                            'selected_date': str(cart_item.selected_date),
                            'selected_time_slot': str(cart_item.selected_time_slot)
                        }
                    )

                messages.success(request, "Cart updated successfully.")
                return redirect('booking_cart_app:cart_view')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'cart_update_error',
                        f"Error updating cart item: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'item_id': item_id, 'quantity': quantity}
                    )
                messages.error(request, "An error occurred while updating your cart.")
                return redirect('booking_cart_app:cart_view')
    else:
        form = UpdateCartItemForm(instance=cart_item)

    context = {
        'form': form,
        'cart_item': cart_item,
    }

    return render(request, 'booking_cart_app/customer/update_cart_item.html', context)


@login_required
def remove_from_cart_view(request, item_id):
    """
    Remove an item from the cart.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_cart_removal',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'remove_from_cart', 'item_id': item_id}
            )
        messages.error(request, "Only customers can modify cart.")
        return redirect('/')

    # Get the cart item
    cart_item = get_object_or_404(CartItem, id=item_id, cart__customer=request.user)

    if request.method == 'POST':
        try:
            # Log the removal before deleting
            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'cart_item_removed',
                    user=request.user,
                    request=request,
                    details={
                        'item_id': item_id,
                        'service_id': cart_item.service.id,
                        'service_title': cart_item.service.service_title,
                        'quantity': cart_item.quantity,
                        'selected_date': str(cart_item.selected_date),
                        'selected_time_slot': str(cart_item.selected_time_slot),
                        'total_price': str(cart_item.total_price)
                    }
                )

            cart_item.delete()
            messages.success(request, "Item removed from cart successfully.")

        except Exception as e:
            if LOGGING_ENABLED:
                log_error(
                    'booking_cart_app',
                    'cart_removal_error',
                    f"Error removing cart item: {str(e)}",
                    user=request.user,
                    request=request,
                    exception=e,
                    details={'item_id': item_id}
                )
            messages.error(request, "An error occurred while removing the item.")

    return redirect('booking_cart_app:cart_view')


@login_required
def checkout_view(request):
    """
    Checkout process to create bookings from cart items.
    Groups cart items by venue and creates separate bookings for each venue.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_checkout',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'checkout'}
            )
        messages.error(request, "Only customers can checkout.")
        return redirect('/')


    # Get or create cart for user
    cart = get_or_create_cart(request.user)

    # Check if cart is empty
    if not cart.items.exists():
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'checkout_empty_cart',
                user=request.user,
                request=request,
                details={'cart_id': cart.id}
            )
        messages.error(request, "Your cart is empty.")
        return redirect('booking_cart_app:cart_view')

    # Check if cart has expired
    if cart.is_expired:
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'checkout_expired_cart',
                user=request.user,
                request=request,
                details={'cart_id': cart.id, 'expires_at': str(cart.expires_at)}
            )
        messages.error(request, "Your cart has expired. Please add items again.")
        return redirect('booking_cart_app:cart_view')

    if request.method == 'POST':
        form = CheckoutForm(request.POST)
        if form.is_valid():
            notes = form.cleaned_data.get('notes', '')

            try:
                with transaction.atomic():
                    # Log checkout attempt
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'checkout_initiated',
                            user=request.user,
                            request=request,
                            details={
                                'cart_id': cart.id,
                                'cart_items_count': cart.total_items,
                                'cart_total_price': str(cart.total_price),
                                'notes_provided': bool(notes)
                            }
                        )

                    # Create bookings from cart
                    bookings_created = create_booking_from_cart(cart, notes)

                    if bookings_created:
                        # Clear the cart
                        cart.items.all().delete()

                        # Log successful checkout
                        if LOGGING_ENABLED:
                            booking_details = []
                            for booking in bookings_created:
                                booking_details.append({
                                    'booking_id': str(booking.booking_id),
                                    'venue_name': booking.venue.venue_name,
                                    'total_price': str(booking.total_price),
                                    'items_count': booking.items.count()
                                })

                            log_user_activity(
                                'booking_cart_app',
                                'checkout_completed',
                                user=request.user,
                                request=request,
                                details={
                                    'bookings_created_count': len(bookings_created),
                                    'bookings_details': booking_details,
                                    'total_amount': str(sum(b.total_price for b in bookings_created))
                                }
                            )

                        # Send notifications for all bookings
                        if NOTIFICATIONS_ENABLED:
                            for booking in bookings_created:
                                run_async(notify_new_booking, booking)

                        # Record discount usage for all items
                        for booking in bookings_created:
                            for item in booking.items.all():
                                discount_info = get_best_discount(item.service, request.user)
                                if discount_info:
                                    discount, discount_amount, final_price = discount_info
                                    original_price = item.service_price
                                    record_discount_usage(
                                        discount,
                                        request.user,
                                        booking,
                                        original_price,
                                        discount_amount,
                                        final_price
                                    )

                        messages.success(request, f"Successfully created {len(bookings_created)} booking(s)!")

                        # Redirect to booking list
                        return redirect('booking_cart_app:booking_list')
                    else:
                        if LOGGING_ENABLED:
                            log_error(
                                'booking_cart_app',
                                'checkout_no_bookings_created',
                                "No bookings were created during checkout",
                                user=request.user,
                                request=request,
                                details={'cart_id': cart.id}
                            )
                        messages.error(request, "No bookings were created. Please try again.")
                        return redirect('booking_cart_app:cart_view')

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'checkout_error',
                        f"Error creating booking: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'cart_id': cart.id}
                    )
                messages.error(request, f"Error creating booking: {str(e)}")
                return redirect('booking_cart_app:cart_view')
    else:
        # Log checkout page view
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'checkout_page_viewed',
                user=request.user,
                request=request,
                details={
                    'cart_id': cart.id,
                    'cart_items_count': cart.total_items,
                    'cart_total_price': str(cart.total_price)
                }
            )
        form = CheckoutForm()

    # Calculate totals for display
    cart_total = cart.total_price

    context = {
        'form': form,
        'cart': cart,
        'cart_total': cart_total,
    }

    return render(request, 'booking_cart_app/customer/checkout.html', context)


@login_required
def booking_list_view(request):
    """
    Display all bookings for the customer with filtering options.
    Shows upcoming and past bookings with their current status.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can view bookings.")
        return redirect('/')

    # Get filter parameters
    status_filter = request.GET.get('status', 'all')

    # Get all bookings for the user
    bookings = (
        Booking.objects.filter(customer=request.user)
        .prefetch_related('items__service', 'venue')
        .order_by('-booking_date')
    )

    # Apply status filter
    if status_filter != 'all':
        bookings = bookings.filter(status=status_filter)

    # Separate upcoming and past bookings
    today = timezone.now().date()
    upcoming_bookings = []
    past_bookings = []

    for booking in bookings:
        # Check if any booking item is scheduled for today or future
        has_future_items = booking.items.filter(scheduled_date__gte=today).exists()

        if has_future_items and booking.status not in [Booking.CANCELLED, Booking.COMPLETED]:
            upcoming_bookings.append(booking)
        else:
            past_bookings.append(booking)

    # Pagination
    paginator = Paginator(bookings, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'bookings': page_obj,
        'upcoming_bookings': upcoming_bookings[:5],  # Show first 5 upcoming
        'past_bookings': past_bookings[:5],  # Show first 5 past
        'status_filter': status_filter,
        'status_choices': Booking.STATUS_CHOICES,
    }

    return render(request, 'booking_cart_app/customer/booking_list.html', context)


@login_required
def booking_detail_view(request, booking_slug):
    """
    Display detailed information about a specific booking.
    Shows all booking items, status, and allows cancellation if eligible.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can view booking details.")
        return redirect('/')

    # Get the booking
    booking = get_object_or_404(Booking, slug=booking_slug, customer=request.user)

    # Get all booking items
    booking_items = booking.items.all().order_by('scheduled_date', 'scheduled_time')

    context = {
        'booking': booking,
        'booking_items': booking_items,
        'can_cancel': booking.can_be_cancelled,
    }

    return render(request, 'booking_cart_app/customer/booking_detail.html', context)


@login_required
def cancel_booking_view(request, booking_slug):
    """
    Cancel a booking if it's within the 6-hour cancellation window.
    Allows customer to provide a cancellation reason.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        if LOGGING_ENABLED:
            log_security_event(
                'booking_cart_app',
                'unauthorized_booking_cancellation',
                user_email=request.user.email if request.user.is_authenticated else None,
                request=request,
                details={'attempted_action': 'cancel_booking', 'booking_slug': booking_slug}
            )
        messages.error(request, "Only customers can cancel bookings.")
        return redirect('/')

    # Get the booking
    booking = get_object_or_404(Booking, slug=booking_slug, customer=request.user)

    # Check if booking can be cancelled
    if not booking.can_be_cancelled:
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_cancellation_denied',
                user=request.user,
                request=request,
                details={
                    'booking_id': str(booking.booking_id),
                    'booking_date': str(booking.booking_date),
                    'reason': 'outside_cancellation_window'
                }
            )
        messages.error(request, "This booking cannot be cancelled. The 6-hour cancellation window has passed.")
        return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)

    if request.method == 'POST':
        form = BookingCancellationForm(request.POST)
        if form.is_valid():
            cancellation_reason = form.cleaned_data['cancellation_reason']

            try:
                with transaction.atomic():
                    # Log cancellation attempt
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_cancellation_initiated',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'venue_name': booking.venue.venue_name,
                                'total_price': str(booking.total_price),
                                'cancellation_reason': cancellation_reason,
                                'items_count': booking.items.count()
                            }
                        )

                    # Cancel the booking
                    booking.cancel_booking(cancellation_reason)

                    # Update service availability for all booking items
                    for item in booking.items.all():
                        try:
                            availability = ServiceAvailability.objects.get(
                                service=item.service,
                                available_date=item.scheduled_date,
                                start_time=item.scheduled_time
                            )
                            availability.cancel_booking()
                        except ServiceAvailability.DoesNotExist:
                            pass  # Availability record doesn't exist, skip

                    # Log successful cancellation
                    if LOGGING_ENABLED:
                        log_user_activity(
                            'booking_cart_app',
                            'booking_cancelled',
                            user=request.user,
                            request=request,
                            details={
                                'booking_id': str(booking.booking_id),
                                'venue_name': booking.venue.venue_name,
                                'total_price': str(booking.total_price),
                                'cancellation_reason': cancellation_reason,
                                'cancelled_at': str(timezone.now())
                            }
                        )

                    # Send notification
                    if NOTIFICATIONS_ENABLED:
                        notify_booking_cancelled(booking)

                    messages.success(request, "Booking cancelled successfully.")
                    return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)

            except Exception as e:
                if LOGGING_ENABLED:
                    log_error(
                        'booking_cart_app',
                        'booking_cancellation_error',
                        f"Error cancelling booking: {str(e)}",
                        user=request.user,
                        request=request,
                        exception=e,
                        details={'booking_id': str(booking.booking_id)}
                    )
                messages.error(request, f"Error cancelling booking: {str(e)}")
                return redirect('booking_cart_app:booking_detail', booking_slug=booking_slug)
    else:
        # Log cancellation page view
        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_cancellation_page_viewed',
                user=request.user,
                request=request,
                details={
                    'booking_id': str(booking.booking_id),
                    'venue_name': booking.venue.venue_name,
                    'can_be_cancelled': booking.can_be_cancelled
                }
            )
        form = BookingCancellationForm()

    context = {
        'form': form,
        'booking': booking,
    }

    return render(request, 'booking_cart_app/customer/cancel_booking.html', context)


@login_required
def booking_confirmation_view(request, booking_slug):
    """
    Display booking confirmation page after successful checkout.
    Shows booking details and next steps.
    """
    # Check if user is a customer
    if not hasattr(request.user, 'is_customer') or not request.user.is_customer:
        messages.error(request, "Only customers can view booking confirmations.")
        return redirect('/')

    # Get the booking
    booking = get_object_or_404(Booking, slug=booking_slug, customer=request.user)

    # Get all booking items
    booking_items = booking.items.all().order_by('scheduled_date', 'scheduled_time')

    context = {
        'booking': booking,
        'booking_items': booking_items,
    }

    return render(request, 'booking_cart_app/customer/booking_confirmation.html', context)


