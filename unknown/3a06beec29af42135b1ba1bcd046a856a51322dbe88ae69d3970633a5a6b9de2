{% extends 'base.html' %}

{% block extra_css %}
    {% load static %}
    {% load widget_tweaks %}

    <!-- Preconnect for Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Professional Animations CSS -->
    <link rel="stylesheet" href="{% static 'css/notifications_animations.css' %}">

    <style>
        /* CozyWish Notifications App - Black & White Design */
        /* Matching homepage and accounts_app design with clean typography and spacing */

        /* CSS Variables for fonts */
        :root {
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Notifications wrapper - clean white background */
        .notifications-wrapper {
            background-color: white;
            min-height: 100vh;
            padding: 2rem 0;
            font-family: var(--font-primary);
        }

        /* Typography */
        .notifications-wrapper h1, .notifications-wrapper h2, .notifications-wrapper h3,
        .notifications-wrapper h4, .notifications-wrapper h5, .notifications-wrapper h6 {
            font-family: var(--font-heading);
            font-weight: 600;
            color: black;
            margin-bottom: 1rem;
        }

        .notifications-wrapper p, .notifications-wrapper span, .notifications-wrapper div {
            color: black;
        }

        /* Cards - clean white with black border */
        .notifications-wrapper .card {
            background: white;
            border: 2px solid black;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .notifications-wrapper .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        /* Buttons - black and white theme */
        .notifications-wrapper .btn {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            border: 2px solid black;
            transition: all 0.3s ease;
        }

        .notifications-wrapper .btn-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-outline-primary {
            background-color: white;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-outline-primary:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-success {
            background-color: white;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-success:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-danger {
            background-color: white;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .btn-danger:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Form elements */
        .notifications-wrapper .form-control, .notifications-wrapper .form-select {
            font-family: var(--font-primary);
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: white;
            color: black;
        }

        .notifications-wrapper .form-control:focus, .notifications-wrapper .form-select:focus {
            border-color: black;
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            background-color: white;
            color: black;
        }

        /* Labels */
        .notifications-wrapper .form-label {
            font-family: var(--font-primary);
            font-weight: 500;
            color: black;
            margin-bottom: 0.5rem;
        }

        /* Badges */
        .notifications-wrapper .badge {
            font-family: var(--font-primary);
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
        }

        .notifications-wrapper .badge.bg-success {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .notifications-wrapper .badge.bg-primary {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .notifications-wrapper .badge.bg-warning {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .notifications-wrapper .badge.bg-danger {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        .notifications-wrapper .badge.bg-info {
            background-color: white !important;
            color: black;
            border: 2px solid black;
        }

        /* Pagination */
        .notifications-wrapper .pagination .page-link {
            color: black;
            border: 2px solid black;
            background-color: white;
            font-family: var(--font-primary);
            font-weight: 500;
        }

        .notifications-wrapper .pagination .page-link:hover {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        .notifications-wrapper .pagination .page-item.active .page-link {
            background-color: #f8f9fa;
            color: black;
            border-color: black;
        }

        /* Text colors */
        .notifications-wrapper .text-muted {
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .notifications-wrapper .text-primary {
            color: black !important;
        }

        .notifications-wrapper .text-success {
            color: black !important;
        }

        .notifications-wrapper .text-danger {
            color: black !important;
        }

        .notifications-wrapper .text-warning {
            color: black !important;
        }

        .notifications-wrapper .text-info {
            color: black !important;
        }

        /* Notification specific styling */
        .notification-item {
            border: 2px solid black;
            border-radius: 1rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: white;
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .notification-item.unread {
            border-color: black;
            background-color: rgba(0, 0, 0, 0.02);
        }

        .notification-item.read {
            border-color: rgba(0, 0, 0, 0.3);
            background-color: white;
        }

        .notification-icon {
            background-color: white;
            border: 2px solid black;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: black;
        }

        .notification-time {
            font-size: 0.875rem;
            color: rgba(0, 0, 0, 0.6);
            font-family: var(--font-primary);
        }

        /* Breadcrumb styling */
        .notifications-wrapper .breadcrumb {
            background-color: white;
            border: 2px solid black;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
        }

        .notifications-wrapper .breadcrumb-item a {
            color: black;
            text-decoration: none;
            font-weight: 500;
        }

        .notifications-wrapper .breadcrumb-item a:hover {
            color: rgba(0, 0, 0, 0.7);
        }

        .notifications-wrapper .breadcrumb-item.active {
            color: rgba(0, 0, 0, 0.6);
        }

        /* Card header styling */
        .notifications-wrapper .card-header {
            background-color: white !important;
            color: black;
            border-bottom: 2px solid black;
            font-family: var(--font-heading);
            font-weight: 600;
        }

        .notifications-wrapper .card-header.bg-light {
            background-color: white !important;
            color: black;
            border-bottom: 2px solid black;
        }

        /* Accessibility Enhancements */
        .visually-hidden {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        /* Focus indicators */
        .notifications-wrapper *:focus {
            outline: 3px solid black !important;
            outline-offset: 2px !important;
        }

        .notifications-wrapper .notification-card:focus {
            outline: 3px solid black !important;
            outline-offset: 4px !important;
            transform: translateY(-2px);
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .notifications-wrapper {
                background: white !important;
                color: black !important;
            }

            .notifications-wrapper .card,
            .notifications-wrapper .btn,
            .notifications-wrapper .form-control {
                border-width: 3px !important;
                border-color: black !important;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .notifications-wrapper * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Skip links */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: black;
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 10000;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .notifications-wrapper {
                padding: 1rem 0;
            }

            .notifications-wrapper .card {
                margin-bottom: 1.5rem;
            }
        }
    </style>
    {% block notifications_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="notifications-wrapper">
    <div class="container py-4" style="max-width: 960px;">
        {% block notifications_content %}{% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
    <!-- Professional Feedback System -->
    <script src="{% static 'js/notifications_feedback.js' %}"></script>

    <!-- Accessibility Enhancements -->
    <script src="{% static 'js/notifications_accessibility.js' %}"></script>

    {% block notifications_extra_js %}{% endblock %}
{% endblock %}
