{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Provider Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}




{% block dashboard_actions %}
<div class="btn-group me-2">
    {% if not venue %}
    <a href="{% url 'venues_app:venue_create' %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Add Venue">
        <i class="fas fa-plus"></i> Add Venue
    </a>
    {% endif %}
    {% if venue %}
    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Manage Services">
        <i class="fas fa-spa"></i> Manage Services
    </a>
    {% endif %}
</div>
{% endblock %}




{% block dashboard_content %}
{% include 'payments_app/includes/dashboard_skeleton.html' %}
<div id="dashboard-content" style="display:none;">
<nav aria-label="breadcrumb" class="mb-3">
  <ol class="breadcrumb">
    <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
  </ol>
</nav>
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title" style="color: black;">Today's Bookings</h6>
                        <h3 class="mb-0" style="color: black;">{{ todays_bookings.count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x" style="color: white;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; color: white;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title" style="color: black;">Monthly Earnings</h6>
                        <h3 class="mb-0" style="color: black;">${{ monthly_earnings|floatformat:2 }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x" style="color: white;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; color: black; border: 2px solid black;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title" style="color: black;">Total Bookings</h6>
                        <h3 class="mb-0" style="color: black;">{{ total_bookings }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clipboard-list fa-2x" style="color: black;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card dashboard-card" style="background-color: white; color: black; border: 2px solid black;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title" style="color: black;">Team Members</h6>
                        <h3 class="mb-0" style="color: black;">{{ team_members_count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x" style="color: black;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Status Overview -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Today's Bookings</h5>
                <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="View today's bookings list">
                    View All
                </a>
            </div>
            <div class="card-body">
                {% include 'booking_cart_app/includes/booking_table_skeleton.html' %}
                <table class="table table-sm table-sticky" style="display:none;" id="todays-bookings-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Service</th>
                            <th>Customer</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for booking in todays_bookings %}
                            {% for item in booking.items.all %}
                                <tr>
                                    <td>{{ item.scheduled_time|time:"H:i" }}</td>
                                    <td>{{ item.service_title }}</td>
                                    <td>{{ booking.customer.get_full_name|default:booking.customer.email }}</td>
                                    <td>
                                        <span class="badge" style="{% if booking.status == 'confirmed' %}background-color: black; color: white;{% elif booking.status == 'pending' %}background-color: white; color: black; border: 2px solid black;{% elif booking.status == 'completed' %}background-color: black; color: white;{% else %}background-color: white; color: black; border: 2px solid black;{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            {% endfor %}
                        {% endfor %}
                    </tbody>
                </table>
                {% if not todays_bookings %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x mb-3" style="color: black; opacity: 0.6;"></i>
                        <p style="color: black; opacity: 0.7;">{% trans "No bookings scheduled for today." %}</p>
                        <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-outline-primary mt-2" data-bs-toggle="tooltip" title="View all bookings">
                            {% trans "View All Bookings" %}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Booking Status</h5>
            </div>
            <div class="card-body text-center">
                <canvas id="statusChart" width="200" height="200"
                        data-pending="{{ pending_bookings }}"
                        data-confirmed="{{ confirmed_bookings }}"
                        data-completed="{{ completed_bookings }}"
                        aria-label="Booking status chart" role="img"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Venue Management Section -->
{% if venue %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Your Venue</h5>
                <div class="btn-group">
                    <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Manage venue details">
                        <i class="fas fa-cog me-1"></i>Manage
                    </a>
                    {% if venue.approval_status == 'approved' %}
                    <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="View public venue page">
                        <i class="fas fa-eye me-1"></i>Public View
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 class="fw-bold">{{ venue.venue_name }}</h6>
                        <p style="color: black; opacity: 0.7;" class="mb-2">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ venue.full_address }}
                        </p>
                        <p class="mb-2">{{ venue.description|truncatewords:20 }}</p>
                        <div class="d-flex flex-wrap gap-2">
                            <span class="badge" style="{% if venue.approval_status == 'approved' %}background-color: black; color: white;{% elif venue.approval_status == 'pending' %}background-color: white; color: black; border: 2px solid black;{% else %}background-color: black; color: white;{% endif %}">
                                <i class="fas fa-{% if venue.approval_status == 'approved' %}check{% elif venue.approval_status == 'pending' %}clock{% else %}times{% endif %} me-1"></i>
                                {{ venue.get_approval_status_display }}
                            </span>
                            {% if venue.is_active %}
                            <span class="badge" style="background-color: black; color: white;">
                                <i class="fas fa-check-circle me-1"></i>Active
                            </span>
                            {% else %}
                            <span class="badge" style="background-color: white; color: black; border: 2px solid black;">
                                <i class="fas fa-pause-circle me-1"></i>Inactive
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        {% if venue.get_primary_image %}
                        <img src="{{ venue.get_primary_image }}" alt="{{ venue.venue_name }}" class="img-fluid rounded" style="max-height: 120px; object-fit: cover;">
                        {% else %}
                        <div class="rounded d-flex align-items-center justify-content-center" style="height: 120px; background-color: white; border: 2px solid black;">
                            <i class="fas fa-image fa-2x" style="color: black; opacity: 0.6;"></i>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Bookings (Last 7 Days)</h5>
                <a href="{% url 'booking_cart_app:provider_booking_list' %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="Go to all bookings">
                    View All Bookings
                </a>
            </div>
            <div class="card-body">
                {% if recent_bookings %}
                    <div class="table-responsive">
                        <table class="table table-sticky">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Services</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in recent_bookings %}
                                    <tr>
                                        <td>{{ booking.booking_date|date:"M d, Y" }}</td>
                                        <td>{{ booking.customer.get_full_name|default:booking.customer.email }}</td>
                                        <td>
                                            {% for item in booking.items.all %}
                                                {{ item.service_title }}{% if not forloop.last %}, {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>${{ booking.total_price|floatformat:2 }}</td>
                                        <td>
                                            <span class="badge" style="{% if booking.status == 'confirmed' %}background-color: black; color: white;{% elif booking.status == 'pending' %}background-color: white; color: black; border: 2px solid black;{% elif booking.status == 'completed' %}background-color: black; color: white;{% else %}background-color: white; color: black; border: 2px solid black;{% endif %}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        <nav aria-label="Recent bookings pagination" class="mt-3">
                            <ul class="pagination justify-content-center">
                                {% if recent_bookings.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ recent_bookings.previous_page_number }}" aria-label="Previous">
                                        &laquo;
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled"><span class="page-link">&laquo;</span></li>
                                {% endif %}
                                <li class="page-item active"><span class="page-link">{{ recent_bookings.number }}</span></li>
                                {% if recent_bookings.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ recent_bookings.next_page_number }}" aria-label="Next">
                                        &raquo;
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled"><span class="page-link">&raquo;</span></li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x mb-3" style="color: black; opacity: 0.6;"></i>
                        <p style="color: black; opacity: 0.7;">No recent bookings found.</p>
                    </div>
                {% endif %}
            </div>
        </div>
</div>
</div>
</div>
{% endblock %}

{% block dashboard_js %}
{{ block.super }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/dashboard.js' %}"></script>
{% endblock %}
