"""
Management command to seed accounts_app with realistic test data.
Creates customers, service providers, and team members.
"""
import random
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from accounts_app.models import CustomerProfile, ServiceProviderProfile, TeamMember

User = get_user_model()


class Command(BaseCommand):
    """Seed accounts_app with realistic test data."""
    
    help = 'Seed accounts_app with customers, service providers, and team members'

    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing accounts data before seeding',
        )

    def handle(self, *args, **options):
        """Execute the command."""
        self.stdout.write(
            self.style.SUCCESS('🌱 Starting accounts_app data seeding...')
        )
        
        if options['clear']:
            self.clear_existing_data()

        with transaction.atomic():
            self.create_customers()
            self.create_service_providers()
            self.create_team_members()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Accounts app data seeding completed successfully!')
        )

    def clear_existing_data(self):
        """Clear existing accounts data."""
        self.stdout.write('🧹 Clearing existing accounts data...')

        try:
            # Delete in reverse order of dependencies
            TeamMember.objects.all().delete()
            ServiceProviderProfile.objects.all().delete()
            CustomerProfile.objects.all().delete()
            User.objects.filter(is_superuser=False).delete()

            self.stdout.write('   ✅ Existing accounts data cleared')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'   ⚠️ Warning during data clearing: {str(e)}')
            )
            self.stdout.write('   ℹ️ Continuing with seeding...')

    def create_customers(self):
        """Create customer accounts with profiles."""
        self.stdout.write('👥 Creating customer accounts...')
        
        customers_data = [
            {
                'email': '<EMAIL>',
                'first_name': 'Emily',
                'last_name': 'Johnson',
                'gender': 'F',
                'birth_month': 3,
                'birth_year': 1992,
                'phone_number': '+**********',
                'address': '123 Maple Street',
                'city': 'New York',
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Michael',
                'last_name': 'Chen',
                'gender': 'M',
                'birth_month': 7,
                'birth_year': 1988,
                'phone_number': '+**********',
                'address': '456 Oak Avenue',
                'city': 'Los Angeles',
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Williams',
                'gender': 'F',
                # Missing birth info for realism
                'phone_number': '',  # Missing phone for realism
                'address': '789 Pine Road',
                'city': 'Chicago',
            },
        ]
        
        created_count = 0
        for customer_data in customers_data:
            email = customer_data.pop('email')

            # Check if user already exists
            if User.objects.filter(email=email).exists():
                self.stdout.write(f'   ⚠️ Customer {email} already exists, skipping...')
                continue

            # Create user
            user = User.objects.create_user(
                email=email,
                password='testpass123',
                role=User.CUSTOMER
            )

            # Create profile
            CustomerProfile.objects.create(
                user=user,
                **customer_data
            )

            created_count += 1
        
        self.stdout.write(f'   ✅ Created {created_count} customers')

    def create_service_providers(self):
        """Create service provider accounts with business profiles."""
        self.stdout.write('🏢 Creating service provider accounts...')
        
        providers_data = [
            {
                'email': '<EMAIL>',
                'legal_name': 'Serenity Spa & Wellness',
                'display_name': 'Serenity Spa',
                'description': 'A luxury spa offering relaxation and wellness services in the heart of Manhattan.',
                'phone': '+***********',
                'contact_name': 'Jessica Martinez',
                'address': '150 West 47th Street',
                'city': 'New York',
                'state': 'NY',
                'county': 'New York County',
                'zip_code': '10036',
                'website': 'https://serenityspa.com',
                'instagram': 'https://instagram.com/serenityspa',
                'facebook': 'https://facebook.com/serenityspa',
            },
            {
                'email': '<EMAIL>',
                'legal_name': 'Golden State Massage Therapy LLC',
                'display_name': 'Golden State Massage',
                'description': 'Professional therapeutic massage services for stress relief and muscle recovery.',
                'phone': '+13235559876',
                'contact_name': 'David Kim',
                'address': '8765 Sunset Boulevard',
                'city': 'Los Angeles',
                'state': 'CA',
                'county': 'Los Angeles County',
                'zip_code': '90069',
                'website': 'https://goldenstatemassage.com',
                'instagram': '',  # Missing for realism
                'facebook': 'https://facebook.com/goldenstatemassage',
            },
            {
                'email': '<EMAIL>',
                'legal_name': 'Windy City Beauty Salon',
                'display_name': '',  # No DBA name
                'description': '',  # Missing description for realism
                'phone': '+17735554567',
                'contact_name': 'Maria Rodriguez',
                'address': '2200 North Michigan Avenue',
                'city': 'Chicago',
                'state': 'IL',
                'county': 'Cook County',
                'zip_code': '60601',
                'website': '',  # Missing website
                'instagram': 'https://instagram.com/windycitybeauty',
                'facebook': '',
            },
            {
                'email': '<EMAIL>',
                'legal_name': 'Texas Wellness Center Inc',
                'display_name': 'Texas Wellness',
                'description': 'Comprehensive wellness services including massage, facials, and holistic treatments.',
                'phone': '+17135552345',
                'contact_name': 'Robert Thompson',
                'address': '1500 Main Street',
                'city': 'Houston',
                'state': 'TX',
                'county': 'Harris County',
                'zip_code': '77002',
                'website': 'https://texaswellness.com',
                'instagram': 'https://instagram.com/texaswellness',
                'facebook': 'https://facebook.com/texaswellness',
            },
            {
                'email': '<EMAIL>',
                'legal_name': 'Pacific Northwest Spa',
                'display_name': 'PNW Spa',
                'description': 'Eco-friendly spa services using natural and organic products.',
                'phone': '+12065558901',
                'contact_name': 'Amanda Foster',
                'address': '425 Pine Street',
                'city': 'Seattle',
                'state': 'WA',
                'county': 'King County',
                'zip_code': '98101',
                'website': 'https://pnwspa.com',
                'instagram': 'https://instagram.com/pnwspa',
                'facebook': '',  # Missing Facebook
            },
        ]
        
        created_count = 0
        for provider_data in providers_data:
            email = provider_data.pop('email')

            # Check if user already exists
            if User.objects.filter(email=email).exists():
                self.stdout.write(f'   ⚠️ Provider {email} already exists, skipping...')
                continue

            # Create user
            user = User.objects.create_user(
                email=email,
                password='testpass123',
                role=User.SERVICE_PROVIDER
            )

            # Create profile
            ServiceProviderProfile.objects.create(
                user=user,
                **provider_data
            )

            created_count += 1
        
        self.stdout.write(f'   ✅ Created {created_count} service providers')

    def create_team_members(self):
        """Create team members for service providers."""
        self.stdout.write('👨‍💼 Creating team members...')

        # Team member data templates
        team_member_templates = [
            {'name': 'Jennifer Adams', 'position': 'Senior Massage Therapist', 'is_active': True},
            {'name': 'Michael Brown', 'position': 'Spa Manager', 'is_active': True},
            {'name': 'Lisa Chen', 'position': 'Esthetician', 'is_active': True},
            {'name': 'David Wilson', 'position': 'Massage Therapist', 'is_active': True},
            {'name': 'Sarah Davis', 'position': 'Nail Technician', 'is_active': True},
            {'name': 'Robert Garcia', 'position': 'Hair Stylist', 'is_active': True},
            {'name': 'Emily Rodriguez', 'position': 'Receptionist', 'is_active': True},
            {'name': 'James Martinez', 'position': 'Wellness Coordinator', 'is_active': False},  # Inactive
            {'name': 'Amanda Johnson', 'position': 'Facial Specialist', 'is_active': True},
            {'name': 'Christopher Lee', 'position': 'Yoga Instructor', 'is_active': True},
            {'name': 'Nicole Taylor', 'position': 'Aromatherapist', 'is_active': True},
            {'name': 'Kevin Anderson', 'position': 'Physical Therapist', 'is_active': True},
            {'name': 'Rachel White', 'position': 'Makeup Artist', 'is_active': True},
            {'name': 'Daniel Thomas', 'position': 'Reflexologist', 'is_active': True},
            {'name': 'Ashley Jackson', 'position': 'Spa Attendant', 'is_active': True},
        ]

        service_providers = ServiceProviderProfile.objects.all()
        total_created = 0

        for provider in service_providers:
            # Each provider gets 0-7 team members
            num_members = random.randint(0, 7)

            if num_members == 0:
                self.stdout.write(f'   📝 {provider.business_name}: No team members')
                continue

            # Randomly select team members for this provider
            selected_members = random.sample(team_member_templates, min(num_members, len(team_member_templates)))

            created_for_provider = 0
            for member_data in selected_members:
                TeamMember.objects.create(
                    service_provider=provider,
                    **member_data
                )
                created_for_provider += 1
                total_created += 1

            self.stdout.write(f'   📝 {provider.business_name}: {created_for_provider} team members')

        self.stdout.write(f'   ✅ Created {total_created} team members total')
