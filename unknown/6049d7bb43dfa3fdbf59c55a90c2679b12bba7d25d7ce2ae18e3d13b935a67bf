"""
Email utility functions for Cozy<PERSON><PERSON> using SendGrid.
"""
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
import logging

logger = logging.getLogger(__name__)



def send_welcome_email(user_email, user_name):
    """
    Send a welcome email to new users.

    Args:
        user_email (str): User's email address
        user_name (str): User's name

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        subject = 'Welcome to CozyWish - Your Spa & Wellness Journey Begins!'

        # Plain text message
        message = f"""Hello {user_name},

Welcome to CozyWish! We're thrilled to have you join our community of wellness enthusiasts.

CozyWish is your gateway to discovering amazing spa and wellness experiences at discounted prices. Whether you're looking for a relaxing massage, rejuvenating facial, or a complete wellness retreat, we've got you covered.

What you can expect:
• Exclusive discounts on premium spa services
• Curated wellness experiences
• Easy booking and secure payments
• Personalized recommendations

Start exploring our amazing offers and treat yourself to the wellness experience you deserve!

Best regards,
The CozyWish Team

---
This email was sent from CozyWish. If you have any questions, please contact <NAME_EMAIL>.
"""

        from_email = settings.DEFAULT_FROM_EMAIL

        # Log email attempt
        logger.info(
            f"Attempting to send welcome email to {user_email}",
            extra={
                'email_type': 'welcome',
                'recipient': user_email,
                'user_name': user_name,
                'from_email': from_email,
                'email_backend': settings.EMAIL_BACKEND
            }
        )

        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[user_email],
            fail_silently=False,
        )

        logger.info(
            f"Welcome email sent successfully to {user_email}",
            extra={
                'email_type': 'welcome',
                'recipient': user_email,
                'user_name': user_name,
                'status': 'success'
            }
        )
        return True

    except Exception as e:
        logger.error(
            f"Failed to send welcome email to {user_email}: {str(e)}",
            extra={
                'email_type': 'welcome',
                'recipient': user_email,
                'user_name': user_name,
                'error': str(e),
                'status': 'failed'
            }
        )
        return False


def send_booking_confirmation_email(user_email, user_name, booking_details):
    """
    Send booking confirmation email.
    
    Args:
        user_email (str): User's email address
        user_name (str): User's name
        booking_details (dict): Booking information
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        subject = f'Booking Confirmed - {booking_details.get("service_name", "CozyWish Service")}'
        
        message = f"""
        Hello {user_name},
        
        Your booking has been confirmed! Here are the details:
        
        Service: {booking_details.get('service_name', 'N/A')}
        Date: {booking_details.get('date', 'N/A')}
        Time: {booking_details.get('time', 'N/A')}
        Location: {booking_details.get('location', 'N/A')}
        Booking Reference: {booking_details.get('reference', 'N/A')}
        
        Please arrive 15 minutes before your appointment time.
        
        If you need to make any changes or have questions, please contact us as soon as possible.
        
        We look forward to providing you with an amazing wellness experience!
        
        Best regards,
        The CozyWish Team
        """
        
        from_email = '<EMAIL>'
        
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[user_email],
            fail_silently=False,
        )
        
        logger.info(f"Booking confirmation email sent successfully to {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send booking confirmation email to {user_email}: {str(e)}")
        return False


def send_password_reset_email(user_email, reset_link):
    """
    Send password reset email.
    
    Args:
        user_email (str): User's email address
        reset_link (str): Password reset link
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        subject = 'CozyWish - Password Reset Request'
        
        message = f"""
        Hello,
        
        You have requested to reset your password for your CozyWish account.
        
        Please click the link below to reset your password:
        {reset_link}
        
        This link will expire in 24 hours for security reasons.
        
        If you did not request this password reset, please ignore this email.
        
        Best regards,
        The CozyWish Team
        """
        
        from_email = '<EMAIL>'
        
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[user_email],
            fail_silently=False,
        )
        
        logger.info(f"Password reset email sent successfully to {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send password reset email to {user_email}: {str(e)}")
        return False


def send_promotional_email(user_email, user_name, promotion_details):
    """
    Send promotional email about special offers.
    
    Args:
        user_email (str): User's email address
        user_name (str): User's name
        promotion_details (dict): Promotion information
    
    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        subject = f'Special Offer: {promotion_details.get("title", "Amazing Spa Deals")}'
        
        message = f"""
        Hello {user_name},
        
        We have an exciting special offer just for you!
        
        {promotion_details.get('title', 'Special Promotion')}
        
        {promotion_details.get('description', 'Check out our latest deals and discounts.')}
        
        Discount: {promotion_details.get('discount', 'Up to 50% off')}
        Valid Until: {promotion_details.get('valid_until', 'Limited time')}
        
        Don't miss out on this amazing opportunity to treat yourself!
        
        Book now and save big on your next wellness experience.
        
        Best regards,
        The CozyWish Team
        """
        
        from_email = '<EMAIL>'
        
        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[user_email],
            fail_silently=False,
        )
        
        logger.info(f"Promotional email sent successfully to {user_email}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send promotional email to {user_email}: {str(e)}")
        return False


def send_email_verification(user_email, verification_link):
    """
    Send email verification link to service providers.

    Args:
        user_email (str): User's email address
        verification_link (str): Email verification link

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        subject = 'CozyWish - Verify Your Business Account'

        message = f"""
        Hello,

        Thank you for signing up as a service provider with CozyWish!

        Please click the link below to verify your email address and activate your account:
        {verification_link}

        This link will expire in 24 hours for security reasons.

        Once verified, you'll be able to log in and start managing your business profile.

        Best regards,
        The CozyWish Team

        ---
        This email was sent from CozyWish. If you have any questions, please contact <NAME_EMAIL>.
        """

        from_email = settings.DEFAULT_FROM_EMAIL

        send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=[user_email],
            fail_silently=False,
        )

        logger.info(f"Email verification sent successfully to {user_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email verification to {user_email}: {str(e)}")
        return False
