{% extends 'base.html' %}

{% block title %}Create Announcement{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'notifications_app:admin_notification_dashboard' %}">
                            <i class="fas fa-bell me-1"></i>Notification Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Create Announcement</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>Create New Announcement
                    </h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" id="announcementForm">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                <i class="fas fa-heading me-1"></i>Announcement Title
                            </label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.title.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Enter a clear and descriptive title for your announcement.</div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.announcement_text.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Announcement Message
                            </label>
                            {{ form.announcement_text }}
                            {% if form.announcement_text.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.announcement_text.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Write the full announcement message that users will receive.</div>
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.target_audience.id_for_label }}" class="form-label">
                                <i class="fas fa-users me-1"></i>Target Audience
                            </label>
                            {{ form.target_audience }}
                            {% if form.target_audience.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.target_audience.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Select who should receive this announcement.</div>
                        </div>

                        <!-- Preview Section -->
                        <div class="card bg-light mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-eye me-1"></i>Preview</h6>
                            </div>
                            <div class="card-body">
                                <div id="preview-content">
                                    <h6 id="preview-title" class="text-muted">Title will appear here...</h6>
                                    <p id="preview-message" class="text-muted">Message will appear here...</p>
                                    <small id="preview-audience" class="text-muted">Target audience: Not selected</small>
                                </div>
                            </div>
                        </div>

                        <!-- Warning Alert -->
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> Once sent, announcements cannot be edited or recalled. 
                            Please review your message carefully before sending.
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'notifications_app:admin_notification_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="sendButton">
                                <i class="fas fa-paper-plane me-1"></i>Send Announcement
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card shadow-sm mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-question-circle me-1"></i>Announcement Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Best Practices:</h6>
                            <ul class="small">
                                <li>Keep titles concise and descriptive</li>
                                <li>Use clear, professional language</li>
                                <li>Include relevant dates and deadlines</li>
                                <li>Test with a small group first if possible</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Target Audiences:</h6>
                            <ul class="small">
                                <li><strong>All Users:</strong> Everyone on the platform</li>
                                <li><strong>Customers:</strong> Only customer accounts</li>
                                <li><strong>Service Providers:</strong> Only business accounts</li>
                                <li><strong>Admins:</strong> Only admin accounts</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('{{ form.title.id_for_label }}');
    const messageInput = document.getElementById('{{ form.announcement_text.id_for_label }}');
    const audienceSelect = document.getElementById('{{ form.target_audience.id_for_label }}');
    
    const previewTitle = document.getElementById('preview-title');
    const previewMessage = document.getElementById('preview-message');
    const previewAudience = document.getElementById('preview-audience');

    // Update preview in real-time
    function updatePreview() {
        previewTitle.textContent = titleInput.value || 'Title will appear here...';
        previewMessage.textContent = messageInput.value || 'Message will appear here...';
        
        const selectedOption = audienceSelect.options[audienceSelect.selectedIndex];
        previewAudience.textContent = 'Target audience: ' + (selectedOption.text || 'Not selected');
    }

    // Add event listeners
    titleInput.addEventListener('input', updatePreview);
    messageInput.addEventListener('input', updatePreview);
    audienceSelect.addEventListener('change', updatePreview);

    // Form validation
    document.getElementById('announcementForm').addEventListener('submit', function(e) {
        if (!titleInput.value.trim() || !messageInput.value.trim() || !audienceSelect.value) {
            e.preventDefault();
            alert('Please fill in all required fields before sending the announcement.');
            return false;
        }

        // Confirmation dialog
        const audience = audienceSelect.options[audienceSelect.selectedIndex].text;
        if (!confirm(`Are you sure you want to send this announcement to ${audience}? This action cannot be undone.`)) {
            e.preventDefault();
            return false;
        }
    });

    // Auto-hide success messages after 3 seconds
    const alerts = document.querySelectorAll('.alert-success');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 3000);
    });
});
</script>
{% endblock %}
