# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ..models import VenueFAQ


try:
    from utils.sanitization import sanitize_html
except ImportError:
    sanitize_html = lambda x: x


class VenueFAQForm(forms.ModelForm):
    """Form for creating and editing venue FAQs."""

    class Meta:
        model = VenueFAQ
        fields = ['question', 'answer']
        widgets = {
            'question': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Frequently asked question',
            }),
            'answer': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Answer to the question (max 500 characters)',
                'maxlength': 500,
            }),
        }

    def clean_answer(self):
        return sanitize_html(self.cleaned_data.get('answer', ''))
