{% extends 'booking_cart_app/base.html' %}
{% load static %}

{% block title %}Booking Confirmation - CozyWish{% endblock %}

{% block booking_content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Success Message -->
            <div class="alert alert-success text-center mb-4">
                <h4 class="alert-heading">
                    <i class="fas fa-check-circle"></i> Booking Confirmed!
                </h4>
                <p class="mb-0">Your booking has been successfully confirmed. You will receive a confirmation email shortly.</p>
            </div>

            <!-- Booking Details Card -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check"></i> Booking Details
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Booking Information -->
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Booking ID:</strong></div>
                        <div class="col-sm-8">{{ booking.booking_id }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Status:</strong></div>
                        <div class="col-sm-8">
                            <span class="badge badge-success">{{ booking.get_status_display }}</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Total Amount:</strong></div>
                        <div class="col-sm-8">
                            <strong class="text-success">${{ booking.total_amount }}</strong>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4"><strong>Booking Date:</strong></div>
                        <div class="col-sm-8">{{ booking.created_at|date:"F d, Y g:i A" }}</div>
                    </div>

                    <!-- Booking Items -->
                    <h6 class="mt-4 mb-3">
                        <i class="fas fa-list"></i> Booked Services
                    </h6>
                    {% for item in booking_items %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="card-title">{{ item.service_title }}</h6>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-map-marker-alt"></i> 
                                        {{ item.service.venue.venue_name }}
                                    </p>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-clock"></i> 
                                        {{ item.duration_minutes }} minutes
                                    </p>
                                </div>
                                <div class="col-md-4 text-md-right">
                                    <p class="mb-1">
                                        <strong>Date:</strong> {{ item.scheduled_date|date:"M d, Y" }}
                                    </p>
                                    <p class="mb-1">
                                        <strong>Time:</strong> {{ item.scheduled_time|time:"g:i A" }}
                                    </p>
                                    <p class="mb-1">
                                        <strong>Price:</strong> ${{ item.service_price }}
                                    </p>
                                    <p class="mb-0">
                                        <strong>Quantity:</strong> {{ item.quantity }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i> What's Next?
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-envelope text-primary"></i>
                            You will receive a confirmation email with all booking details
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-bell text-warning"></i>
                            We'll send you a reminder 24 hours before your appointment
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-phone text-success"></i>
                            The service provider may contact you to confirm details
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-calendar text-info"></i>
                            You can view and manage your bookings in your account
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center mt-4 mb-4">
                <a href="{% url 'booking_cart_app:booking_list' %}" class="btn btn-primary">
                    <i class="fas fa-list"></i> View My Bookings
                </a>
                <a href="/" class="btn btn-outline-secondary ml-2">
                    <i class="fas fa-home"></i> Back to Home
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
