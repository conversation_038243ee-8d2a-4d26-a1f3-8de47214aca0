{% extends 'review_app/base_review.html' %}

{% block title %}Submit Review - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block review_extra_css %}
{% load static %}
{% load widget_tweaks %}
<style>
    /* Submit review specific styles - black & white theme */
    .star-rating {
        font-size: 2rem;
        color: rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .star-rating.active {
        color: black;
    }

    .star-rating:hover {
        color: black;
    }

    /* Venue info card */
    .venue-info {
        background: white;
        border: 2px solid black;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .venue-info h4 {
        font-family: var(--font-heading);
        font-weight: 700;
        color: black;
        margin-bottom: 0.5rem;
    }

    /* Carousel styling */
    .carousel {
        border: 2px solid black;
        border-radius: 1rem;
        overflow: hidden;
    }

    .carousel-control-prev,
    .carousel-control-next {
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        top: 50%;
        transform: translateY(-50%);
    }

    .carousel-control-prev {
        left: 10px;
    }

    .carousel-control-next {
        right: 10px;
    }

    /* Accordion styling */
    .review-wrapper .accordion-item {
        border: 2px solid black;
        border-radius: 1rem;
        background-color: white;
    }

    .review-wrapper .accordion-button {
        background-color: white;
        color: black;
        border: none;
        font-family: var(--font-primary);
        font-weight: 500;
    }

    .review-wrapper .accordion-button:not(.collapsed) {
        background-color: black;
        color: white;
    }

    .review-wrapper .accordion-button:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    }

    .review-wrapper .accordion-body {
        background-color: white;
        color: black;
    }

    /* Rating text */
    #rating-text {
        font-family: var(--font-primary);
        color: rgba(0, 0, 0, 0.6);
    }

    /* Form styling improvements */
    .form-label.fw-bold {
        font-family: var(--font-heading);
        font-weight: 600;
        color: black;
    }

    .text-danger {
        color: black !important;
        background-color: rgba(0, 0, 0, 0.05);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        border: 1px solid black;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star rating functionality
    const stars = document.querySelectorAll('.star-rating');
    const ratingInput = document.getElementById('id_rating');
    
    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingInput.value = rating;
            updateStars(rating);
        });

        star.addEventListener('mouseover', function() {
            const rating = index + 1;
            updateStars(rating);
        });

        star.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const rating = index + 1;
                ratingInput.value = rating;
                updateStars(rating);
            }
        });
    });
    
    // Reset stars on mouse leave
    document.querySelector('.star-container').addEventListener('mouseleave', function() {
        const currentRating = ratingInput.value || 0;
        updateStars(currentRating);
    });
    
    function updateStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
                star.setAttribute('aria-checked', 'true');
            } else {
                star.classList.remove('active');
                star.setAttribute('aria-checked', 'false');
            }
        });
    }
    
    // Initialize stars based on current value
    const currentRating = ratingInput.value || 0;
    updateStars(currentRating);
});
</script>
{% endblock %}

{% block review_content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_list' %}">Venues</a></li>
        <li class="breadcrumb-item"><a href="{% url 'venues_app:venue_detail' venue.slug %}">{{ venue.venue_name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">Submit Review</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="text-center mb-4">
    <h3 class="mb-0">
        <i class="fas fa-star me-2"></i>Submit Your Review
    </h3>
</div>

<!-- Venue Information -->
<div class="venue-info">
    <div class="row">
        <div class="col-md-4">
            <div id="venueCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner ratio ratio-4x3">
                    {% if venue.main_image %}
                    <div class="carousel-item active">
                        <img src="{{ venue.main_image.url }}" alt="{{ venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% else %}
                    <div class="carousel-item active">
                        <div class="bg-light d-flex align-items-center justify-content-center h-100">
                            <i class="fas fa-image fa-2x text-muted"></i>
                        </div>
                    </div>
                    {% endif %}
                    {% for image in venue.images.all %}
                    <div class="carousel-item{% if not venue.main_image and forloop.first %} active{% endif %}">
                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:venue.venue_name }}" class="d-block w-100 h-100" style="object-fit: cover;">
                    </div>
                    {% endfor %}
                </div>
                {% if venue.images.all|length > 1 or venue.main_image and venue.images.all %}
                <button class="carousel-control-prev" type="button" data-bs-target="#venueCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#venueCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
                {% endif %}
            </div>
        </div>
        <div class="col-md-8">
            <h4>{{ venue.venue_name }}</h4>
            <p class="text-muted mb-2">{{ venue.service_provider.business_name }}</p>
            <p class="mb-0">{{ venue.short_description|truncatewords:20 }}</p>
        </div>
    </div>
</div>

                    <!-- Review Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Star Rating -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">{{ form.rating.label }}</label>
                            <div class="star-container d-flex align-items-center mb-2" role="radiogroup" aria-label="Star rating">
                                {% for i in "12345" %}
                                    <i class="fas fa-star star-rating me-1" data-rating="{{ forloop.counter }}" role="radio" aria-label="{{ forloop.counter }} star{% if forloop.counter != 1 %}s{% endif %}" aria-checked="false" tabindex="0"></i>
                                {% endfor %}
                                <span class="ms-3 text-muted" id="rating-text">Click to rate</span>
                            </div>
                            {{ form.rating|add_class:"d-none" }}
                            {% if form.rating.errors %}
                                <div class="text-danger small">
                                    {% for error in form.rating.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.rating.help_text %}
                                <small class="form-text text-muted">{{ form.rating.help_text }}</small>
                            {% endif %}
                        </div>

                        <!-- Written Review -->
                        <div class="mb-4">
                            <label class="form-label fw-bold" for="{{ form.written_review.id_for_label }}">
                                {{ form.written_review.label }}
                            </label>
                            {{ form.written_review|add_class:"form-control" }}
                            {% if form.written_review.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.written_review.errors %}
                                        <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.written_review.help_text %}
                                <small class="form-text text-muted">{{ form.written_review.help_text }}</small>
                            {% endif %}
                        </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'venues_app:venue_detail' venue.slug %}"
                           class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Submit Review
                        </button>
                    </div>
                </form>

<!-- Review Guidelines -->
<div class="accordion mt-4" id="guidelinesAccordion">
    <div class="accordion-item">
        <h2 class="accordion-header" id="headingGuide">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGuide" aria-expanded="false" aria-controls="collapseGuide">
                <i class="fas fa-info-circle me-2"></i>Review Guidelines
            </button>
        </h2>
        <div id="collapseGuide" class="accordion-collapse collapse">
            <div class="accordion-body">
                <ul class="mb-0 text-muted">
                    <li>Be honest and fair in your review</li>
                    <li>Focus on your personal experience</li>
                    <li>Keep your language professional and respectful</li>
                    <li>Avoid sharing personal information</li>
                    <li>Reviews help other customers make informed decisions</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
