{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ action }} Team Member - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Team Member Form */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Team Member Form Section */
    .team-form-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
    }

    .team-form-container {
        max-width: 800px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    /* Header */
    .team-header {
        display: flex;
        align-items: center;
        margin-bottom: 3rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .back-btn {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-right: 1.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        font-size: 1.125rem;
    }

    .back-btn:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    .team-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .team-subtitle {
        font-size: 1.125rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
    }

    /* Info Alert */
    .team-info-alert {
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: var(--cw-brand-primary);
        display: flex;
        align-items: center;
        gap: 1rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .team-info-alert i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    /* Form Card */
    .form-card {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 1.5rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .form-card-body {
        padding: 3rem;
    }

    /* Form Styling */
    .form-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control,
    .form-select {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-family: var(--cw-font-primary);
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control::placeholder {
        color: var(--cw-neutral-600);
        opacity: 0.7;
    }

    .form-text {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    /* Profile Picture Preview */
    .profile-picture-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid var(--cw-brand-accent);
        box-shadow: var(--cw-shadow-md);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .profile-picture-preview:hover {
        border-color: var(--cw-brand-primary);
        transform: scale(1.02);
    }

    .profile-picture-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: var(--cw-accent-light);
        border: 4px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .profile-picture-placeholder:hover {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
    }

    .profile-picture-container {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    /* Checkbox Styling */
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.25rem;
        background-color: white;
        transition: all 0.2s ease;
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
    }

    .form-check-label {
        font-family: var(--cw-font-heading);
        font-weight: 500;
        color: var(--cw-brand-primary);
        margin-left: 0.5rem;
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Action Buttons */
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: space-between;
        padding: 2rem 0;
        border-top: 2px solid var(--cw-brand-accent);
        margin-top: 2rem;
    }

    /* Tips Card */
    .tips-card {
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        border-radius: 1rem;
        padding: 2rem;
        margin-top: 2rem;
        box-shadow: var(--cw-shadow-sm);
    }

    .tips-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tips-list li {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1rem;
        color: var(--cw-neutral-700);
        line-height: 1.5;
    }

    .tips-list li:before {
        content: '•';
        color: var(--cw-brand-primary);
        font-weight: bold;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    /* Alert Styling */
    .alert {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-primary);
        font-weight: 500;
    }

    .alert-success {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
    }

    .alert-danger {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }

    .alert-info {
        background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .team-form-section {
            padding: 3rem 0;
        }

        .team-form-container {
            padding: 0 1.5rem;
        }

        .team-header {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .back-btn {
            margin-right: 0;
            margin-bottom: 1rem;
        }

        .team-title {
            font-size: 2rem;
        }

        .form-card-body {
            padding: 2rem;
        }

        .form-actions {
            flex-direction: column-reverse;
            align-items: center;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .team-form-container {
            padding: 0 1rem;
        }

        .form-card-body {
            padding: 1.5rem;
        }

        .team-title {
            font-size: 1.75rem;
        }

        .tips-card {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
/**
 * Team Member Form JavaScript
 * Handles photo preview and form validation
 */
document.addEventListener('DOMContentLoaded', function() {
    // Photo preview functionality
    const photoInput = document.getElementById('{{ form.staff_profile_picture.id_for_label }}');
    const photoPreview = document.querySelector('.profile-picture-preview');
    const photoPlaceholder = document.querySelector('.profile-picture-placeholder');

    if (photoInput) {
        photoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                // Validate file type
                const file = this.files[0];
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG or PNG).');
                    this.value = '';
                    return;
                }

                // Validate file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB.');
                    this.value = '';
                    return;
                }

                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (photoPreview) {
                        photoPreview.src = e.target.result;
                    } else if (photoPlaceholder) {
                        // Replace placeholder with actual image
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'profile-picture-preview';
                        img.alt = 'Team Member Photo';
                        photoPlaceholder.parentNode.replaceChild(img, photoPlaceholder);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    }
});
</script>
{% endblock %}

{% block content %}
<section class="team-form-section">
    <div class="team-form-container">
        <!-- Header -->
        <div class="team-header">
            <a href="{% url 'accounts_app:service_provider_profile' %}" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="team-title">{{ action }} Team Member</h1>
                <p class="team-subtitle">
                    {% if action == 'Add' %}
                        Add a new team member to your business
                    {% else %}
                        Update team member information
                    {% endif %}
                </p>
            </div>
        </div>

        <!-- Team limit info for add -->
        {% if action == 'Add' %}
            <div class="team-info-alert">
                <i class="fas fa-info-circle"></i>
                <div>
                    <strong>Team Limit:</strong> You can add up to {{ max_members }} team members.
                    Currently you have {{ current_count }} member{{ current_count|pluralize }}.
                </div>
            </div>
        {% endif %}

        <!-- Display messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Form Card -->
        <div class="form-card">
            <div class="form-card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Staff Name -->
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.staff_name.id_for_label }}">{{ form.staff_name.label }}</label>
                        {{ form.staff_name|add_class:"form-control"|attr:"placeholder:Enter team member name" }}
                        {% if form.staff_name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.staff_name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Staff Position -->
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.staff_position.id_for_label }}">{{ form.staff_position.label }}</label>
                        {{ form.staff_position|add_class:"form-control"|attr:"placeholder:e.g., Licensed Massage Therapist" }}
                        {% if form.staff_position.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.staff_position.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Staff Profile Picture -->
                    <div class="mb-3">
                        <label class="form-label" for="{{ form.staff_profile_picture.id_for_label }}">{{ form.staff_profile_picture.label }}</label>

                        <div class="profile-picture-container">
                            {% if action == 'Edit' and team_member.photo %}
                                <img src="{{ team_member.photo.url }}" alt="{{ team_member.name }}" class="profile-picture-preview">
                                <p class="form-text">Current photo</p>
                            {% else %}
                                <div class="profile-picture-placeholder">
                                    <i class="fas fa-user fa-2x" style="color: var(--cw-neutral-600);"></i>
                                </div>
                                <p class="form-text">No photo uploaded</p>
                            {% endif %}
                        </div>

                        {{ form.staff_profile_picture|add_class:"form-control" }}
                        {% if form.staff_profile_picture.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.staff_profile_picture.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                        {% if form.staff_profile_picture.help_text %}
                            <div class="form-text">{{ form.staff_profile_picture.help_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Active Status -->
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_active|add_class:"form-check-input" }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                            {% if form.is_active.help_text %}
                                <div class="form-text">{{ form.is_active.help_text }}</div>
                            {% endif %}
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-actions">
                        <a href="{% url 'accounts_app:service_provider_profile' %}" class="btn-cw-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-{% if action == 'Add' %}plus{% else %}save{% endif %}"></i>
                            {% if action == 'Add' %}Add Team Member{% else %}Save Changes{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tips for team management -->
        <div class="tips-card">
            <h6 class="tips-title">
                <i class="fas fa-lightbulb"></i>
                Tips for Team Management
            </h6>
            <ul class="tips-list">
                <li>Use clear, professional photos for better customer trust</li>
                <li>Include specific titles (e.g., "Licensed Massage Therapist" vs "Therapist")</li>
                <li>You can temporarily deactivate team members without removing them</li>
                <li>Team member information helps customers choose the right service provider</li>
            </ul>
        </div>
    </div>
</section>
{% endblock %}
