"""
Unit tests for discount_app views.

This module contains comprehensive unit tests for all view classes in the discount_app,
including customer views, provider views, and admin views.
"""

# Standard library imports
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.messages import get_messages

# Local imports
from discount_app.models import (
    DiscountType, VenueDiscount, ServiceDiscount, PlatformDiscount, DiscountUsage
)
from venues_app.models import Category, Venue, Service
from accounts_app.models import ServiceProviderProfile

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False, STATICFILES_STORAGE='django.contrib.staticfiles.storage.StaticFilesStorage')
class DiscountViewBaseTest(TestCase):
    """Base test class for discount views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create users
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer_user.role = User.CUSTOMER
        self.customer_user.save()

        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider_user.role = User.SERVICE_PROVIDER
        self.provider_user.save()

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.admin_user.role = User.ADMIN
        self.admin_user.is_staff = True
        self.admin_user.save()

        # Create category
        self.category = Category.objects.create(
            name='Spa Services',
            description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            business_name='Test Spa',
            business_phone_number='+**********',
            contact_person_name='John Doe',
            business_address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            venue_address='123 Spa St',
            city='New York',
            state='NY',
            zip_code='10001',
            phone_number='+**********',
            category=self.category,
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Massage Therapy',
            service_description='Relaxing massage',
            price_min=Decimal('100.00'),
            price_max=Decimal('200.00'),
            duration_minutes=60,
            is_active=True
        )


class CustomerDiscountViewsTest(DiscountViewBaseTest):
    """Test customer-facing discount views."""

    def test_featured_discounts_view_anonymous_user(self):
        """Test featured discounts view for anonymous users."""
        # Create featured platform discount
        PlatformDiscount.objects.create(
            name='Featured Summer Sale',
            description='Platform-wide summer discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            is_featured=True,
            created_by=self.admin_user
        )

        url = reverse('discount_app:featured_discounts')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Featured Summer Sale')

    def test_featured_discounts_view_authenticated_user(self):
        """Test featured discounts view for authenticated users."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('discount_app:featured_discounts')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)

    def test_venue_discounts_view(self):
        """Test venue discounts view."""
        # Create approved venue discount
        VenueDiscount.objects.create(
            venue=self.venue,
            name='Venue Special',
            description='Special venue discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        url = reverse('discount_app:venue_discounts', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Venue Special')

    def test_venue_discounts_view_nonexistent_venue(self):
        """Test venue discounts view with nonexistent venue."""
        url = reverse('discount_app:venue_discounts', kwargs={'venue_id': 99999})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)

    def test_search_discounts_view_get(self):
        """Test search discounts view GET request."""
        url = reverse('discount_app:search_discounts')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Search Discounts')

    def test_search_discounts_view_post_with_query(self):
        """Test search discounts view POST request with search query."""
        # Create searchable discount
        ServiceDiscount.objects.create(
            service=self.service,
            name='Massage Special Offer',
            description='Special discount for massage therapy',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('25.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        url = reverse('discount_app:search_discounts')
        response = self.client.post(url, {'query': 'massage'})
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Massage Special Offer')

    def test_search_discounts_view_post_empty_query(self):
        """Test search discounts view POST request with empty query."""
        url = reverse('discount_app:search_discounts')
        response = self.client.post(url, {'query': ''})
        
        self.assertEqual(response.status_code, 200)


class ProviderDiscountViewsTest(DiscountViewBaseTest):
    """Test provider discount views."""

    def test_provider_discount_list_view_requires_login(self):
        """Test provider discount list view requires login."""
        url = reverse('discount_app:provider_discount_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_provider_discount_list_view_requires_provider_role(self):
        """Test provider discount list view requires provider role."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('discount_app:provider_discount_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_provider_discount_list_view_success(self):
        """Test provider discount list view for authenticated provider."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create some discounts
        ServiceDiscount.objects.create(
            service=self.service,
            name='Provider Service Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )

        VenueDiscount.objects.create(
            venue=self.venue,
            name='Provider Venue Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )

        url = reverse('discount_app:provider_discount_list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Provider Service Discount')
        self.assertContains(response, 'Provider Venue Discount')

    def test_create_service_discount_view_get(self):
        """Test create service discount view GET request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('discount_app:create_service_discount')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Service Discount')

    def test_create_service_discount_view_post_valid(self):
        """Test create service discount view POST request with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        form_data = {
            'service': self.service.id,
            'name': 'New Service Discount',
            'description': 'Test discount description',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '25.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
        }
        
        url = reverse('discount_app:create_service_discount')
        response = self.client.post(url, form_data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Check discount was created
        discount = ServiceDiscount.objects.get(name='New Service Discount')
        self.assertEqual(discount.service, self.service)
        self.assertEqual(discount.created_by, self.provider_user)

    def test_create_service_discount_view_post_invalid(self):
        """Test create service discount view POST request with invalid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        form_data = {
            'service': self.service.id,
            'name': '',  # Invalid: empty name
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '25.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
        }
        
        url = reverse('discount_app:create_service_discount')
        response = self.client.post(url, form_data)
        
        self.assertEqual(response.status_code, 200)  # Stay on form page
        self.assertContains(response, 'This field is required')

    def test_create_venue_discount_view_get(self):
        """Test create venue discount view GET request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        url = reverse('discount_app:create_venue_discount')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Venue Discount')

    def test_create_venue_discount_view_post_valid(self):
        """Test create venue discount view POST request with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        form_data = {
            'name': 'New Venue Discount',
            'description': 'Test venue discount description',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '20.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
            'min_booking_value': '50.00',
        }
        
        url = reverse('discount_app:create_venue_discount')
        response = self.client.post(url, form_data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Check discount was created
        discount = VenueDiscount.objects.get(name='New Venue Discount')
        self.assertEqual(discount.venue, self.venue)
        self.assertEqual(discount.created_by, self.provider_user)

    def test_update_service_discount_view_get(self):
        """Test update service discount view GET request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create discount to update
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Discount to Update',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )
        
        url = reverse('discount_app:edit_service_discount', kwargs={'discount_slug': discount.slug})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Update Service Discount')
        self.assertContains(response, 'Discount to Update')

    def test_update_service_discount_view_post_valid(self):
        """Test update service discount view POST request with valid data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create discount to update
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Original Name',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )
        
        form_data = {
            'service': self.service.id,
            'name': 'Updated Name',
            'description': 'Updated description',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '30.00',
            'start_date': (timezone.now() + timedelta(hours=2)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=8)).strftime('%Y-%m-%dT%H:%M'),
        }
        
        url = reverse('discount_app:edit_service_discount', kwargs={'discount_slug': discount.slug})
        response = self.client.post(url, form_data)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Check discount was updated
        discount.refresh_from_db()
        self.assertEqual(discount.name, 'Updated Name')
        self.assertEqual(discount.discount_value, Decimal('30.00'))

    def test_delete_service_discount_view_get(self):
        """Test delete service discount view GET request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create discount to delete
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Discount to Delete',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )
        
        url = reverse('discount_app:delete_service_discount', kwargs={'discount_slug': discount.slug})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Delete Service Discount')
        self.assertContains(response, 'Discount to Delete')

    def test_delete_service_discount_view_post(self):
        """Test delete service discount view POST request."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create discount to delete
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Discount to Delete',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )
        
        url = reverse('discount_app:delete_service_discount', kwargs={'discount_slug': discount.slug})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        
        # Check discount was deleted
        self.assertFalse(ServiceDiscount.objects.filter(pk=discount.pk).exists())

    def test_provider_cannot_access_other_provider_discount(self):
        """Test that provider cannot access another provider's discount."""
        # Create another provider with their own venue and service
        other_provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_provider.role = User.SERVICE_PROVIDER
        other_provider.save()

        # Create service provider profile for other provider
        other_provider_profile = ServiceProviderProfile.objects.create(
            user=other_provider,
            business_name='Other Spa',
            business_phone_number='+**********',
            contact_person_name='Jane Doe',
            business_address='456 Other St',
            city='Los Angeles',
            state='CA',
            zip_code='90001'
        )

        # Create venue for other provider
        other_venue = Venue.objects.create(
            service_provider=other_provider_profile,
            venue_name='Other Spa Venue',
            venue_address='456 Other Spa St',
            city='Los Angeles',
            state='CA',
            zip_code='90001',
            phone_number='+**********',
            category=self.category,
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )

        # Create service for other provider
        other_service = Service.objects.create(
            venue=other_venue,
            service_title='Other Massage Therapy',
            service_description='Other relaxing massage',
            price_min=Decimal('80.00'),
            price_max=Decimal('150.00'),
            duration_minutes=45,
            is_active=True
        )

        # Create discount by other provider for their own service
        discount = ServiceDiscount.objects.create(
            service=other_service,
            name='Other Provider Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=other_provider
        )

        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('discount_app:edit_service_discount', kwargs={'discount_slug': discount.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 302)  # Redirect to discount list (permission denied)

    def test_view_messages_on_success(self):
        """Test that success messages are displayed."""
        self.client.login(email='<EMAIL>', password='testpass123')

        form_data = {
            'service': self.service.id,
            'name': 'Test Success Message',
            'description': 'Test discount',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '25.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
        }

        url = reverse('discount_app:create_service_discount')
        response = self.client.post(url, form_data, follow=True)

        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('successfully created' in str(message) for message in messages))

    def test_view_messages_on_error(self):
        """Test that error messages are displayed."""
        self.client.login(email='<EMAIL>', password='testpass123')

        form_data = {
            'service': self.service.id,
            'name': '',  # Invalid: empty name
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '25.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=7)).strftime('%Y-%m-%dT%H:%M'),
        }

        url = reverse('discount_app:create_service_discount')
        response = self.client.post(url, form_data)

        self.assertEqual(response.status_code, 200)  # Stay on form page
        self.assertContains(response, 'This field is required')


class AdminDiscountViewsTest(DiscountViewBaseTest):
    """Test admin discount views."""

    def test_admin_discount_list_view_requires_login(self):
        """Test admin discount list view requires login."""
        url = reverse('discount_app:admin_discount_list', kwargs={'discount_type': 'service'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_admin_discount_list_view_requires_admin_role(self):
        """Test admin discount list view requires admin role."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('discount_app:admin_discount_list', kwargs={'discount_type': 'service'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)  # Forbidden

    def test_admin_discount_list_view_success(self):
        """Test admin discount list view for authenticated admin."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create some discounts
        ServiceDiscount.objects.create(
            service=self.service,
            name='Service Discount for Review',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False
        )

        PlatformDiscount.objects.create(
            name='Platform Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            created_by=self.admin_user
        )

        url = reverse('discount_app:admin_discount_list', kwargs={'discount_type': 'service'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Service Discount for Review')
        self.assertContains(response, 'Platform Discount')

    def test_admin_create_platform_discount_view_get(self):
        """Test admin create platform discount view GET request."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        url = reverse('discount_app:admin_create_platform_discount')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Create Platform Discount')

    def test_admin_create_platform_discount_view_post_valid(self):
        """Test admin create platform discount view POST request with valid data."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        form_data = {
            'name': 'New Platform Discount',
            'description': 'Test platform discount description',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': '25.00',
            'start_date': (timezone.now() + timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'end_date': (timezone.now() + timedelta(days=30)).strftime('%Y-%m-%dT%H:%M'),
            'category': self.category.id,
            'min_booking_value': '100.00',
            'is_featured': True,
        }

        url = reverse('discount_app:admin_create_platform_discount')
        response = self.client.post(url, form_data)

        self.assertEqual(response.status_code, 302)  # Redirect after success

        # Check discount was created
        discount = PlatformDiscount.objects.get(name='New Platform Discount')
        self.assertEqual(discount.category, self.category)
        self.assertEqual(discount.created_by, self.admin_user)
        self.assertTrue(discount.is_featured)

    def test_admin_discount_list_pending_view(self):
        """Admin list pending service discounts."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        ServiceDiscount.objects.create(
            service=self.service,
            name='Discount Pending Approval',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False
        )

        url = reverse('discount_app:admin_discount_list', kwargs={'discount_type': 'service'}) + '?status=pending'
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Discount Pending Approval')

    def test_admin_approve_discount_view_post(self):
        """Test admin approve discount view POST request."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create unapproved discount
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Discount to Approve',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False
        )

        url = reverse('discount_app:admin_approve_discount', kwargs={'discount_type': 'service', 'discount_id': discount.id})
        response = self.client.post(url, {'action': 'approve'})

        self.assertEqual(response.status_code, 302)  # Redirect after success

        # Check discount was approved
        discount.refresh_from_db()
        self.assertTrue(discount.is_approved)
        self.assertEqual(discount.approved_by, self.admin_user)
        self.assertIsNotNone(discount.approved_at)

    def test_admin_reject_discount_view_post(self):
        """Test admin reject discount view POST request."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create approved discount
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Discount to Reject',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True,
            approved_by=self.admin_user
        )

        url = reverse('discount_app:admin_approve_discount', kwargs={'discount_type': 'service', 'discount_id': discount.id})
        response = self.client.post(url, {'action': 'reject'})

        self.assertEqual(response.status_code, 302)  # Redirect after success

        # Check discount was rejected
        discount.refresh_from_db()
        self.assertFalse(discount.is_approved)
        self.assertIsNone(discount.approved_by)
        self.assertIsNone(discount.approved_at)

    def test_admin_discount_dashboard_view(self):
        """Test admin discount dashboard view."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create various discounts for dashboard stats
        ServiceDiscount.objects.create(
            service=self.service,
            name='Active Service Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        VenueDiscount.objects.create(
            venue=self.venue,
            name='Pending Venue Discount',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('15.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=False
        )

        url = reverse('discount_app:admin_discount_dashboard')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Discount Dashboard')

    def test_admin_discount_detail_view(self):
        """Test admin discount detail view."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create discount to view
        discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Discount Detail View',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user
        )

        url = reverse('discount_app:admin_discount_detail', kwargs={'discount_type': 'service', 'discount_id': discount.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Discount Detail View')
        self.assertContains(response, discount.service.service_title)

    def test_admin_update_platform_discount_view_get(self):
        """Test admin update platform discount view GET request."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create platform discount to update
        discount = PlatformDiscount.objects.create(
            name='Platform Discount to Update',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            created_by=self.admin_user
        )

        url = reverse('discount_app:admin_edit_platform_discount', kwargs={'discount_id': discount.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Update Platform Discount')
        self.assertContains(response, 'Platform Discount to Update')

    def test_admin_delete_platform_discount_view_post(self):
        """Test admin delete platform discount view POST request."""
        self.client.login(email='<EMAIL>', password='adminpass123')

        # Create platform discount to delete
        discount = PlatformDiscount.objects.create(
            name='Platform Discount to Delete',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=30),
            created_by=self.admin_user
        )

        url = reverse('discount_app:admin_delete_platform_discount', kwargs={'discount_id': discount.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 302)  # Redirect after success

        # Check discount was deleted
        self.assertFalse(PlatformDiscount.objects.filter(pk=discount.pk).exists())


class DiscountViewPermissionTest(DiscountViewBaseTest):
    """Test discount view permissions and security."""

    def test_customer_cannot_access_provider_views(self):
        """Test that customers cannot access provider views."""
        self.client.login(email='<EMAIL>', password='testpass123')

        provider_urls = [
            reverse('discount_app:provider_discount_list'),
            reverse('discount_app:create_service_discount'),
            reverse('discount_app:create_venue_discount'),
        ]

        for url in provider_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 403, f'URL {url} should be forbidden for customers')

    def test_customer_cannot_access_admin_views(self):
        """Test that customers cannot access admin views."""
        self.client.login(email='<EMAIL>', password='testpass123')

        admin_urls = [
            reverse('discount_app:admin_discount_list', kwargs={'discount_type': 'service'}),
            reverse('discount_app:admin_create_platform_discount'),
            reverse('discount_app:admin_discount_dashboard'),
        ]

        for url in admin_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 403, f'URL {url} should be forbidden for customers')

    def test_provider_cannot_access_admin_views(self):
        """Test that providers cannot access admin views."""
        self.client.login(email='<EMAIL>', password='testpass123')

        admin_urls = [
            reverse('discount_app:admin_discount_list', kwargs={'discount_type': 'service'}),
            reverse('discount_app:admin_create_platform_discount'),
            reverse('discount_app:admin_discount_dashboard'),
        ]

        for url in admin_urls:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 403, f'URL {url} should be forbidden for providers')

    @patch('discount_app.views.common.log_unauthorized_discount_access')
    def test_unauthorized_access_logging(self, mock_log_unauthorized):
        """Test that unauthorized access attempts are logged."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('discount_app:provider_discount_list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)
        # Check that unauthorized access was logged
        mock_log_unauthorized.assert_called_once()
