from io import <PERSON><PERSON>
from datetime import timedelta
from django.core.management import call_command
from django.utils import timezone
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model

from admin_app.models import SystemHealthLog


@override_settings(SECURE_SSL_REDIRECT=False)
class PruneSystemHealthLogsCommandTest(TestCase):
    def test_old_logs_deleted(self):
        user = get_user_model().objects.create_user(email='<EMAIL>', password='pass')
        old_log = SystemHealthLog.objects.create(
            event_type='info', severity='low', title='Old', description='old',
            affected_user=user, recorded_at=timezone.now() - timedelta(days=365)
        )
        recent_log = SystemHealthLog.objects.create(
            event_type='info', severity='low', title='Recent', description='recent'
        )
        out = StringIO()
        call_command('prune_system_health_logs', '--days', '180', stdout=out)
        self.assertFalse(SystemHealthLog.objects.filter(id=old_log.id).exists())
        self.assertTrue(SystemHealthLog.objects.filter(id=recent_log.id).exists())
