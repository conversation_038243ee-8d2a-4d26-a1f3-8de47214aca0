"""
Unit tests for admin_app forms.

This module contains comprehensive unit tests for all form classes in the admin_app,
including StaticPageForm, BlogCategoryForm, BlogPostForm, HomepageBlockForm, MediaFileForm,
SiteConfigurationForm, AnnouncementForm, SystemHealthLogForm, and BulkUserActionForm.
"""

# Standard library imports
import tempfile
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.core.files.uploadedfile import SimpleUploadedFile

# Local imports
from admin_app.forms import (
    StaticPageForm, BlogCategoryForm, BlogPostForm, HomepageBlockForm,
    MediaFileForm, SiteConfigurationForm, AnnouncementForm,
    SystemHealthLogForm, BulkUserActionForm
)
from admin_app.models import (
    StaticPage, BlogCategory, BlogPost, HomepageBlock, MediaFile,
    SiteConfiguration, Announcement, SystemHealthLog
)
from .test_utils import create_test_image, create_large_test_image, create_invalid_image_file

User = get_user_model()


class StaticPageFormTest(TestCase):
    """Test the StaticPageForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

    def test_valid_static_page_form(self):
        """Test valid static page form submission."""
        form_data = {
            'title': 'About Us',
            'slug': 'about-us',
            'content': 'This is our about page content.',
            'meta_title': 'About Us - CozyWish',
            'meta_description': 'Learn more about CozyWish and our mission.',
            'meta_keywords': 'about, cozywish, spa, wellness',
            'status': 'published',
            'is_featured': True
        }
        
        form = StaticPageForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_static_page_form_required_fields(self):
        """Test that required fields are validated."""
        form = StaticPageForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)
        self.assertIn('content', form.errors)

    def test_static_page_content_sanitized(self):
        """Unsafe HTML should be stripped from content."""
        form_data = {
            'title': 'Safe',
            'slug': 'safe',
            'content': '<script>alert(1)</script><p>Text</p>',
            'status': 'published'
        }
        form = StaticPageForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['content'], '<p>Text</p>')

    def test_static_page_form_image_validation_valid(self):
        """Test valid image upload."""
        # Create a proper test image
        test_image = create_test_image("test_image.jpg")

        form_data = {
            'title': 'Test Page',
            'content': 'Test content.',
            'status': 'published'
        }

        form = StaticPageForm(data=form_data, files={'featured_image': test_image})
        self.assertTrue(form.is_valid())

    def test_static_page_form_image_validation_invalid_extension(self):
        """Test invalid image extension."""
        test_file = create_invalid_image_file("test_file.txt")

        form_data = {
            'title': 'Test Page',
            'content': 'Test content.',
            'status': 'published'
        }

        form = StaticPageForm(data=form_data, files={'featured_image': test_file})
        self.assertFalse(form.is_valid())
        self.assertIn('featured_image', form.errors)

    def test_static_page_form_image_validation_too_large(self):
        """Test image file size validation."""
        # Create a large test image
        test_image = create_large_test_image("large_image.jpg", size_mb=6)

        form_data = {
            'title': 'Test Page',
            'content': 'Test content.',
            'status': 'published'
        }

        form = StaticPageForm(data=form_data, files={'featured_image': test_image})
        self.assertFalse(form.is_valid())
        self.assertIn('featured_image', form.errors)


class BlogCategoryFormTest(TestCase):
    """Test the BlogCategoryForm functionality."""

    def test_valid_blog_category_form(self):
        """Test valid blog category form submission."""
        form_data = {
            'name': 'Wellness Tips',
            'slug': 'wellness-tips',
            'description': 'Tips for wellness and health.',
            'is_active': True
        }
        
        form = BlogCategoryForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_blog_category_form_required_fields(self):
        """Test that required fields are validated."""
        form = BlogCategoryForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)

    def test_blog_category_form_optional_fields(self):
        """Test form with only required fields."""
        form_data = {
            'name': 'Health Tips'
        }
        
        form = BlogCategoryForm(data=form_data)
        self.assertTrue(form.is_valid())


class BlogPostFormTest(TestCase):
    """Test the BlogPostForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.category = BlogCategory.objects.create(
            name='Test Category',
            is_active=True
        )
        self.inactive_category = BlogCategory.objects.create(
            name='Inactive Category',
            is_active=False
        )

    def test_valid_blog_post_form(self):
        """Test valid blog post form submission."""
        form_data = {
            'title': 'How to Relax at a Spa',
            'slug': 'how-to-relax-at-spa',
            'content': 'This is a comprehensive guide to spa relaxation.',
            'excerpt': 'Learn how to maximize your spa experience.',
            'category': self.category.id,
            'meta_title': 'Spa Relaxation Guide',
            'meta_description': 'Complete guide to spa relaxation techniques.',
            'meta_keywords': 'spa, relaxation, wellness, massage',
            'status': 'published',
            'is_featured': True
        }
        
        form = BlogPostForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_blog_post_form_required_fields(self):
        """Test that required fields are validated."""
        form = BlogPostForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)
        self.assertIn('content', form.errors)

    def test_blog_post_form_category_queryset_active_only(self):
        """Test that only active categories are available in form."""
        form = BlogPostForm()
        category_choices = form.fields['category'].queryset
        
        self.assertIn(self.category, category_choices)
        self.assertNotIn(self.inactive_category, category_choices)

    def test_blog_post_form_image_validation_valid(self):
        """Test valid featured image upload."""
        test_image = create_test_image("featured.png", format="PNG")

        form_data = {
            'title': 'Test Post',
            'content': 'Test content.',
            'status': 'published'
        }

        form = BlogPostForm(data=form_data, files={'featured_image': test_image})
        self.assertTrue(form.is_valid())

    def test_blog_post_form_image_validation_invalid(self):
        """Test invalid featured image upload."""
        test_file = create_invalid_image_file("document.pdf")

        form_data = {
            'title': 'Test Post',
            'content': 'Test content.',
            'status': 'published'
        }

        form = BlogPostForm(data=form_data, files={'featured_image': test_file})
        self.assertFalse(form.is_valid())
        self.assertIn('featured_image', form.errors)


class HomepageBlockFormTest(TestCase):
    """Test the HomepageBlockForm functionality."""

    def test_valid_homepage_block_form(self):
        """Test valid homepage block form submission."""
        form_data = {
            'block_type': 'hero',
            'title': 'Welcome to CozyWish',
            'subtitle': 'Find your perfect spa experience',
            'content': 'Discover amazing spa deals in your area.',
            'button_text': 'Get Started',
            'button_url': 'https://example.com/signup',
            'is_active': True,
            'display_order': 1
        }
        
        form = HomepageBlockForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_homepage_block_form_required_fields(self):
        """Test that required fields are validated."""
        form = HomepageBlockForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('block_type', form.errors)
        self.assertIn('title', form.errors)

    def test_homepage_block_form_button_validation_text_without_url(self):
        """Test validation when button text is provided without URL."""
        form_data = {
            'block_type': 'call_to_action',
            'title': 'Join Today',
            'button_text': 'Sign Up Now',
            'button_url': '',  # Missing URL
            'display_order': 1
        }

        form = HomepageBlockForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_homepage_block_form_button_validation_url_without_text(self):
        """Test validation when button URL is provided without text."""
        form_data = {
            'block_type': 'call_to_action',
            'title': 'Join Today',
            'button_text': '',  # Missing text
            'button_url': 'https://example.com/signup',
            'display_order': 1
        }

        form = HomepageBlockForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_homepage_block_form_button_validation_both_provided(self):
        """Test validation when both button text and URL are provided."""
        form_data = {
            'block_type': 'call_to_action',
            'title': 'Join Today',
            'button_text': 'Sign Up Now',
            'button_url': 'https://example.com/signup',
            'display_order': 1
        }

        form = HomepageBlockForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_homepage_block_form_button_validation_neither_provided(self):
        """Test validation when neither button text nor URL are provided."""
        form_data = {
            'block_type': 'features',
            'title': 'Our Features',
            'button_text': '',
            'button_url': '',
            'display_order': 2
        }

        form = HomepageBlockForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_homepage_block_form_image_validation(self):
        """Test image validation for homepage blocks."""
        test_image = create_test_image("block_image.jpg")

        form_data = {
            'block_type': 'hero',
            'title': 'Hero Block',
            'display_order': 1
        }

        form = HomepageBlockForm(data=form_data, files={'image': test_image})
        self.assertTrue(form.is_valid())


class MediaFileFormTest(TestCase):
    """Test the MediaFileForm functionality."""

    def test_valid_media_file_form(self):
        """Test valid media file form submission."""
        test_file = SimpleUploadedFile(
            "test_document.pdf",
            b"fake pdf content",
            content_type="application/pdf"
        )
        
        form_data = {
            'title': 'Important Document',
            'description': 'This is an important document.',
            'alt_text': 'Document icon',
            'tags': 'document, important, pdf',
            'is_public': True
        }
        
        form = MediaFileForm(data=form_data, files={'file': test_file})
        self.assertTrue(form.is_valid())

    def test_media_file_form_required_fields(self):
        """Test that required fields are validated."""
        form = MediaFileForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)
        self.assertIn('file', form.errors)

    def test_media_file_form_file_size_validation(self):
        """Test file size validation."""
        # Create a mock large file (11MB)
        large_content = b"x" * (11 * 1024 * 1024)
        test_file = SimpleUploadedFile(
            "large_file.pdf",
            large_content,
            content_type="application/pdf"
        )
        
        form_data = {
            'title': 'Large File'
        }
        
        form = MediaFileForm(data=form_data, files={'file': test_file})
        self.assertFalse(form.is_valid())
        self.assertIn('file', form.errors)


class SiteConfigurationFormTest(TestCase):
    """Test the SiteConfigurationForm functionality."""

    def test_valid_site_configuration_form(self):
        """Test valid site configuration form submission."""
        form_data = {
            'site_name': 'CozyWish',
            'site_tagline': 'Your wellness destination',
            'site_description': 'Find the best spa and wellness deals',
            'contact_email': '<EMAIL>',
            'contact_phone': '******-123-4567',
            'contact_address': '123 Wellness St, Spa City, SC 12345',
            'facebook_url': 'https://facebook.com/cozywish',
            'twitter_url': 'https://twitter.com/cozywish',
            'instagram_url': 'https://instagram.com/cozywish',
            'linkedin_url': 'https://linkedin.com/company/cozywish',
            'default_meta_title': 'CozyWish - Spa & Wellness Deals',
            'default_meta_description': 'Discover amazing spa and wellness deals in your area',
            'default_meta_keywords': 'spa, wellness, massage, deals, discounts',
            'google_analytics_id': 'GA-*********-1',
            'facebook_pixel_id': '*********',
            'maintenance_mode': False,
            'maintenance_message': 'Site under maintenance',
            'allow_user_registration': True,
            'require_email_verification': True
        }

        form = SiteConfigurationForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_site_configuration_form_required_fields(self):
        """Test that required fields are validated."""
        form = SiteConfigurationForm(data={})
        self.assertFalse(form.is_valid())
        # Most fields are optional, but let's check the form structure
        self.assertIn('site_name', form.fields)
        self.assertIn('contact_email', form.fields)

    def test_site_configuration_form_email_validation(self):
        """Test email field validation."""
        form_data = {
            'site_name': 'Test Site',
            'contact_email': 'invalid-email'
        }

        form = SiteConfigurationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('contact_email', form.errors)

    def test_site_configuration_form_url_validation(self):
        """Test URL field validation."""
        form_data = {
            'site_name': 'Test Site',
            'facebook_url': 'not-a-valid-url'
        }

        form = SiteConfigurationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('facebook_url', form.errors)


class AnnouncementFormTest(TestCase):
    """Test the AnnouncementForm functionality."""

    def test_valid_announcement_form(self):
        """Test valid announcement form submission."""
        start_date = timezone.now()
        end_date = start_date + timedelta(days=7)

        form_data = {
            'title': 'Welcome to CozyWish!',
            'content': 'We are excited to launch our new spa booking platform.',
            'announcement_type': 'info',
            'display_location': 'homepage',
            'is_active': True,
            'is_dismissible': True,
            'start_date': start_date.strftime('%Y-%m-%dT%H:%M'),
            'end_date': end_date.strftime('%Y-%m-%dT%H:%M'),
            'target_user_roles': 'customer,provider',
            'priority': 5
        }

        form = AnnouncementForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_announcement_form_required_fields(self):
        """Test that required fields are validated."""
        form = AnnouncementForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('title', form.errors)
        self.assertIn('content', form.errors)

    def test_announcement_form_date_validation_invalid_range(self):
        """Test date validation when end date is before start date."""
        start_date = timezone.now()
        end_date = start_date - timedelta(days=1)  # End before start

        form_data = {
            'title': 'Test Announcement',
            'content': 'Test content.',
            'start_date': start_date.strftime('%Y-%m-%dT%H:%M'),
            'end_date': end_date.strftime('%Y-%m-%dT%H:%M')
        }

        form = AnnouncementForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)

    def test_announcement_form_date_validation_valid_range(self):
        """Test date validation when end date is after start date."""
        start_date = timezone.now()
        end_date = start_date + timedelta(days=7)

        form_data = {
            'title': 'Test Announcement',
            'content': 'Test content.',
            'announcement_type': 'info',
            'display_location': 'homepage',
            'is_active': True,
            'is_dismissible': True,
            'priority': 0,
            'start_date': start_date.strftime('%Y-%m-%dT%H:%M'),
            'end_date': end_date.strftime('%Y-%m-%dT%H:%M')
        }

        form = AnnouncementForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_announcement_form_optional_end_date(self):
        """Test form with no end date."""
        start_date = timezone.now()

        form_data = {
            'title': 'Test Announcement',
            'content': 'Test content.',
            'announcement_type': 'info',
            'display_location': 'homepage',
            'is_active': True,
            'is_dismissible': True,
            'priority': 0,
            'start_date': start_date.strftime('%Y-%m-%dT%H:%M'),
            'end_date': ''  # No end date
        }

        form = AnnouncementForm(data=form_data)
        self.assertTrue(form.is_valid())


class SystemHealthLogFormTest(TestCase):
    """Test the SystemHealthLogForm functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_valid_system_health_log_form(self):
        """Test valid system health log form submission."""
        form_data = {
            'event_type': 'error',
            'severity': 'high',
            'title': 'Database Connection Error',
            'description': 'Failed to connect to the database server',
            'affected_user': self.user.id,
            'ip_address': '*************',
            'user_agent': 'Mozilla/5.0 Test Browser'
        }

        form = SystemHealthLogForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_system_health_log_form_required_fields(self):
        """Test that required fields are validated."""
        form = SystemHealthLogForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('event_type', form.errors)
        self.assertIn('severity', form.errors)
        self.assertIn('title', form.errors)
        self.assertIn('description', form.errors)

    def test_system_health_log_form_user_queryset_active_only(self):
        """Test that only active users are available in form."""
        inactive_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_active=False
        )

        form = SystemHealthLogForm()
        user_choices = form.fields['affected_user'].queryset

        self.assertIn(self.user, user_choices)
        self.assertNotIn(inactive_user, user_choices)

    def test_system_health_log_form_optional_user_field(self):
        """Test that affected_user field is optional."""
        form_data = {
            'event_type': 'info',
            'severity': 'low',
            'title': 'System Info',
            'description': 'System information event'
        }

        form = SystemHealthLogForm(data=form_data)
        self.assertTrue(form.is_valid())


class BulkUserActionFormTest(TestCase):
    """Test the BulkUserActionForm functionality."""

    def setUp(self):
        """Create users used for testing."""
        self.users = [
            User.objects.create_user(email=f'user{i}@example.com', password='testpass123')
            for i in range(3)
        ]

    def test_valid_bulk_user_action_form(self):
        """Test valid bulk user action form submission."""
        ids = ','.join(str(u.id) for u in self.users)
        form_data = {
            'action': 'activate',
            'user_ids': ids,
            'reason': 'Activating selected user accounts'
        }

        form = BulkUserActionForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_bulk_user_action_form_required_fields(self):
        """Test that required fields are validated."""
        form = BulkUserActionForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('action', form.errors)
        self.assertIn('user_ids', form.errors)

    def test_bulk_user_action_form_optional_reason(self):
        """Test that reason field is optional."""
        ids = ','.join(str(u.id) for u in self.users)
        form_data = {
            'action': 'deactivate',
            'user_ids': ids
        }

        form = BulkUserActionForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_bulk_user_action_form_action_choices(self):
        """Test that action choices are properly defined."""
        expected_actions = ['activate', 'deactivate', 'approve_providers', 'reject_providers']

        form = BulkUserActionForm()
        action_choices = [choice[0] for choice in form.fields['action'].choices]

        for action in expected_actions:
            self.assertIn(action, action_choices)
