"""
Simple integration test to verify the notifications_app integration tests work.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from notifications_app.models import Notification
from notifications_app.utils import create_notification
from accounts_app.models import CustomerProfile

User = get_user_model()


class SimpleIntegrationTest(TestCase):
    """Simple integration test to verify basic functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='CustomerPass123!',
            role='customer'
        )
        CustomerProfile.objects.create(user=self.customer)
    
    def test_notification_creation_and_viewing(self):
        """Test basic notification creation and viewing."""
        # Create a notification
        notification = create_notification(
            user=self.customer,
            notification_type=Notification.SYSTEM,
            title='Test Notification',
            message='This is a test notification.'
        )
        
        self.assertIsNotNone(notification)
        self.assertEqual(notification.user, self.customer)
        self.assertEqual(notification.read_status, Notification.UNREAD)
        
        # Login and view notifications
        self.client.login(email='<EMAIL>', password='CustomerPass123!')
        
        response = self.client.get(reverse('notifications_app:notification_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Notification')
        
        print("✅ Simple integration test passed!")
    
    def test_ajax_unread_notifications(self):
        """Test AJAX endpoint for unread notifications."""
        # Create notifications
        for i in range(3):
            create_notification(
                user=self.customer,
                notification_type=Notification.SYSTEM,
                title=f'Test Notification {i+1}',
                message=f'This is test notification {i+1}.'
            )
        
        # Login and test AJAX endpoint
        self.client.login(email='<EMAIL>', password='CustomerPass123!')
        
        response = self.client.get(
            reverse('notifications_app:get_unread_notifications'),
            HTTP_X_REQUESTED_WITH='XMLHttpRequest'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['unread_count'], 3)
        
        print("✅ AJAX integration test passed!")
