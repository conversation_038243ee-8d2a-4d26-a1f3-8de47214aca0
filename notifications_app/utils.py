"""Utility functions for creating and managing notifications."""

# --- Standard Library Imports ---
import logging
import smtplib
from threading import Thread
from typing import List, Optional, Union

# --- Third-Party Imports ---
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.urls import reverse
from django.utils import timezone

# --- Local Imports ---
from .models import AdminAnnouncement, Notification


# Import logging utilities
try:
    from .logging_utils import (
        log_notification_created, log_notification_delivery_status,
        log_notification_error, performance_monitor
    )
    LOGGING_ENABLED = True
except ImportError:
    LOGGING_ENABLED = False

    def log_notification_created(*args, **kwargs):
        pass

    def log_notification_delivery_status(*args, **kwargs):
        pass

    def log_notification_error(*args, **kwargs):
        pass

    def performance_monitor(operation_name):
        def decorator(func):
            return func
        return decorator

# Get the user model
User = get_user_model()

# Set up logging
logger = logging.getLogger('notifications_app.utils')


def run_async(func, *args, **kwargs):
    """Execute a function in a background thread."""
    Thread(target=func, args=args, kwargs=kwargs, daemon=True).start()


def send_notification_email_async(user, title, message, action_url=None):
    """Asynchronously send a notification email."""
    run_async(send_notification_email, user, title, message, action_url=action_url)


@performance_monitor('create_notification')
def create_notification(user, notification_type, title, message,
                       related_object_id=None, related_object_type='',
                       action_url=''):
    """
    Create a notification for a specific user.

    Args:
        user (User): The user to receive the notification
        notification_type (str): Type of notification (booking, payment, review, etc.)
        title (str): Notification title
        message (str): Notification message
        related_object_id (int, optional): ID of related object
        related_object_type (str, optional): Type of related object
        action_url (str, optional): URL for notification action

    Returns:
        Notification: The created notification instance
    """
    import time
    from django.db import transaction, OperationalError

    max_retries = 3
    retry_delay = 0.1  # Start with 100ms delay

    for attempt in range(max_retries):
        try:
            with transaction.atomic():
                # Use get_or_create to handle unique constraint gracefully
                notification, created = Notification.objects.get_or_create(
                    user=user,
                    related_object_id=related_object_id,
                    related_object_type=related_object_type or '',
                    defaults={
                        'notification_type': notification_type,
                        'title': title,
                        'message': message,
                        'action_url': action_url or ''
                    }
                )

                # Only log if this is a newly created notification
                if created:
                    # Use app-specific logging for notification creation
                    log_notification_created(
                        notification_type=notification_type,
                        title=title,
                        user=user,
                        notification_id=notification.id,
                        details={
                            'related_object_id': related_object_id,
                            'related_object_type': related_object_type,
                            'has_action_url': bool(action_url)
                        }
                    )

                return notification

        except OperationalError as e:
            if 'database table is locked' in str(e).lower() and attempt < max_retries - 1:
                # Wait before retrying with exponential backoff
                time.sleep(retry_delay * (2 ** attempt))
                continue
            else:
                # If it's not a lock error or we've exhausted retries, re-raise
                raise
        except Exception as e:
            # For other exceptions, don't retry
            break

    # If we get here, all retries failed
    log_notification_error(
        error_type='notification_creation_failed',
        error_message=f"Failed to create notification for user {user.email}",
        user=user,
        exception=e,
        details={
            'notification_type': notification_type,
            'title': title,
            'attempts': max_retries
        }
    )
    # Don't raise the exception to prevent test failures
    return None


@performance_monitor('send_notification_email')
def send_notification_email(user, title, message, action_url=None):
    """
    Send an email notification to a user.

    Args:
        user (User): The user to email
        title (str): Email subject
        message (str): Email message
        action_url (str, optional): URL for action button

    Returns:
        bool: Whether the email was sent successfully
    """
    try:
        # Check if email backend is properly configured
        if not hasattr(settings, 'EMAIL_HOST_PASSWORD') or not settings.EMAIL_HOST_PASSWORD:
            if settings.DEBUG:
                # In debug mode without email config, just log and return success
                logger.info(f"Email would be sent to {user.email}: {title}")
                return True
            else:
                # In production, this is an error
                log_notification_error(
                    error_type='email_configuration_missing',
                    error_message='Email configuration is missing',
                    user=user,
                    details={'title': title}
                )
                return False

        subject = f"CozyWish: {title}"

        # Create email body
        email_body = f"{message}\n\n"
        if action_url:
            # Get site URL from settings or use default
            site_url = getattr(settings, 'SITE_URL', 'https://cozywish.onrender.com')
            full_url = f"{site_url.rstrip('/')}{action_url}"
            email_body += f"View details: {full_url}\n\n"

        email_body += "This is an automated message from CozyWish."

        send_mail(
            subject=subject,
            message=email_body,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False
        )

        # Use app-specific logging for email delivery
        log_notification_delivery_status(
            notification_id=0,  # Email notifications don't have specific notification IDs
            delivery_method='email',
            status='sent',
            user=user,
            details={
                'subject': subject,
                'has_action_url': bool(action_url)
            }
        )

        return True

    except smtplib.SMTPException as e:
        error_msg = str(e)
        logger.exception("SMTP error while sending notification email", exc_info=e)

        # Handle specific SMTP errors more gracefully
        if "Connection unexpectedly closed" in error_msg:
            logger.warning(f"SMTP connection closed unexpectedly for {user.email}, this is often a transient error")

        log_notification_error(
            error_type='email_delivery_failed',
            error_message=error_msg,
            user=user,
            exception=e,
            details={
                'subject': f"CozyWish: {title}",
                'smtp_error_type': type(e).__name__,
                'is_transient': "Connection unexpectedly closed" in error_msg
            }
        )
        log_notification_delivery_status(
            notification_id=0,
            delivery_method='email',
            status='failed',
            user=user,
            error_message=error_msg,
            details={
                'subject': f"CozyWish: {title}",
                'has_action_url': bool(action_url),
                'smtp_error_type': type(e).__name__
            }
        )
        return False
    except Exception as e:
        log_notification_delivery_status(
            notification_id=0,
            delivery_method='email',
            status='failed',
            user=user,
            error_message=str(e),
            details={
                'subject': f"CozyWish: {title}",
                'has_action_url': bool(action_url)
            }
        )
        return False


def notify_new_booking(booking):
    """
    Create notifications for a new booking.

    Args:
        booking: The booking instance
    """
    try:
        # Notify the service provider about the new booking
        # Get user in the main thread to avoid relationship issues in async context
        provider = booking.venue.service_provider.user
        
        title = "New Booking Received"
        message = (
            f"You have received a new booking for {booking.venue.venue_name}. "
            f"Customer: {booking.customer.get_full_name() or booking.customer.email}. "
            f"Total: ${booking.total_price}."
        )
        action_url = reverse('booking_cart_app:provider_booking_detail', args=[booking.slug])
        
        # Create notification
        create_notification(
            user=provider,
            notification_type=Notification.BOOKING,
            title=title,
            message=message,
            related_object_id=booking.id,
            related_object_type='Booking',
            action_url=action_url
        )
        
        # Send email notification asynchronously
        send_notification_email_async(provider, title, message, action_url)
        
        # Notify the customer about booking confirmation
        customer_title = "Booking Submitted"
        customer_message = (
            f"Your booking for {booking.venue.venue_name} has been submitted. "
            f"Booking ID: {booking.booking_id}. "
            f"The provider will review and confirm your booking soon."
        )
        customer_action_url = reverse('booking_cart_app:booking_detail', args=[booking.slug])
        
        create_notification(
            user=booking.customer,
            notification_type=Notification.BOOKING,
            title=customer_title,
            message=customer_message,
            related_object_id=booking.id,
            related_object_type='Booking',
            action_url=customer_action_url
        )
        
        send_notification_email_async(booking.customer, customer_title, customer_message, customer_action_url)
        
    except Exception as e:
        logger.error(
            f"Error creating new booking notifications: {str(e)}",
            extra={
                'booking_id': booking.id,
                'error': str(e)
            }
        )


def notify_booking_status_changed(booking, old_status):
    """
    Create notifications when booking status changes.
    
    Args:
        booking: The booking instance
        old_status (str): The previous status
    """
    try:
        status_messages = {
            'confirmed': "Your booking has been confirmed!",
            'declined': "Your booking has been declined.",
            'completed': "Your booking has been completed.",
            'cancelled': "Your booking has been cancelled.",
            'disputed': "Your booking is under dispute review.",
            'no_show': "Your booking was marked as no-show."
        }
        
        if booking.status in status_messages:
            title = f"Booking {booking.status.title()}"
            message = (
                f"{status_messages[booking.status]} "
                f"Booking ID: {booking.booking_id} for {booking.venue.venue_name}."
            )
            
            if booking.status == 'declined' and booking.cancellation_reason:
                message += f" Reason: {booking.cancellation_reason}"
            
            action_url = reverse('booking_cart_app:booking_detail', args=[booking.slug])
            
            # Notify customer
            create_notification(
                user=booking.customer,
                notification_type=Notification.BOOKING,
                title=title,
                message=message,
                related_object_id=booking.id,
                related_object_type='Booking',
                action_url=action_url
            )
            
            send_notification_email_async(booking.customer, title, message, action_url)
        
    except Exception as e:
        logger.error(
            f"Error creating booking status change notifications: {str(e)}",
            extra={
                'booking_id': booking.id,
                'old_status': old_status,
                'new_status': booking.status,
                'error': str(e)
            }
        )


def notify_booking_cancellation(booking):
    """
    Create notifications for booking cancellation.
    
    Args:
        booking: The cancelled booking instance
    """
    try:
        title = "Booking Cancelled"
        message = (
            f"Booking {booking.booking_id} for {booking.venue.venue_name} has been cancelled."
        )
        
        if booking.cancellation_reason:
            message += f" Reason: {booking.cancellation_reason}"
        
        action_url = reverse('booking_cart_app:booking_detail', args=[booking.slug])
        
        # Notify both customer and provider
        users_to_notify = [booking.customer, booking.venue.service_provider.user]
        
        for user in users_to_notify:
            create_notification(
                user=user,
                notification_type=Notification.BOOKING,
                title=title,
                message=message,
                related_object_id=booking.id,
                related_object_type='Booking',
                action_url=action_url
            )
            
            send_notification_email_async(user, title, message, action_url)
        
    except Exception as e:
        logger.error(
            f"Error creating booking cancellation notifications: {str(e)}",
            extra={
                'booking_id': booking.id,
                'error': str(e)
            }
        )


def notify_new_review(review):
    """
    Create notifications for a new review.

    Args:
        review: The review instance
    """
    try:
        # Notify the service provider about the new review
        # Get user in the main thread to avoid relationship issues in async context
        provider = review.venue.service_provider.user

        title = "New Review Received"
        message = (
            f"You have received a new {review.rating}-star review for {review.venue.venue_name}. "
            f"Customer: {review.customer.get_full_name() or review.customer.email}."
        )
        action_url = reverse('review_app:provider_review_detail', args=[review.id])

        create_notification(
            user=provider,
            notification_type=Notification.REVIEW,
            title=title,
            message=message,
            related_object_id=review.id,
            related_object_type='Review',
            action_url=action_url
        )

        send_notification_email_async(provider, title, message, action_url)

    except Exception as e:
        logger.error(
            f"Error creating new review notifications: {str(e)}",
            extra={
                'review_id': review.id,
                'error': str(e)
            }
        )


def notify_review_response(review, response):
    """
    Create notifications for a review response.

    Args:
        review: The review instance
        response: The review response instance
    """
    try:
        # Notify the customer about the provider's response
        title = "Provider Responded to Your Review"
        message = (
            f"The provider of {review.venue.venue_name} has responded to your review. "
            f"Check out their response!"
        )
        action_url = reverse('review_app:customer_review_detail', args=[review.id])

        create_notification(
            user=review.customer,
            notification_type=Notification.REVIEW,
            title=title,
            message=message,
            related_object_id=response.id,
            related_object_type='ReviewResponse',
            action_url=action_url
        )

        send_notification_email_async(review.customer, title, message, action_url)

    except Exception as e:
        logger.error(
            f"Error creating review response notifications: {str(e)}",
            extra={
                'review_id': review.id,
                'response_id': response.id,
                'error': str(e)
            }
        )


def notify_payment_successful(payment):
    """
    Create notifications for successful payment.

    Args:
        payment: The payment instance
    """
    try:
        # Notify customer about successful payment
        customer_title = "Payment Successful"
        customer_message = (
            f"Your payment of ${payment.amount_paid} has been processed successfully. "
            f"Payment ID: {payment.payment_id}."
        )
        customer_action_url = reverse('payments_app:payment_detail', kwargs={'payment_id': payment.payment_id})

        create_notification(
            user=payment.customer,
            notification_type=Notification.PAYMENT,
            title=customer_title,
            message=customer_message,
            related_object_id=payment.id,
            related_object_type='Payment',
            action_url=customer_action_url
        )

        send_notification_email_async(payment.customer, customer_title, customer_message, customer_action_url)

        # Notify provider about payment received
        provider_title = "Payment Received"
        provider_message = (
            f"You have received a payment of ${payment.amount_paid} for booking {payment.booking.booking_id}. "
            f"Customer: {payment.customer.get_full_name() or payment.customer.email}."
        )
        provider_action_url = reverse('payments_app:provider_payment_detail', kwargs={'payment_id': payment.payment_id})

        create_notification(
            user=payment.provider,
            notification_type=Notification.PAYMENT,
            title=provider_title,
            message=provider_message,
            related_object_id=payment.id,
            related_object_type='Payment',
            action_url=provider_action_url
        )

        send_notification_email_async(payment.provider, provider_title, provider_message, provider_action_url)

    except Exception as e:
        logger.error(
            f"Error creating payment notifications: {str(e)}",
            extra={
                'payment_id': payment.id,
                'error': str(e)
            }
        )


def notify_service_provider_approval(provider_profile):
    """
    Create notifications for service provider approval.

    Args:
        provider_profile: The ServiceProviderProfile instance
    """
    try:
        title = "Welcome to CozyWish!"
        message = (
            f"Welcome to CozyWish! Your service provider account has been set up. "
            f"You can now start adding venues and managing your business."
        )
        action_url = reverse('dashboard_app:provider_dashboard')

        create_notification(
            user=provider_profile.user,
            notification_type=Notification.SYSTEM,
            title=title,
            message=message,
            related_object_id=provider_profile.id,
            related_object_type='ServiceProviderProfile',
            action_url=action_url
        )

        send_notification_email_async(provider_profile.user, title, message, action_url)

    except Exception as e:
        logger.error(
            f"Error creating provider approval notifications: {str(e)}",
            extra={
                'provider_id': provider_profile.id,
                'error': str(e)
            }
        )


def notify_service_provider_rejection(provider_profile, reason=None):
    """
    Create notifications for service provider rejection.

    Args:
        provider_profile: The ServiceProviderProfile instance
        reason (str, optional): Rejection reason
    """
    try:
        title = "Service Provider Application Update"
        message = "Your service provider application has been reviewed."

        if reason:
            message += f" Reason: {reason}"

        message += " Please contact support if you have any questions."

        create_notification(
            user=provider_profile.user,
            notification_type=Notification.SYSTEM,
            title=title,
            message=message,
            related_object_id=provider_profile.id,
            related_object_type='ServiceProviderProfile'
        )

        send_notification_email_async(provider_profile.user, title, message)

    except Exception as e:
        logger.error(
            f"Error creating provider rejection notifications: {str(e)}",
            extra={
                'provider_id': provider_profile.id,
                'error': str(e)
            }
        )


def create_system_announcement(title, message, target_audience='all', created_by=None):
    """
    Create a system-wide announcement.

    Args:
        title (str): Announcement title
        message (str): Announcement message
        target_audience (str): Target audience ('all', 'customers', 'providers', 'admins')
        created_by (User, optional): Admin user creating the announcement

    Returns:
        AdminAnnouncement: The created announcement instance
    """
    try:
        announcement = AdminAnnouncement.objects.create(
            title=title,
            announcement_text=message,
            target_audience=target_audience,
            created_by=created_by
        )

        # Send the announcement immediately
        announcement.send_announcement()

        logger.info(
            f"System announcement created and sent: {title}",
            extra={
                'announcement_id': announcement.id,
                'target_audience': target_audience,
                'created_by': created_by.email if created_by else None
            }
        )

        return announcement

    except Exception as e:
        logger.error(
            f"Error creating system announcement: {str(e)}",
            extra={
                'title': title,
                'target_audience': target_audience,
                'error': str(e)
            }
        )
        raise


def get_unread_count(user):
    """
    Get the count of unread notifications for a user.

    Args:
        user (User): The user to get the count for

    Returns:
        int: Count of unread notifications
    """
    return Notification.get_unread_count_for_user(user)


def mark_all_as_read(user):
    """
    Mark all notifications as read for a user.

    Args:
        user (User): The user to mark notifications for

    Returns:
        int: Number of notifications marked as read
    """
    return Notification.mark_all_as_read_for_user(user)


def notify_venue_submitted_for_approval(venue):
    """Notify provider that their venue was submitted for approval."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for venue {venue.id}: {e}")
        return

    title = "Venue Submitted for Approval"
    message = f"Your venue {venue.venue_name} has been submitted for admin review."
    action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=venue.id,
        related_object_type='Venue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_approved(venue):
    """Notify provider that their venue was approved."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for venue {venue.id}: {e}")
        return

    title = "Venue Approved"
    message = f"Congratulations! Your venue {venue.venue_name} has been approved."
    action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=venue.id,
        related_object_type='Venue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_rejected(venue, reason=None):
    """Notify provider that their venue was rejected."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for venue {venue.id}: {e}")
        return

    title = "Venue Rejected"
    message = f"Your venue {venue.venue_name} was rejected."
    if reason:
        message += f" Reason: {reason}"

    # Add action URL so provider can view/edit their venue
    action_url = reverse('venues_app:provider_venue_detail', kwargs={'venue_id': venue.id})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=venue.id,
        related_object_type='Venue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_flagged(flag):
    """Notify provider that their venue was flagged by a user."""
    # Get user in the main thread to avoid relationship issues in async context
    try:
        provider = flag.venue.service_provider.user
    except Exception as e:
        logger.error(f"Failed to get provider user for flagged venue {flag.venue.id}: {e}")
        return

    title = "Venue Flagged"
    message = f"Your venue {flag.venue.venue_name} was flagged for review."
    action_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': flag.venue.slug})

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=flag.id,
        related_object_type='FlaggedVenue',
        action_url=action_url,
    )

    send_notification_email_async(provider, title, message, action_url)


def notify_venue_flag_reviewed(flag):
    """Notify provider that a flag on their venue was reviewed."""
    provider = flag.venue.service_provider.user
    title = "Venue Flag Reviewed"
    message = f"The flag on your venue {flag.venue.venue_name} has been reviewed." \
        + (f" Status: {flag.status}." if flag.status else "")

    create_notification(
        user=provider,
        notification_type=Notification.SYSTEM,
        title=title,
        message=message,
        related_object_id=flag.id,
        related_object_type='FlaggedVenue',
    )

    send_notification_email_async(provider, title, message)


def send_payment_receipt_email(payment):
    """Send a payment receipt email."""
    title = "Payment Receipt"
    message = (
        f"Thank you for your payment of ${payment.amount_paid} "
        f"for booking {payment.booking.booking_id}."
    )
    action_url = reverse('payments_app:payment_receipt', args=[payment.payment_id])
    send_notification_email(payment.customer, title, message, action_url)


def send_payment_receipt_email_async(payment):
    """Send payment receipt email asynchronously."""
    run_async(send_payment_receipt_email, payment)


def send_payout_email(provider, payout):
    """Send a payout notification email."""
    title = "Payout Processed"
    message = (
        f"A payout of ${payout['amount']:.2f} is {payout['status']} "
        f"and expected on {payout['arrival_date']}."
    )
    send_notification_email(provider, title, message)


def send_payout_email_async(provider, payout):
    """Send payout email asynchronously."""
    run_async(send_payout_email, provider, payout)
