"""
Unit tests for booking_cart_app forms.

This module contains comprehensive unit tests for all form classes in the booking_cart_app,
including AddToCartForm, BookingForm, ServiceAvailabilityForm, and other forms.
"""

# Standard library imports
from datetime import timedelta
from decimal import Decimal

# Django imports
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError

# Local imports
from accounts_app.models import ServiceProviderProfile
from venues_app.models import Category, Venue, Service, VenueCategory
from booking_cart_app.models import Cart, CartItem, Booking, ServiceAvailability
from booking_cart_app.forms import (
    AddToCartForm, CheckoutForm, ServiceAvailabilityForm,
    UpdateCartItemForm, BookingCancellationForm
)

User = get_user_model()


class AddToCartFormTest(TestCase):
    """Test the AddToCartForm functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_customer=True
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_service_provider=True
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Spa Business',
            business_phone_number='+**********',
            contact_person_name='Test Provider',
            business_address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Spa",
            description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            service_description="A relaxing full-body massage.",
            service_price=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

        # Create cart
        self.cart = Cart.objects.create(customer=self.customer)

    def test_add_to_cart_form_valid_data(self):
        """Test AddToCartForm with valid data."""
        form_data = {
            'selected_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'selected_time_slot': '10:00',
            'quantity': 1
        }
        
        form = AddToCartForm(data=form_data, service=self.service)
        self.assertTrue(form.is_valid())

    def test_add_to_cart_form_past_date(self):
        """Test AddToCartForm with past date."""
        form_data = {
            'selected_date': (timezone.now().date() - timedelta(days=1)).strftime('%Y-%m-%d'),
            'selected_time_slot': '10:00',
            'quantity': 1
        }
        
        form = AddToCartForm(data=form_data, service=self.service)
        self.assertFalse(form.is_valid())
        self.assertIn('selected_date', form.errors)

    def test_add_to_cart_form_invalid_quantity(self):
        """Test AddToCartForm with invalid quantity."""
        form_data = {
            'selected_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'selected_time_slot': '10:00',
            'quantity': 0  # Invalid quantity
        }
        
        form = AddToCartForm(data=form_data, service=self.service)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)

    def test_add_to_cart_form_missing_required_fields(self):
        """Test AddToCartForm with missing required fields."""
        form_data = {
            'selected_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            # Missing selected_time_slot and quantity
        }
        
        form = AddToCartForm(data=form_data, service=self.service)
        self.assertFalse(form.is_valid())
        self.assertIn('selected_time_slot', form.errors)
        self.assertIn('quantity', form.errors)

    def test_add_to_cart_form_save_method(self):
        """Test AddToCartForm save method."""
        form_data = {
            'selected_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'selected_time_slot': '10:00',
            'quantity': 2
        }
        
        form = AddToCartForm(data=form_data, service=self.service)
        self.assertTrue(form.is_valid())
        
        cart_item = form.save(cart=self.cart)
        
        self.assertEqual(cart_item.cart, self.cart)
        self.assertEqual(cart_item.service, self.service)
        self.assertEqual(cart_item.quantity, 2)
        self.assertEqual(cart_item.price_per_item, self.service.price)


class CheckoutFormTest(TestCase):
    """Test the CheckoutForm functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_customer=True
        )

        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_service_provider=True
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Spa Business',
            business_phone_number='+**********',
            contact_person_name='Test Provider',
            business_address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Spa",
            description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

    def test_checkout_form_valid_data(self):
        """Test CheckoutForm with valid data."""
        form_data = {
            'notes': 'Please use lavender oil'
        }

        form = CheckoutForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_checkout_form_empty_data(self):
        """Test CheckoutForm with empty data (should be valid as fields are optional)."""
        form_data = {}

        form = CheckoutForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_checkout_form_notes_max_length(self):
        """Test CheckoutForm with notes exceeding max length."""
        form_data = {
            'notes': 'x' * 501,  # Exceeds 500 character limit
        }

        form = CheckoutForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('notes', form.errors)

    def test_checkout_form_cleaned_data(self):
        """Test CheckoutForm cleaned data."""
        form_data = {
            'notes': 'Please use lavender oil'
        }

        form = CheckoutForm(data=form_data)
        self.assertTrue(form.is_valid())

        cleaned_data = form.cleaned_data
        self.assertEqual(cleaned_data['notes'], 'Please use lavender oil')


class ServiceAvailabilityFormTest(TestCase):
    """Test the ServiceAvailabilityForm functionality."""

    def setUp(self):
        """Set up test data."""
        # Create users
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_service_provider=True
        )
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            business_name='Test Spa Business',
            business_phone_number='+**********',
            contact_person_name='Test Provider',
            business_address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create category and venue
        self.category = Category.objects.create(
            name="Spa",
            description="Spa services",
            is_active=True
        )
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name="Test Spa",
            short_description="A luxury spa in the heart of the city.",
            state="NY",
            county="New York County",
            city="New York",
            street_number="123",
            street_name="Main St",
            operating_hours="9AM-6PM",
            tags="spa, massage, wellness",
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE
        )
        
        # Add category to venue
        VenueCategory.objects.create(venue=self.venue, category=self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title="Swedish Massage",
            service_description="A relaxing full-body massage.",
            service_price=Decimal("100.00"),
            duration_minutes=60,
            is_active=True
        )

    def test_service_availability_form_valid_data(self):
        """Test ServiceAvailabilityForm with valid data."""
        form_data = {
            'available_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'start_time': '09:00',
            'end_time': '10:00',
            'max_bookings': 5
        }
        
        form = ServiceAvailabilityForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_service_availability_form_past_date(self):
        """Test ServiceAvailabilityForm with past date."""
        form_data = {
            'available_date': (timezone.now().date() - timedelta(days=1)).strftime('%Y-%m-%d'),
            'start_time': '09:00',
            'end_time': '10:00',
            'max_bookings': 5
        }
        
        form = ServiceAvailabilityForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('available_date', form.errors)

    def test_service_availability_form_end_before_start(self):
        """Test ServiceAvailabilityForm with end time before start time."""
        form_data = {
            'available_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'start_time': '10:00',
            'end_time': '09:00',  # End before start
            'max_bookings': 5
        }
        
        form = ServiceAvailabilityForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('end_time', form.errors)

    def test_service_availability_form_save_method(self):
        """Test ServiceAvailabilityForm save method."""
        form_data = {
            'available_date': (timezone.now().date() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'start_time': '09:00',
            'end_time': '10:00',
            'max_bookings': 5
        }
        
        form = ServiceAvailabilityForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        availability = form.save(service=self.service)
        
        self.assertEqual(availability.service, self.service)
        self.assertEqual(availability.max_bookings, 5)
        self.assertTrue(availability.is_available)
