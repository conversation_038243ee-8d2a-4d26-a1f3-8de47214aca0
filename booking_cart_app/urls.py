# Django imports
from django.urls import path

# Local imports
from . import views

app_name = 'booking_cart_app'

urlpatterns = [
    # ===== CUSTOMER URLS =====

    # Cart management
    path('cart/add/<int:service_id>/', views.add_to_cart_view, name='add_to_cart'),
    path('cart/', views.cart_view, name='cart_view'),
    path('cart/update/<int:item_id>/', views.update_cart_item_view, name='update_cart_item'),
    path('cart/remove/<int:item_id>/', views.remove_from_cart_view, name='remove_from_cart'),

    # AJAX endpoints
    path('ajax/slots/<int:service_id>/', views.get_available_slots_ajax, name='get_available_slots'),

    # Checkout and booking management
    path('checkout/', views.checkout_view, name='checkout'),
    path('bookings/', views.booking_list_view, name='booking_list'),
    path('bookings/<slug:booking_slug>/', views.booking_detail_view, name='booking_detail'),
    path('bookings/<slug:booking_slug>/cancel/', views.cancel_booking_view, name='cancel_booking'),
    path('bookings/<slug:booking_slug>/confirmation/', views.booking_confirmation_view, name='booking_confirmation'),

    # ===== PROVIDER URLS =====

    # Availability management
    path('provider/availability/', views.provider_availability_list_view, name='provider_availability_list'),
    path('provider/availability/service/<int:service_id>/', views.provider_service_availability_view, name='provider_service_availability'),
    path('provider/availability/service/<int:service_id>/calendar/', views.provider_availability_calendar_view, name='provider_availability_calendar'),
    path('provider/availability/add/<int:service_id>/', views.provider_add_availability_view, name='provider_add_availability'),
    path('provider/availability/bulk/<int:service_id>/', views.provider_bulk_availability_view, name='provider_bulk_availability'),
    path('provider/availability/<int:availability_id>/edit/', views.provider_edit_availability_view, name='provider_edit_availability'),
    path('provider/availability/<int:availability_id>/delete/', views.provider_delete_availability_view, name='provider_delete_availability'),

    # Booking management
    path('provider/bookings/', views.provider_booking_list_view, name='provider_booking_list'),
    path('provider/bookings/<slug:booking_slug>/', views.provider_booking_detail_view, name='provider_booking_detail'),
    path('provider/bookings/<slug:booking_slug>/accept/', views.provider_accept_booking_view, name='provider_accept_booking'),
    path('provider/bookings/<slug:booking_slug>/decline/', views.provider_decline_booking_view, name='provider_decline_booking'),

    # ===== ADMIN URLS =====

    # Admin dashboard and overview
    path('admin/dashboard/', views.admin_booking_dashboard_view, name='admin_booking_dashboard'),
    path('admin/analytics/', views.admin_booking_analytics_view, name='admin_booking_analytics'),

    # Admin booking management
    path('admin/bookings/', views.admin_booking_list_view, name='admin_booking_list'),
    path('admin/bookings/<slug:booking_slug>/', views.admin_booking_detail_view, name='admin_booking_detail'),
    path('admin/bookings/<slug:booking_slug>/status/', views.admin_update_booking_status_view, name='admin_update_booking_status'),

    # Admin dispute resolution
    path('admin/disputes/', views.admin_dispute_list_view, name='admin_dispute_list'),
    path('admin/disputes/<slug:booking_slug>/resolve/', views.admin_resolve_dispute_view, name='admin_resolve_dispute'),
]
