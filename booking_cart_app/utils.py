# Django imports
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.core.cache import cache
from datetime import timedelta, time, datetime

# Local imports
from .models import Cart, CartItem, Booking, BookingItem, ServiceAvailability

# Import logging utilities
try:
    from utils.logging_utils import get_app_logger, log_user_activity, log_error
    LOGGING_ENABLED = True
except ImportError:
    LOGGING_ENABLED = False

    def get_app_logger(app_name, logger_type=''):
        import logging
        return logging.getLogger(app_name)

    def log_user_activity(app_name, activity_type, user=None, request=None, details=None):
        pass

    def log_error(app_name, error_type, error_message, user=None, request=None, exception=None, details=None):
        pass


def get_or_create_cart(user):
    """
    Get or create a cart for the user.
    Returns the user's active cart, creating one if it doesn't exist.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        raise ValidationError("Only customers can have carts")
    
    cart, created = Cart.objects.get_or_create(customer=user)
    
    # If cart has expired, extend it
    if cart.is_expired:
        cart.extend_expiration()
    
    return cart


def clean_expired_cart_items():
    """
    Remove expired cart items and their empty carts.
    Returns the number of items removed.
    """
    try:
        # Find expired carts
        expired_carts = Cart.objects.filter(expires_at__lt=timezone.now())

        # Count items in expired carts
        expired_items_count = CartItem.objects.filter(cart__in=expired_carts).count()

        if expired_items_count > 0 and LOGGING_ENABLED:
            # Log expired cart cleanup
            log_user_activity(
                'booking_cart_app',
                'expired_carts_cleaned',
                user=None,
                request=None,
                details={
                    'expired_carts_count': expired_carts.count(),
                    'expired_items_count': expired_items_count,
                    'cleanup_timestamp': str(timezone.now())
                }
            )

        # Delete expired carts (this will cascade delete cart items)
        expired_carts.delete()

        return expired_items_count

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'cart_cleanup_error',
                f"Error cleaning expired cart items: {str(e)}",
                user=None,
                request=None,
                exception=e
            )
        return 0


def get_cart_total(user):
    """
    Calculate the total price of items in a user's cart.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        return 0
    
    try:
        cart = Cart.objects.get(customer=user)
        if not cart.is_expired:
            return cart.total_price
    except Cart.DoesNotExist:
        pass
    
    return 0


def check_service_availability(service, date, time_slot, quantity=1):
    """
    Check if a service is available for booking on a specific date and time.
    Returns (is_available, message) tuple.
    """
    try:
        availability = ServiceAvailability.objects.get(
            service=service,
            available_date=date,
            start_time=time_slot
        )
        
        if not availability.is_available:
            return False, "This time slot is not available for booking."
        
        if availability.is_fully_booked:
            return False, "This time slot is fully booked."
        
        if availability.current_bookings + quantity > availability.max_bookings:
            available_spots = availability.available_spots
            return False, f"Only {available_spots} spot(s) available for this time slot."
        
        return True, "Available"
        
    except ServiceAvailability.DoesNotExist:
        # If no availability record exists, assume it's available
        # This will be created when the booking is made
        return True, "Available"


def get_available_time_slots(service, date):
    """
    Get available time slots for a service on a specific date.
    Returns a list of (time, available_spots) tuples.
    """
    cache_key = f"avail-slots:{service.id}:{date.isoformat()}"
    cached = cache.get(cache_key)
    if cached is not None:
        return cached

    # Get all availability slots for the service on the given date
    availability_slots = ServiceAvailability.objects.filter(
        service=service,
        available_date=date,
        is_available=True
    ).order_by('start_time')
    
    available_slots = []
    for slot in availability_slots:
        if not slot.is_fully_booked:
            available_slots.append((slot.start_time, slot.available_spots))
    
    # If no availability slots exist, generate default time slots
    if not available_slots:
        # Generate default time slots from 9 AM to 5 PM with 1-hour intervals
        start_time = time(9, 0)  # 9:00 AM
        end_time = time(17, 0)   # 5:00 PM
        current_time = datetime.combine(date, start_time)
        end_datetime = datetime.combine(date, end_time)
        
        while current_time < end_datetime:
            available_slots.append((current_time.time(), 1))  # Default 1 spot available
            current_time += timedelta(hours=1)
    
    cache.set(cache_key, available_slots, 300)
    return available_slots


def create_booking_from_cart(cart, notes=''):
    """
    Create bookings from cart items.
    Groups items by venue and creates separate bookings for each venue.
    Returns a list of created booking objects.
    """
    if cart.is_expired:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_creation_expired_cart',
                "Attempted to create booking from expired cart",
                user=cart.customer,
                request=None,
                details={'cart_id': cart.id, 'expires_at': str(cart.expires_at)}
            )
        raise ValidationError("Cart has expired")

    if not cart.items.exists():
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_creation_empty_cart',
                "Attempted to create booking from empty cart",
                user=cart.customer,
                request=None,
                details={'cart_id': cart.id}
            )
        raise ValidationError("Cart is empty")

    try:
        bookings_created = []

        # Group cart items by venue
        venues_items = {}
        for item in cart.items.all():
            venue = item.service.venue
            if venue not in venues_items:
                venues_items[venue] = []
            venues_items[venue].append(item)

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_creation_started',
                user=cart.customer,
                request=None,
                details={
                    'cart_id': cart.id,
                    'venues_count': len(venues_items),
                    'total_items': cart.total_items,
                    'total_price': str(cart.total_price),
                    'notes_provided': bool(notes)
                }
            )

        # Create a booking for each venue
        for venue, items in venues_items.items():
            # Calculate total price for this venue's items
            total_price = sum(item.total_price for item in items)

            # Create the booking
            booking = Booking.objects.create(
                customer=cart.customer,
                venue=venue,
                total_price=total_price,
                notes=notes,
                status=Booking.PENDING
            )

            # Create booking items
            booking_items_details = []
            for cart_item in items:
                booking_item = BookingItem.objects.create(
                    booking=booking,
                    service=cart_item.service,
                    service_title=cart_item.service.service_title,
                    service_price=cart_item.price_per_item,
                    quantity=cart_item.quantity,
                    scheduled_date=cart_item.selected_date,
                    scheduled_time=cart_item.selected_time_slot,
                    duration_minutes=cart_item.service.duration_minutes
                )

                booking_items_details.append({
                    'service_title': cart_item.service.service_title,
                    'scheduled_date': str(cart_item.selected_date),
                    'scheduled_time': str(cart_item.selected_time_slot),
                    'quantity': cart_item.quantity,
                    'price': str(cart_item.price_per_item)
                })

                # Update service availability
                try:
                    availability = ServiceAvailability.objects.get(
                        service=cart_item.service,
                        available_date=cart_item.selected_date,
                        start_time=cart_item.selected_time_slot
                    )
                    availability.book_slot()
                except ServiceAvailability.DoesNotExist:
                    # Create availability record if it doesn't exist
                    ServiceAvailability.objects.create(
                        service=cart_item.service,
                        available_date=cart_item.selected_date,
                        start_time=cart_item.selected_time_slot,
                        end_time=cart_item.selected_time_slot,  # Same as start time for now
                        max_bookings=10,  # Default max bookings
                        current_bookings=cart_item.quantity,
                        is_available=True
                    )

            if LOGGING_ENABLED:
                log_user_activity(
                    'booking_cart_app',
                    'booking_created',
                    user=cart.customer,
                    request=None,
                    details={
                        'booking_id': str(booking.booking_id),
                        'venue_name': venue.venue_name,
                        'venue_id': venue.id,
                        'total_price': str(total_price),
                        'items_count': len(items),
                        'booking_items': booking_items_details,
                        'status': booking.status
                    }
                )

            bookings_created.append(booking)

        if LOGGING_ENABLED:
            log_user_activity(
                'booking_cart_app',
                'booking_creation_completed',
                user=cart.customer,
                request=None,
                details={
                    'cart_id': cart.id,
                    'bookings_created_count': len(bookings_created),
                    'total_amount': str(sum(b.total_price for b in bookings_created)),
                    'booking_ids': [str(b.booking_id) for b in bookings_created]
                }
            )

        return bookings_created

    except Exception as e:
        if LOGGING_ENABLED:
            log_error(
                'booking_cart_app',
                'booking_creation_error',
                f"Error creating bookings from cart: {str(e)}",
                user=cart.customer,
                request=None,
                exception=e,
                details={'cart_id': cart.id}
            )
        raise


def get_upcoming_bookings(user, limit=None):
    """
    Get upcoming bookings for a customer.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        return Booking.objects.none()
    
    today = timezone.now().date()
    bookings = Booking.objects.filter(
        customer=user,
        items__scheduled_date__gte=today,
        status__in=[Booking.PENDING, Booking.CONFIRMED]
    ).distinct().order_by('items__scheduled_date', 'items__scheduled_time')
    
    if limit:
        bookings = bookings[:limit]
    
    return bookings


def get_past_bookings(user, limit=None):
    """
    Get past bookings for a customer.
    """
    if not user.is_authenticated or not hasattr(user, 'is_customer') or not user.is_customer:
        return Booking.objects.none()
    
    today = timezone.now().date()
    bookings = Booking.objects.filter(
        customer=user
    ).filter(
        Q(items__scheduled_date__lt=today) | 
        Q(status__in=[Booking.CANCELLED, Booking.COMPLETED])
    ).distinct().order_by('-items__scheduled_date', '-items__scheduled_time')
    
    if limit:
        bookings = bookings[:limit]
    
    return bookings


def can_cancel_booking(booking):
    """
    Check if a booking can be cancelled (within 6 hours of creation).
    """
    if booking.status in [Booking.CANCELLED, Booking.COMPLETED]:
        return False
    
    # Check if booking was made within last 6 hours
    time_since_booking = timezone.now() - booking.booking_date
    return time_since_booking <= timedelta(hours=6)


def get_booking_analytics(user=None, venue=None, start_date=None, end_date=None):
    """
    Get comprehensive booking analytics data.
    Can be filtered by user, venue, or date range.
    """
    from django.db.models import Sum, Count, Avg
    from decimal import Decimal

    bookings = Booking.objects.all()

    if user:
        bookings = bookings.filter(customer=user)

    if venue:
        bookings = bookings.filter(venue=venue)

    if start_date:
        bookings = bookings.filter(booking_date__gte=start_date)

    if end_date:
        bookings = bookings.filter(booking_date__lte=end_date)

    # Calculate basic analytics
    total_bookings = bookings.count()
    pending_bookings = bookings.filter(status=Booking.PENDING).count()
    confirmed_bookings = bookings.filter(status=Booking.CONFIRMED).count()
    cancelled_bookings = bookings.filter(status=Booking.CANCELLED).count()
    declined_bookings = bookings.filter(status=Booking.DECLINED).count()
    completed_bookings = bookings.filter(status=Booking.COMPLETED).count()
    disputed_bookings = bookings.filter(status=Booking.DISPUTED).count()
    no_show_bookings = bookings.filter(status=Booking.NO_SHOW).count()

    # Calculate revenue
    revenue_bookings = bookings.filter(status__in=[Booking.CONFIRMED, Booking.COMPLETED])
    total_revenue = revenue_bookings.aggregate(total=Sum('total_price'))['total'] or Decimal('0.00')
    avg_booking_value = revenue_bookings.aggregate(avg=Avg('total_price'))['avg'] or Decimal('0.00')

    # Calculate rates
    cancellation_rate = (cancelled_bookings / total_bookings * 100) if total_bookings > 0 else 0
    dispute_rate = (disputed_bookings / total_bookings * 100) if total_bookings > 0 else 0
    no_show_rate = (no_show_bookings / total_bookings * 100) if total_bookings > 0 else 0
    confirmation_rate = (confirmed_bookings / total_bookings * 100) if total_bookings > 0 else 0

    return {
        'total_bookings': total_bookings,
        'pending_bookings': pending_bookings,
        'confirmed_bookings': confirmed_bookings,
        'cancelled_bookings': cancelled_bookings,
        'declined_bookings': declined_bookings,
        'completed_bookings': completed_bookings,
        'disputed_bookings': disputed_bookings,
        'no_show_bookings': no_show_bookings,
        'total_revenue': total_revenue,
        'avg_booking_value': avg_booking_value,
        'cancellation_rate': round(cancellation_rate, 2),
        'dispute_rate': round(dispute_rate, 2),
        'no_show_rate': round(no_show_rate, 2),
        'confirmation_rate': round(confirmation_rate, 2),
    }


def get_dispute_analytics(start_date=None, end_date=None):
    """
    Get analytics specifically for disputes.
    """
    from django.db.models import Count

    disputes = Booking.objects.filter(status=Booking.DISPUTED)

    if start_date:
        disputes = disputes.filter(dispute_filed_at__gte=start_date)

    if end_date:
        disputes = disputes.filter(dispute_filed_at__lte=end_date)

    total_disputes = disputes.count()
    resolved_disputes = disputes.filter(dispute_resolved_at__isnull=False).count()
    unresolved_disputes = disputes.filter(dispute_resolved_at__isnull=True).count()

    # Disputes by who filed them
    customer_disputes = disputes.filter(dispute_filed_by='customer').count()
    provider_disputes = disputes.filter(dispute_filed_by='provider').count()
    admin_disputes = disputes.filter(dispute_filed_by='admin').count()

    # Average resolution time for resolved disputes
    resolved_dispute_times = []
    for dispute in disputes.filter(dispute_resolved_at__isnull=False):
        if dispute.dispute_filed_at and dispute.dispute_resolved_at:
            resolution_time = dispute.dispute_resolved_at - dispute.dispute_filed_at
            resolved_dispute_times.append(resolution_time.total_seconds() / 3600)  # Convert to hours

    avg_resolution_time = sum(resolved_dispute_times) / len(resolved_dispute_times) if resolved_dispute_times else 0

    return {
        'total_disputes': total_disputes,
        'resolved_disputes': resolved_disputes,
        'unresolved_disputes': unresolved_disputes,
        'customer_disputes': customer_disputes,
        'provider_disputes': provider_disputes,
        'admin_disputes': admin_disputes,
        'avg_resolution_time_hours': round(avg_resolution_time, 2),
        'resolution_rate': round((resolved_disputes / total_disputes * 100) if total_disputes > 0 else 0, 2),
    }


def get_venue_booking_stats(venue_id=None):
    """
    Get booking statistics for venues.
    """
    from django.db.models import Count, Sum
    from venues_app.models import Venue

    if venue_id:
        venues = Venue.objects.filter(id=venue_id)
    else:
        venues = Venue.objects.all()

    venue_stats = []
    for venue in venues:
        bookings = Booking.objects.filter(venue=venue)
        total_bookings = bookings.count()
        total_revenue = bookings.filter(
            status__in=[Booking.CONFIRMED, Booking.COMPLETED]
        ).aggregate(total=Sum('total_price'))['total'] or 0

        venue_stats.append({
            'venue': venue,
            'total_bookings': total_bookings,
            'total_revenue': total_revenue,
            'pending_bookings': bookings.filter(status=Booking.PENDING).count(),
            'confirmed_bookings': bookings.filter(status=Booking.CONFIRMED).count(),
            'disputed_bookings': bookings.filter(status=Booking.DISPUTED).count(),
        })

    return venue_stats
