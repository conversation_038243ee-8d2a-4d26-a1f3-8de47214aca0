# """Database models for the dashboard application."""

# --- Third-Party Imports ---
from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from venues_app.models import Venue


class FavoriteVenue(models.Model):
    """
    Model for storing customer's favorite venues.
    Allows customers to mark venues as favorites for easy access and notifications.
    """

    # Core relationships
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='favorite_venues',
        help_text=_('Customer who favorited this venue')
    )
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='favorited_by',
        help_text=_('Venue that was favorited')
    )

    # Metadata
    added_date = models.DateTimeField(
        auto_now_add=True,
        help_text=_('When the venue was added to favorites')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text=_('When the favorite was last updated')
    )

    class Meta:
        verbose_name = _('Favorite Venue')
        verbose_name_plural = _('Favorite Venues')
        ordering = ['-added_date']
        unique_together = ['customer', 'venue']
        indexes = [
            models.Index(fields=['customer', '-added_date']),
            models.Index(fields=['venue', '-added_date']),
        ]

    def __str__(self):
        """Return string representation of favorite venue."""
        return f"{self.customer.email} - {self.venue.venue_name}"

    def save(self, *args, **kwargs):
        """Override save to ensure only customers can favorite venues."""
        if not self.customer.is_customer:
            raise ValueError(_('Only customers can favorite venues'))
        super().save(*args, **kwargs)
