import pytest
from decimal import Decimal
from django.contrib.auth.models import AnonymousUser
from django.contrib.messages import get_messages
from django.core import mail
from django.core.cache import cache
from django.core.paginator import Page
from django.http import JsonResponse
from django.test import Client
from django.urls import reverse
from django.utils import timezone
from model_bakery import baker

from accounts_app.models import CustomUser, ServiceProviderProfile, CustomerProfile
from venues_app.models import (
    Category,
    Venue,
    VenueCategory,
    VenueImage,
    VenueFAQ,
    Service,
    OperatingHours,
    FlaggedVenue,
    USCity,
)

# All tests use the database
pytestmark = pytest.mark.django_db


# --- Venue Search and Public Views Integration ---

def test_venue_search_complete_workflow():
    """Test complete venue search workflow with filters and pagination."""
    client = Client()

    # Step 1: Create test data
    category = baker.make(Category, category_name="Spa & Wellness", is_active=True)
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Luxury Spa", display_name="Luxury Spa")

    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Serenity Spa",
        short_description="Relaxing spa experience",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
        state="CA",
        county="Los Angeles",
        city="Beverly Hills"
    )
    baker.make(VenueCategory, venue=venue, category=category)
    baker.make(Service, venue=venue, service_title="Swedish Massage", price_min=Decimal("100.00"))

    # Step 2: GET search page
    search_url = reverse('venues_app:venue_search')
    response = client.get(search_url)
    assert response.status_code == 200
    assert 'venues_app/venue_search.html' in [t.name for t in response.templates]
    assert venue in response.context['venues']

    # Step 3: Search with query
    response = client.get(search_url, {'query': 'Serenity'})
    assert response.status_code == 200
    assert venue in response.context['venues']
    assert response.context['applied_filters']['query'] == 'Serenity'

    # Step 4: Filter by location
    response = client.get(search_url, {'location': 'Beverly Hills'})
    assert response.status_code == 200
    assert venue in response.context['venues']

    # Step 5: Filter by category
    response = client.get(search_url, {'category': category.id})
    assert response.status_code == 200
    assert venue in response.context['venues']


def test_venue_detail_complete_workflow():
    """Test complete venue detail page workflow with services and images."""
    client = Client()

    # Step 1: Create test data
    category = baker.make(Category, category_name="Spa & Wellness", is_active=True)
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Luxury Spa", display_name="Luxury Spa")

    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Serenity Spa",
        short_description="Relaxing spa experience",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
        state="CA",
        county="Los Angeles",
        city="Beverly Hills"
    )
    baker.make(VenueCategory, venue=venue, category=category)
    service = baker.make(
        Service,
        venue=venue,
        service_title="Swedish Massage",
        price_min=Decimal("100.00"),
        is_active=True
    )
    baker.make(VenueFAQ, venue=venue, question="What should I bring?", answer="Just yourself!")

    # Step 2: GET venue detail page
    detail_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})
    response = client.get(detail_url)
    assert response.status_code == 200
    assert 'venues_app/venue_detail.html' in [t.name for t in response.templates]
    assert response.context['venue'] == venue
    assert service in response.context['services']

    # Step 3: Verify venue data in context
    assert response.context['faqs'].count() == 1
    assert response.context['images'].count() == 0  # No images created
    assert venue.categories.count() == 1


def test_service_detail_complete_workflow():
    """Test complete service detail page workflow."""
    client = Client()

    # Step 1: Create test data
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Luxury Spa", display_name="Luxury Spa")

    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Serenity Spa",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    )
    service = baker.make(
        Service,
        venue=venue,
        service_title="Swedish Massage",
        short_description="Relaxing full-body massage",
        price_min=Decimal("100.00"),
        duration_minutes=60,
        is_active=True
    )

    # Step 2: GET service detail page
    detail_url = reverse('venues_app:service_detail', kwargs={
        'venue_slug': venue.slug,
        'service_slug': service.slug
    })
    response = client.get(detail_url)
    assert response.status_code == 200
    assert 'venues_app/service_detail.html' in [t.name for t in response.templates]
    assert response.context['service'] == service
    assert response.context['venue'] == venue


def test_location_autocomplete_api_integration():
    """Test location autocomplete API functionality."""
    client = Client()

    # Step 1: Create test city data
    city = baker.make(
        USCity,
        city="Beverly Hills",
        state_name="California",
        state_id="CA",
        county_name="Los Angeles"
    )

    # Step 2: Test autocomplete API
    api_url = reverse('venues_app:location_autocomplete')
    response = client.get(api_url, {'q': 'Beverly'})
    assert response.status_code == 200

    data = response.json()
    assert 'suggestions' in data
    assert len(data['suggestions']) > 0
    assert any('Beverly Hills' in item['label'] for item in data['suggestions'])


def test_category_venues_complete_workflow():
    """Test category-based venue filtering workflow."""
    client = Client()

    # Step 1: Create test data
    category = baker.make(Category, category_name="Spa & Wellness", is_active=True)
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Luxury Spa", display_name="Luxury Spa")

    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Serenity Spa",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    )
    baker.make(VenueCategory, venue=venue, category=category)

    # Step 2: GET category venues page
    category_url = reverse('venues_app:category_venues', kwargs={'category_slug': category.slug})
    response = client.get(category_url)
    assert response.status_code == 200
    assert 'venues_app/category_venues.html' in [t.name for t in response.templates]
    assert venue in response.context['venues']
    assert response.context['category'] == category


def test_venue_flagging_complete_workflow():
    """Test complete venue flagging workflow."""
    client = Client()

    # Step 1: Create test data
    customer_user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    customer = baker.make(CustomerProfile, user=customer_user)

    provider_user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=provider_user)

    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Test Venue",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    )

    # Step 2: Login as customer
    client.force_login(customer_user)

    # Step 3: POST flag venue
    flag_url = reverse('venues_app:flag_venue', kwargs={'venue_slug': venue.slug})
    flag_data = {
        'reason': 'This venue has inappropriate content'
    }
    response = client.post(flag_url, flag_data)
    assert response.status_code == 302  # Redirect after successful flag

    # Step 4: Verify flag was created
    assert FlaggedVenue.objects.filter(venue=venue, flagged_by=customer_user).exists()
    flagged_venue = FlaggedVenue.objects.get(venue=venue, flagged_by=customer_user)
    assert flagged_venue.reason == 'This venue has inappropriate content'
    assert flagged_venue.status == FlaggedVenue.PENDING


# --- Service Provider Venue Management Integration ---

def test_venue_creation_complete_workflow():
    """Test complete venue creation workflow for service providers."""
    client = Client()

    # Step 1: Create service provider and category
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="New Spa Business", display_name="New Spa Business")
    category = baker.make(Category, is_active=True)

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: GET venue creation page
    create_url = reverse('venues_app:venue_create')
    response = client.get(create_url)
    assert response.status_code == 200
    assert 'venues_app/venue_create.html' in [t.name for t in response.templates]

    # Step 4: POST venue creation data with operating hours
    venue_data = {
        'venue_name': 'New Serenity Spa',
        'short_description': 'A peaceful spa experience',
        'state': 'CA',
        'county': 'Los Angeles',
        'city': 'Beverly Hills',
        'street_number': '123',
        'street_name': 'Rodeo Drive',
        'tags': 'spa, massage, wellness',
        'categories': [category.id],
        # Operating hours formset data
        'operating_hours-TOTAL_FORMS': '7',
        'operating_hours-INITIAL_FORMS': '7',
        'operating_hours-MIN_NUM_FORMS': '0',
        'operating_hours-MAX_NUM_FORMS': '1000',
        # Monday - open
        'operating_hours-0-day': '0',
        'operating_hours-0-opening': '09:00',
        'operating_hours-0-closing': '17:00',
        'operating_hours-0-is_closed': False,
        # Tuesday - open
        'operating_hours-1-day': '1',
        'operating_hours-1-opening': '09:00',
        'operating_hours-1-closing': '17:00',
        'operating_hours-1-is_closed': False,
        # Wednesday - closed
        'operating_hours-2-day': '2',
        'operating_hours-2-opening': '',
        'operating_hours-2-closing': '',
        'operating_hours-2-is_closed': True,
        # Thursday - open
        'operating_hours-3-day': '3',
        'operating_hours-3-opening': '09:00',
        'operating_hours-3-closing': '17:00',
        'operating_hours-3-is_closed': False,
        # Friday - open
        'operating_hours-4-day': '4',
        'operating_hours-4-opening': '09:00',
        'operating_hours-4-closing': '17:00',
        'operating_hours-4-is_closed': False,
        # Saturday - closed
        'operating_hours-5-day': '5',
        'operating_hours-5-opening': '',
        'operating_hours-5-closing': '',
        'operating_hours-5-is_closed': True,
        # Sunday - closed
        'operating_hours-6-day': '6',
        'operating_hours-6-opening': '',
        'operating_hours-6-closing': '',
        'operating_hours-6-is_closed': True,
    }
    response = client.post(create_url, venue_data)
    assert response.status_code == 302  # Redirect after successful creation

    # Step 5: Verify venue was created
    assert Venue.objects.filter(venue_name='New Serenity Spa').exists()
    venue = Venue.objects.get(venue_name='New Serenity Spa')
    assert venue.service_provider == provider
    assert venue.approval_status == Venue.PENDING
    assert venue.visibility == Venue.ACTIVE

    # Step 6: Verify operating hours were created
    from venues_app.models import OperatingHours
    operating_hours = OperatingHours.objects.filter(venue=venue).order_by('day')
    assert operating_hours.count() == 4  # Only open days should be saved (Mon, Tue, Thu, Fri)

    # Check Monday (day 0)
    monday = operating_hours.filter(day=0).first()
    assert monday is not None
    assert not monday.is_closed
    assert str(monday.opening) == '09:00:00'
    assert str(monday.closing) == '17:00:00'

    # Check Wednesday (day 2) - should not exist since it's closed
    wednesday = operating_hours.filter(day=2).first()
    assert wednesday is None  # Closed days are not saved


def test_venue_update_complete_workflow():
    """Test complete venue update workflow for service providers."""
    client = Client()

    # Step 1: Create service provider with existing venue
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Spa Business", display_name="Spa Business")
    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Original Spa Name",
        short_description="Original description"
    )

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: GET venue edit page
    edit_url = reverse('venues_app:venue_edit')
    response = client.get(edit_url)
    assert response.status_code == 200
    assert 'venues_app/venue_edit.html' in [t.name for t in response.templates]
    assert response.context['object'] == venue

    # Step 4: POST venue update data
    update_data = {
        'venue_name': 'Updated Spa Name',
        'short_description': 'Updated description with more details',
        'state': venue.state,
        'county': venue.county,
        'city': venue.city,
        'street_number': venue.street_number,
        'street_name': venue.street_name,
        'operating_hours': '8AM-7PM',
        'tags': 'spa, massage, wellness, updated'
    }
    response = client.post(edit_url, update_data)
    assert response.status_code == 302  # Redirect after successful update

    # Step 5: Verify venue was updated
    venue.refresh_from_db()
    assert venue.venue_name == 'Updated Spa Name'
    assert venue.short_description == 'Updated description with more details'
    assert venue.operating_hours == '8AM-7PM'
    assert 'updated' in venue.tags


def test_venue_deletion_complete_workflow():
    """Test complete venue deletion workflow for service providers."""
    client = Client()

    # Step 1: Create service provider with existing venue
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Spa Business", display_name="Spa Business")
    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Spa to Delete"
    )
    venue_id = venue.id

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: GET venue delete confirmation page
    delete_url = reverse('venues_app:venue_delete')
    response = client.get(delete_url)
    assert response.status_code == 200
    assert 'venues_app/venue_delete.html' in [t.name for t in response.templates]
    assert response.context['object'] == venue

    # Step 4: POST venue deletion
    response = client.post(delete_url)
    assert response.status_code == 302  # Redirect after successful deletion

    # Step 5: Verify venue was deleted
    assert not Venue.objects.filter(id=venue_id).exists()


def test_service_management_complete_workflow():
    """Test complete service management workflow for service providers."""
    client = Client()

    # Step 1: Create service provider with venue
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Spa Business", display_name="Spa Business")
    venue = baker.make(Venue, service_provider=provider, venue_name="Test Spa")

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: GET service creation page
    create_url = reverse('venues_app:service_create')
    response = client.get(create_url)
    assert response.status_code == 200
    assert 'venues_app/service_create.html' in [t.name for t in response.templates]

    # Step 4: POST service creation data
    service_data = {
        'service_title': 'Deep Tissue Massage',
        'short_description': 'Therapeutic deep tissue massage',
        'price_min': '120.00',
        'price_max': '180.00',
        'duration_minutes': 90,
        'is_active': True
    }
    response = client.post(create_url, service_data)
    assert response.status_code == 302  # Redirect after successful creation

    # Step 5: Verify service was created
    assert Service.objects.filter(service_title='Deep Tissue Massage').exists()
    service = Service.objects.get(service_title='Deep Tissue Massage')
    assert service.venue == venue
    assert service.price_min == Decimal('120.00')
    assert service.duration_minutes == 90

    # Step 6: Test service update
    edit_url = reverse('venues_app:service_edit', kwargs={'pk': service.pk})
    response = client.get(edit_url)
    assert response.status_code == 200
    assert 'venues_app/service_edit.html' in [t.name for t in response.templates]

    update_data = {
        'service_title': 'Updated Deep Tissue Massage',
        'short_description': 'Updated therapeutic massage',
        'price_min': '130.00',
        'price_max': '190.00',
        'duration_minutes': 90,
        'is_active': True
    }
    response = client.post(edit_url, update_data)
    assert response.status_code == 302  # Redirect after successful update

    # Step 7: Verify service was updated
    service.refresh_from_db()
    assert service.service_title == 'Updated Deep Tissue Massage'
    assert service.price_min == Decimal('130.00')

    # Step 8: Test service deletion
    delete_url = reverse('venues_app:service_delete', kwargs={'pk': service.pk})
    response = client.post(delete_url)
    assert response.status_code == 302  # Redirect after successful deletion

    # Step 9: Verify service was deleted
    assert not Service.objects.filter(pk=service.pk).exists()


def test_provider_venues_dashboard_integration():
    """Test service provider venues dashboard integration."""
    client = Client()

    # Step 1: Create service provider with venue and services
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="Spa Business", display_name="Spa Business")
    venue = baker.make(Venue, service_provider=provider, venue_name="Test Spa")
    service1 = baker.make(Service, venue=venue, service_title="Massage", is_active=True)
    service2 = baker.make(Service, venue=venue, service_title="Facial", is_active=False)

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: GET provider venues dashboard
    dashboard_url = reverse('venues_app:provider_venues')
    response = client.get(dashboard_url)
    assert response.status_code == 200
    assert 'venues_app/provider/venues.html' in [t.name for t in response.templates]
    assert response.context['venue'] == venue
    assert service1 in response.context['services']
    assert service2 in response.context['services']


# --- Admin Venue Management Integration ---

def test_admin_venue_approval_complete_workflow():
    """Test complete admin venue approval workflow."""
    client = Client()

    # Step 1: Create admin user and pending venue
    admin_user = baker.make(CustomUser, is_staff=True, is_superuser=True)
    provider_user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=provider_user)
    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Pending Spa",
        approval_status=Venue.PENDING
    )

    # Step 2: Login as admin
    client.force_login(admin_user)

    # Step 3: GET admin venue approval dashboard
    dashboard_url = reverse('venues_app:admin_venue_approval_dashboard')
    response = client.get(dashboard_url)
    assert response.status_code == 200
    assert 'venues_app/admin/venue_approval_dashboard.html' in [t.name for t in response.templates]
    assert response.context['pending_venues'] == 1
    assert venue in response.context['recent_pending']

    # Step 4: GET venue detail for approval
    detail_url = reverse('venues_app:admin_venue_detail', kwargs={'venue_id': venue.id})
    response = client.get(detail_url)
    assert response.status_code == 200
    assert 'venues_app/admin/venue_detail.html' in [t.name for t in response.templates]
    assert response.context['venue'] == venue

    # Step 5: POST venue approval
    approval_url = reverse('venues_app:admin_venue_approval', kwargs={'venue_id': venue.id})
    approval_data = {
        'action': 'approve',
        'admin_notes': 'Venue meets all requirements'
    }
    response = client.post(approval_url, approval_data)
    assert response.status_code == 302  # Redirect after approval

    # Step 6: Verify venue was approved
    venue.refresh_from_db()
    assert venue.approval_status == Venue.APPROVED
    assert venue.admin_notes == 'Venue meets all requirements'


def test_admin_venue_rejection_complete_workflow():
    """Test complete admin venue rejection workflow."""
    client = Client()

    # Step 1: Create admin user and pending venue
    admin_user = baker.make(CustomUser, is_staff=True, is_superuser=True)
    provider_user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=provider_user)
    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Problematic Spa",
        approval_status=Venue.PENDING
    )

    # Step 2: Login as admin
    client.force_login(admin_user)

    # Step 3: POST venue rejection
    approval_url = reverse('venues_app:admin_venue_approval', kwargs={'venue_id': venue.id})
    rejection_data = {
        'action': 'reject',
        'admin_notes': 'Venue does not meet quality standards'
    }
    response = client.post(approval_url, rejection_data)
    assert response.status_code == 302  # Redirect after rejection

    # Step 4: Verify venue was rejected
    venue.refresh_from_db()
    assert venue.approval_status == Venue.REJECTED
    assert venue.admin_notes == 'Venue does not meet quality standards'


def test_admin_category_management_complete_workflow():
    """Test complete admin category management workflow."""
    client = Client()

    # Step 1: Create admin user
    admin_user = baker.make(CustomUser, is_staff=True, is_superuser=True)

    # Step 2: Login as admin
    client.force_login(admin_user)

    # Step 3: GET category list
    list_url = reverse('venues_app:admin_category_list')
    response = client.get(list_url)
    assert response.status_code == 200
    assert 'venues_app/admin/category_list.html' in [t.name for t in response.templates]

    # Step 4: GET category creation page
    create_url = reverse('venues_app:admin_category_create')
    response = client.get(create_url)
    assert response.status_code == 200
    assert 'venues_app/admin/category_create.html' in [t.name for t in response.templates]

    # Step 5: POST category creation
    category_data = {
        'category_name': 'Wellness & Therapy',
        'category_description': 'Comprehensive wellness and therapy services',
        'is_active': True
    }
    response = client.post(create_url, category_data)
    assert response.status_code == 302  # Redirect after creation

    # Step 6: Verify category was created
    assert Category.objects.filter(category_name='Wellness & Therapy').exists()
    category = Category.objects.get(category_name='Wellness & Therapy')
    assert category.is_active is True
    assert 'wellness-therapy' in category.slug


def test_admin_flagged_venues_management_workflow():
    """Test complete admin flagged venues management workflow."""
    client = Client()

    # Step 1: Create admin user and flagged venue
    admin_user = baker.make(CustomUser, is_staff=True, is_superuser=True)
    customer_user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    provider_user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=provider_user)
    venue = baker.make(Venue, service_provider=provider, venue_name="Flagged Spa")
    flagged_venue = baker.make(
        FlaggedVenue,
        venue=venue,
        flagged_by=customer_user,
        reason="Inappropriate content found",
        status=FlaggedVenue.PENDING
    )

    # Step 2: Login as admin
    client.force_login(admin_user)

    # Step 3: GET flagged venues list
    flagged_url = reverse('venues_app:admin_flagged_venues')
    response = client.get(flagged_url)
    assert response.status_code == 200
    assert 'venues_app/admin/flagged_venues.html' in [t.name for t in response.templates]
    assert flagged_venue in response.context['flagged_venues']


# --- Complex Integration Workflows ---

def test_venue_search_with_multiple_filters_integration():
    """Test venue search with multiple complex filters applied simultaneously."""
    client = Client()

    # Step 1: Create comprehensive test data
    category1 = baker.make(Category, category_name="Spa", is_active=True)
    category2 = baker.make(Category, category_name="Massage", is_active=True)

    user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider1 = baker.make(ServiceProviderProfile, user=user1, legal_name="Luxury Spa", display_name="Luxury Spa")

    user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider2 = baker.make(ServiceProviderProfile, user=user2, legal_name="Budget Spa", display_name="Budget Spa")

    # Create venues with different characteristics
    venue1 = baker.make(
        Venue,
        service_provider=provider1,
        venue_name="Premium Serenity Spa",
        short_description="Luxury spa experience",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
        state="CA",
        county="Los Angeles",
        city="Beverly Hills",
        tags="luxury, premium, spa"
    )
    venue2 = baker.make(
        Venue,
        service_provider=provider2,
        venue_name="Budget Wellness Center",
        short_description="Affordable wellness services",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE,
        state="CA",
        county="Orange",
        city="Anaheim",
        tags="budget, affordable, wellness"
    )

    baker.make(VenueCategory, venue=venue1, category=category1)
    baker.make(VenueCategory, venue=venue2, category=category2)

    baker.make(Service, venue=venue1, service_title="Premium Massage", price_min=Decimal("200.00"))
    baker.make(Service, venue=venue2, service_title="Basic Massage", price_min=Decimal("50.00"))

    # Step 2: Test complex search with multiple filters
    search_url = reverse('venues_app:venue_search')

    # Test query + location + category filter
    response = client.get(search_url, {
        'query': 'spa',
        'location': 'Beverly Hills',
        'category': category1.id,
        'sort_by': 'price_high'
    })
    assert response.status_code == 200
    assert venue1 in response.context['venues']
    assert venue2 not in response.context['venues']

    # Test price range filtering
    response = client.get(search_url, {
        'min_price': '100',
        'max_price': '300',
        'sort_by': 'price_low'
    })
    assert response.status_code == 200
    assert venue1 in response.context['venues']
    assert venue2 not in response.context['venues']


def test_venue_approval_with_services_and_images_workflow():
    """Test complete venue approval workflow including services and images."""
    client = Client()

    # Step 1: Create comprehensive venue data
    admin_user = baker.make(CustomUser, is_staff=True, is_superuser=True)
    provider_user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=provider_user)
    category = baker.make(Category, category_name="Full Service Spa", is_active=True)

    venue = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Complete Spa Experience",
        approval_status=Venue.PENDING
    )
    baker.make(VenueCategory, venue=venue, category=category)
    baker.make(Service, venue=venue, service_title="Signature Massage", is_active=True)
    baker.make(Service, venue=venue, service_title="Facial Treatment", is_active=True)
    baker.make(VenueImage, venue=venue, order=1)
    baker.make(VenueImage, venue=venue, order=2)
    baker.make(VenueFAQ, venue=venue, question="What services do you offer?")

    # Step 2: Login as admin and approve venue
    client.force_login(admin_user)

    # Step 3: Verify all venue components in admin detail view
    detail_url = reverse('venues_app:admin_venue_detail', kwargs={'venue_id': venue.id})
    response = client.get(detail_url)
    assert response.status_code == 200
    assert response.context['venue'] == venue
    assert response.context['venue_services'].count() == 2
    assert response.context['venue_images'].count() == 2
    assert response.context['venue_faqs'].count() == 1
    assert response.context['venue_categories'].count() == 1

    # Step 4: Approve venue
    approval_url = reverse('venues_app:admin_venue_approval', kwargs={'venue_id': venue.id})
    response = client.post(approval_url, {
        'action': 'approve',
        'admin_notes': 'Complete venue with all components'
    })
    assert response.status_code == 302

    # Step 5: Verify venue is now searchable
    venue.refresh_from_db()
    assert venue.approval_status == Venue.APPROVED

    # Step 6: Test public access to approved venue
    client.logout()
    detail_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})
    response = client.get(detail_url)
    assert response.status_code == 200
    assert response.context['venue'] == venue


def test_service_provider_permission_enforcement_integration():
    """Test service provider permission enforcement across all provider views."""
    client = Client()

    # Step 1: Create different user types
    customer_user = baker.make(CustomUser, role=CustomUser.CUSTOMER)
    provider_user1 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider_user2 = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)

    provider1 = baker.make(ServiceProviderProfile, user=provider_user1)
    provider2 = baker.make(ServiceProviderProfile, user=provider_user2)

    venue1 = baker.make(Venue, service_provider=provider1, venue_name="Provider 1 Spa")
    venue2 = baker.make(Venue, service_provider=provider2, venue_name="Provider 2 Spa")

    service1 = baker.make(Service, venue=venue1, service_title="Provider 1 Service")

    # Step 2: Test customer cannot access provider views
    client.force_login(customer_user)

    provider_urls = [
        reverse('venues_app:venue_create'),
        reverse('venues_app:venue_edit'),
        reverse('venues_app:provider_venues'),
        reverse('venues_app:service_create'),
    ]

    for url in provider_urls:
        response = client.get(url)
        assert response.status_code == 302  # Redirect due to permission denied

    # Step 3: Test provider can only access their own venue
    client.force_login(provider_user2)

    # Provider 2 should not be able to edit Provider 1's service
    edit_service_url = reverse('venues_app:service_edit', kwargs={'pk': service1.pk})
    response = client.get(edit_service_url)
    assert response.status_code == 404  # Should not find service belonging to other provider


def test_venue_visibility_and_approval_status_integration():
    """Test venue visibility and approval status combinations in search results."""
    client = Client()

    # Step 1: Create venues with different status combinations
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user)

    # Approved and Active (should appear in search)
    venue1 = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Approved Active Spa",
        approval_status=Venue.APPROVED,
        visibility=Venue.ACTIVE
    )

    # Approved but Inactive (should not appear in search)
    venue2 = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Approved Inactive Spa",
        approval_status=Venue.APPROVED,
        visibility=Venue.INACTIVE
    )

    # Pending and Active (should not appear in search)
    venue3 = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Pending Active Spa",
        approval_status=Venue.PENDING,
        visibility=Venue.ACTIVE
    )

    # Rejected and Active (should not appear in search)
    venue4 = baker.make(
        Venue,
        service_provider=provider,
        venue_name="Rejected Active Spa",
        approval_status=Venue.REJECTED,
        visibility=Venue.ACTIVE
    )

    # Step 2: Test search results only show approved and active venues
    search_url = reverse('venues_app:venue_search')
    response = client.get(search_url)
    assert response.status_code == 200

    venues_in_results = response.context['venues']
    assert venue1 in venues_in_results
    assert venue2 not in venues_in_results
    assert venue3 not in venues_in_results
    assert venue4 not in venues_in_results

    # Step 3: Test direct access to non-approved venues returns 404
    for venue in [venue2, venue3, venue4]:
        detail_url = reverse('venues_app:venue_detail', kwargs={'venue_slug': venue.slug})
        response = client.get(detail_url)
        assert response.status_code == 404


# --- Venue Creation with Images Integration Tests ---

def test_venue_creation_with_gallery_images_workflow():
    """Test complete venue creation workflow with gallery images."""
    from admin_app.tests.test_utils import create_test_image

    client = Client()

    # Step 1: Create service provider and category
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="New Spa Business", display_name="New Spa Business")
    category = baker.make(Category, is_active=True)

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: GET venue creation page
    create_url = reverse('venues_app:venue_create')
    response = client.get(create_url)
    assert response.status_code == 200
    assert 'venues_app/venue_create.html' in [t.name for t in response.templates]
    assert 'gallery_images_form' in response.context

    # Step 4: Create test images
    image1 = create_test_image('gallery1.jpg')
    image2 = create_test_image('gallery2.jpg')
    image3 = create_test_image('gallery3.jpg')

    # Step 5: POST venue creation data with gallery images
    venue_data = {
        'venue_name': 'New Serenity Spa',
        'short_description': 'A peaceful spa experience',
        'state': 'CA',
        'county': 'Los Angeles',
        'city': 'Beverly Hills',
        'street_number': '123',
        'street_name': 'Rodeo Drive',
        'tags': 'spa, massage, wellness',
        'categories': [category.id],
        # Operating hours formset data
        'operating_hours-TOTAL_FORMS': '7',
        'operating_hours-INITIAL_FORMS': '0',
        'operating_hours-MIN_NUM_FORMS': '0',
        'operating_hours-MAX_NUM_FORMS': '1000',
        # Monday - open
        'operating_hours-0-day': '0',
        'operating_hours-0-opening': '09:00',
        'operating_hours-0-closing': '17:00',
        'operating_hours-0-is_closed': False,
        # Other days closed
        'operating_hours-1-day': '1',
        'operating_hours-1-is_closed': True,
        'operating_hours-2-day': '2',
        'operating_hours-2-is_closed': True,
        'operating_hours-3-day': '3',
        'operating_hours-3-is_closed': True,
        'operating_hours-4-day': '4',
        'operating_hours-4-is_closed': True,
        'operating_hours-5-day': '5',
        'operating_hours-5-is_closed': True,
        'operating_hours-6-day': '6',
        'operating_hours-6-is_closed': True,
    }

    # Combine data and files for Django test client
    post_data = venue_data.copy()
    post_data.update({
        'image_1': image1,
        'image_2': image2,
        'image_4': image3  # Test non-sequential slots
    })
    response = client.post(create_url, post_data)
    assert response.status_code == 302  # Redirect after successful creation

    # Step 6: Verify venue was created
    assert Venue.objects.filter(venue_name='New Serenity Spa').exists()
    venue = Venue.objects.get(venue_name='New Serenity Spa')
    assert venue.service_provider == provider
    assert venue.approval_status == Venue.PENDING

    # Step 7: Verify gallery images were created
    gallery_images = venue.images.filter(is_active=True).order_by('order')
    assert gallery_images.count() == 3

    # Check image ordering matches slots
    first_image = gallery_images.filter(order=1).first()
    second_image = gallery_images.filter(order=2).first()
    fourth_image = gallery_images.filter(order=4).first()
    assert first_image is not None
    assert second_image is not None
    assert fourth_image is not None

    # Check that slot 3 is empty
    third_image = gallery_images.filter(order=3).first()
    assert third_image is None

    # First image should be primary if no main image
    if not venue.main_image:
        assert first_image.is_primary


def test_venue_creation_with_all_image_slots_filled():
    """Test venue creation with all 5 image slots filled."""
    from admin_app.tests.test_utils import create_test_image

    client = Client()

    # Step 1: Create service provider and category
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="New Spa Business", display_name="New Spa Business")
    category = baker.make(Category, is_active=True)

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: Create 5 test images (maximum allowed)
    images = [create_test_image(f'gallery{i}.jpg') for i in range(1, 6)]

    # Step 4: POST venue creation data with all 5 gallery image slots
    venue_data = {
        'venue_name': 'New Serenity Spa',
        'short_description': 'A peaceful spa experience',
        'state': 'CA',
        'county': 'Los Angeles',
        'city': 'Beverly Hills',
        'street_number': '123',
        'street_name': 'Rodeo Drive',
        'tags': 'spa, massage, wellness',
        'categories': [category.id],
        # Operating hours formset data
        'operating_hours-TOTAL_FORMS': '7',
        'operating_hours-INITIAL_FORMS': '0',
        'operating_hours-MIN_NUM_FORMS': '0',
        'operating_hours-MAX_NUM_FORMS': '1000',
        'operating_hours-0-day': '0',
        'operating_hours-0-is_closed': True,
        'operating_hours-1-day': '1',
        'operating_hours-1-is_closed': True,
        'operating_hours-2-day': '2',
        'operating_hours-2-is_closed': True,
        'operating_hours-3-day': '3',
        'operating_hours-3-is_closed': True,
        'operating_hours-4-day': '4',
        'operating_hours-4-is_closed': True,
        'operating_hours-5-day': '5',
        'operating_hours-5-is_closed': True,
        'operating_hours-6-day': '6',
        'operating_hours-6-is_closed': True,
    }

    # Combine data and files for Django test client
    post_data = venue_data.copy()
    post_data.update({
        'image_1': images[0],
        'image_2': images[1],
        'image_3': images[2],
        'image_4': images[3],
        'image_5': images[4]
    })

    create_url = reverse('venues_app:venue_create')
    response = client.post(create_url, post_data)

    # Step 5: Should redirect after successful creation
    assert response.status_code == 302

    # Step 6: Verify venue was created with all 5 images
    assert Venue.objects.filter(venue_name='New Serenity Spa').exists()
    venue = Venue.objects.get(venue_name='New Serenity Spa')

    gallery_images = venue.images.filter(is_active=True).order_by('order')
    assert gallery_images.count() == 5

    # Check all slots are filled
    for i in range(1, 6):
        image = gallery_images.filter(order=i).first()
        assert image is not None


def test_venue_creation_with_invalid_image_type():
    """Test venue creation fails with invalid image type in one slot."""
    from admin_app.tests.test_utils import create_test_image, create_invalid_image_file

    client = Client()

    # Step 1: Create service provider and category
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="New Spa Business", display_name="New Spa Business")
    category = baker.make(Category, is_active=True)

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: Create valid and invalid files
    valid_image = create_test_image('gallery1.jpg')
    invalid_file = create_invalid_image_file('document.txt')

    # Step 4: POST venue creation data with invalid file in slot 2
    venue_data = {
        'venue_name': 'New Serenity Spa',
        'short_description': 'A peaceful spa experience',
        'state': 'CA',
        'county': 'Los Angeles',
        'city': 'Beverly Hills',
        'street_number': '123',
        'street_name': 'Rodeo Drive',
        'tags': 'spa, massage, wellness',
        'categories': [category.id],
        # Operating hours formset data
        'operating_hours-TOTAL_FORMS': '7',
        'operating_hours-INITIAL_FORMS': '0',
        'operating_hours-MIN_NUM_FORMS': '0',
        'operating_hours-MAX_NUM_FORMS': '1000',
        'operating_hours-0-day': '0',
        'operating_hours-0-is_closed': True,
        'operating_hours-1-day': '1',
        'operating_hours-1-is_closed': True,
        'operating_hours-2-day': '2',
        'operating_hours-2-is_closed': True,
        'operating_hours-3-day': '3',
        'operating_hours-3-is_closed': True,
        'operating_hours-4-day': '4',
        'operating_hours-4-is_closed': True,
        'operating_hours-5-day': '5',
        'operating_hours-5-is_closed': True,
        'operating_hours-6-day': '6',
        'operating_hours-6-is_closed': True,
    }

    # Combine data and files for Django test client
    post_data = venue_data.copy()
    post_data.update({
        'image_1': valid_image,
        'image_2': invalid_file  # Invalid file type
    })

    create_url = reverse('venues_app:venue_create')
    response = client.post(create_url, post_data)

    # Step 5: Should return form with errors (not redirect)
    assert response.status_code == 200
    assert 'venues_app/venue_create.html' in [t.name for t in response.templates]

    # Step 6: Verify venue was not created
    assert not Venue.objects.filter(venue_name='New Serenity Spa').exists()

    # Step 7: Check that form has errors for image_2
    form = response.context['gallery_images_form']
    assert not form.is_valid()
    assert 'image_2' in form.errors


def test_venue_creation_field_specific_error_display():
    """Test that field-specific errors are displayed properly in venue creation."""
    client = Client()

    # Step 1: Create service provider
    user = baker.make(CustomUser, role=CustomUser.SERVICE_PROVIDER)
    provider = baker.make(ServiceProviderProfile, user=user, legal_name="New Spa Business", display_name="New Spa Business")

    # Step 2: Login as service provider
    client.force_login(user)

    # Step 3: POST incomplete venue creation data (missing required fields)
    venue_data = {
        'venue_name': '',  # Required field missing
        'short_description': '',  # Required field missing
        'state': '',  # Required field missing
        'county': 'Los Angeles',
        'city': 'Beverly Hills',
        'street_number': '123',
        'street_name': 'Rodeo Drive',
        # No categories provided (required)
        # Operating hours formset data
        'operating_hours-TOTAL_FORMS': '7',
        'operating_hours-INITIAL_FORMS': '0',
        'operating_hours-MIN_NUM_FORMS': '0',
        'operating_hours-MAX_NUM_FORMS': '1000',
        'operating_hours-0-day': '0',
        'operating_hours-0-is_closed': True,
        'operating_hours-1-day': '1',
        'operating_hours-1-is_closed': True,
        'operating_hours-2-day': '2',
        'operating_hours-2-is_closed': True,
        'operating_hours-3-day': '3',
        'operating_hours-3-is_closed': True,
        'operating_hours-4-day': '4',
        'operating_hours-4-is_closed': True,
        'operating_hours-5-day': '5',
        'operating_hours-5-is_closed': True,
        'operating_hours-6-day': '6',
        'operating_hours-6-is_closed': True,
    }

    create_url = reverse('venues_app:venue_create')
    response = client.post(create_url, venue_data)

    # Step 4: Should return form with errors (not redirect)
    assert response.status_code == 200
    assert 'venues_app/venue_create.html' in [t.name for t in response.templates]

    # Step 5: Verify form has field-specific errors
    form = response.context['form']
    assert not form.is_valid()
    assert 'venue_name' in form.errors
    assert 'short_description' in form.errors
    assert 'state' in form.errors
    assert 'categories' in form.errors

    # Step 6: Verify venue was not created
    assert not Venue.objects.filter(venue_name='').exists()

    # Step 7: Check that no generic error message is in messages
    messages = list(get_messages(response.wsgi_request))
    generic_error_found = any('Please correct the errors below' in str(msg) for msg in messages)
    assert not generic_error_found  # Generic error should not be present