# --- Django Imports ---
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

# --- Local App Imports ---
from notifications_app.utils import run_async
from .models import FlaggedVenue, Venue, VenueImage

# Store original venue state for comparison
_venue_original_state = {}


@receiver(pre_save, sender=Venue)
def store_venue_original_state(sender, instance, **kwargs):
    """Store original venue state before save for comparison."""
    if instance.pk:
        try:
            original = Venue.objects.get(pk=instance.pk)
            _venue_original_state[instance.pk] = {
                'approval_status': original.approval_status,
                'approved_at': original.approved_at,
                'rejected_at': getattr(original, 'rejected_at', None),
            }
        except Venue.DoesNotExist:
            pass


@receiver(post_save, sender=Venue)
def venue_status_changed(sender, instance, created, **kwargs):
    """Handle venue status changes and send notifications."""
    try:
        from notifications_app.utils import (
            notify_venue_submitted_for_approval,
            notify_venue_approved,
            notify_venue_rejected
        )

        # Pre-load the relationship to avoid issues in async context
        try:
            # Force evaluation of the relationship in the main thread
            provider_user = instance.service_provider.user
            if not provider_user:
                return  # Skip if no user found
        except Exception:
            # Skip notification if we can't access the provider user
            return

        if created:
            # New venue created - notify provider that it was submitted for approval
            if instance.approval_status == 'pending':
                run_async(notify_venue_submitted_for_approval, instance)
        else:
            # Existing venue updated - check for status changes
            original_state = _venue_original_state.get(instance.pk, {})

            # Check if status changed to approved
            if (instance.approval_status == 'approved' and
                original_state.get('approval_status') != 'approved'):
                run_async(notify_venue_approved, instance)

            # Check if status changed to rejected
            elif (instance.approval_status == 'rejected' and
                  original_state.get('approval_status') != 'rejected'):
                rejection_reason = getattr(instance, 'admin_notes', 'No specific reason provided')
                run_async(notify_venue_rejected, instance, rejection_reason)

            # Clean up stored state
            if instance.pk in _venue_original_state:
                del _venue_original_state[instance.pk]

    except ImportError:
        # notifications_app not available yet - skip notifications
        pass


@receiver(post_save, sender=FlaggedVenue)
def venue_flagged(sender, instance, created, **kwargs):
    """Handle venue flagging and send notifications."""
    try:
        from notifications_app.utils import notify_venue_flagged, notify_venue_flag_reviewed

        # Pre-load the relationship to avoid issues in async context
        try:
            # Force evaluation of the relationship in the main thread
            provider_user = instance.venue.service_provider.user
            if not provider_user:
                return  # Skip if no user found
        except Exception:
            # Skip notification if we can't access the provider user
            return

        if created:
            # New flag created - notify admins
            run_async(notify_venue_flagged, instance)
        else:
            # Existing flag updated - check if it was reviewed
            if instance.status in ['reviewed', 'resolved'] and instance.reviewed_at:
                # Check if this is a new review (reviewed_at was just set)
                old_instance = FlaggedVenue.objects.filter(pk=instance.pk).first()
                if old_instance and old_instance.reviewed_at != instance.reviewed_at:
                    run_async(notify_venue_flag_reviewed, instance)
    except ImportError:
        # notifications_app not available yet - skip notifications
        pass


@receiver(pre_save, sender=Venue)
def venue_pre_save(sender, instance, **kwargs):
    """Handle venue pre-save operations."""
    # Set approved_at timestamp when status changes to approved
    if instance.approval_status == 'approved' and not instance.approved_at:
        instance.approved_at = timezone.now()
    elif instance.approval_status != 'approved':
        instance.approved_at = None

    # Check for significant changes and reset approval status
    if instance.pk:  # Only for existing venues
        try:
            original = Venue.objects.get(pk=instance.pk)

            # Define significant fields that require re-approval
            significant_fields = [
                'venue_name', 'short_description', 'phone', 'email', 'website_url',
                'street_number', 'street_name', 'city', 'county', 'state'
            ]

            # Check if any significant field has changed
            has_significant_change = False
            for field in significant_fields:
                original_value = getattr(original, field, '')
                new_value = getattr(instance, field, '')
                if original_value != new_value:
                    has_significant_change = True
                    break

            # Reset approval status if significant changes detected and venue was approved
            if has_significant_change and original.approval_status == 'approved':
                instance.approval_status = 'pending'
                instance.approved_at = None

                # Add to status log
                if not hasattr(instance, '_skip_status_log'):
                    instance.status_log.append({
                        'status': 'pending',
                        'timestamp': timezone.now().isoformat(),
                        'reason': 'Significant changes detected - requires re-approval',
                        'by': 'system'
                    })

        except Venue.DoesNotExist:
            pass


@receiver(pre_save, sender=FlaggedVenue)
def flagged_venue_pre_save(sender, instance, **kwargs):
    """Handle flagged venue pre-save operations."""
    # Set reviewed_at timestamp when status changes to reviewed or resolved
    if instance.status in ['reviewed', 'resolved'] and not instance.reviewed_at:
        instance.reviewed_at = timezone.now()
    elif instance.status == 'pending':
        instance.reviewed_at = None


@receiver(post_delete, sender=VenueImage)
def delete_venue_image_files(sender, instance, **kwargs):
    """Remove image files when a VenueImage is deleted."""
    try:
        from utils.image_service import ImageService
        if instance.image:
            ImageService.delete_image(instance.image.name)
    except Exception:
        pass


@receiver(post_delete, sender=Venue)
def delete_venue_main_image(sender, instance, **kwargs):
    """Remove main image file when a Venue is deleted."""
    try:
        from utils.image_service import ImageService
        if instance.main_image:
            ImageService.delete_image(instance.main_image.name)
    except Exception:
        pass