import bleach

ALLOWED_TAGS = [
    'b', 'i', 'u', 'em', 'strong', 'br', 'ul', 'ol', 'li', 'p'
]

# Tags that should be completely removed (including their content)
DANGEROUS_TAGS = ['script', 'style', 'iframe', 'object', 'embed']


def sanitize_html(value: str) -> str:
    """Sanitize user provided HTML input."""
    if not value:
        return value

    # First, remove dangerous tags and their content completely
    for tag in DANGEROUS_TAGS:
        import re
        pattern = f'<{tag}[^>]*>.*?</{tag}>'
        value = re.sub(pattern, '', value, flags=re.IGNORECASE | re.DOTALL)
        # Also remove self-closing dangerous tags
        pattern = f'<{tag}[^>]*/?>'
        value = re.sub(pattern, '', value, flags=re.IGNORECASE)

    # Then clean with bleach to handle remaining tags
    return bleach.clean(value, tags=ALLOWED_TAGS, strip=True)
