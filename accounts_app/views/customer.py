# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import (
    PasswordResetCompleteView,
    PasswordResetConfirmView,
    PasswordResetDoneView,
    PasswordResetView,
)
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, DetailView, UpdateView


# --- Local App Imports ---
from ..forms import (
    AccountDeactivationForm,
    CustomerLoginForm,
    CustomerPasswordChangeForm,
    CustomerProfileForm,
    CustomerSignupForm,
)
from ..logging_utils import (
    log_account_lifecycle_event,
    log_authentication_event,
    log_error,
    log_user_activity,
    performance_monitor,
)
from ..models import CustomUser, CustomerProfile
from .common import MESSAGES, logger, record_login_attempt
from ..logging_utils import log_error, log_profile_change


# --- Customer Authentication and Account Management ---

class CustomerSignupView(CreateView):
    """Customer registration with automatic login and profile creation"""
    model = CustomUser
    form_class = CustomerSignupForm
    template_name = 'accounts_app/customer/signup.html'
    success_url = reverse_lazy('accounts_app:customer_profile')

    def dispatch(self, request, *args, **kwargs):
        """Redirect authenticated users to appropriate destinations"""
        if request.user.is_authenticated:
            return redirect('accounts_app:customer_profile' if request.user.is_customer 
                          else 'home')
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """Get context data, handling the case where object doesn't exist"""
        if 'object' not in kwargs:
            kwargs['object'] = None
        return super().get_context_data(**kwargs)

    @performance_monitor("customer_signup")
    def form_valid(self, form):
        """Process valid signup form submissions"""
        try:
            with transaction.atomic():
                user = self._create_user_account(form)
                self._create_customer_profile(user)
                self._authenticate_user(user)
                self._log_successful_signup(user)
                self._send_success_message(user)
        except Exception as error:
            self._handle_signup_error(error)
            return self.form_invalid(form)
            
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        self.object = None
        context = self.get_context_data(form=form)
        print('DEBUG CONTEXT:', context)
        return self.render_to_response(context)

    def _create_user_account(self, form):
        """Create and save the user account"""
        user = form.save(commit=False)
        user.role = CustomUser.CUSTOMER
        user.save()
        self.object = user  # Set the view's object for potential later use
        return user

    def _create_customer_profile(self, user):
        """Initialize customer profile"""
        CustomerProfile.objects.create(user=user)

    def _authenticate_user(self, user):
        """Log in the user and record authentication"""
        login(self.request, user)
        record_login_attempt(user, self.request, success=True)

    def _log_successful_signup(self, user):
        """Record account creation events in logs"""
        log_account_lifecycle_event(
            event_type='creation',
            user=user,
            request=self.request,
            reason='customer_signup'
        )
        log_user_activity(
            activity_type='account_signup',
            user=user,
            request=self.request,
            details={'signup_method': 'web_form'}
        )
        logger.info(
            "New customer account: %s", user.email,
            extra={
                'user_id': user.id,
                'user_email': user.email,
                'signup_method': 'web_form'
            }
        )

    def _send_success_message(self, user):
        """Display appropriate success message based on email backend"""
        if settings.EMAIL_BACKEND == 'django.core.mail.backends.console.EmailBackend':
            messages.success(self.request, MESSAGES['signup_success_console'])
        else:
            messages.success(
                self.request,
                MESSAGES['signup_success_email'] % {'email': user.email}
            )

    def _handle_signup_error(self, error):
        """Handle signup errors consistently"""
        log_error(
            error_type='account_creation',
            error_message="Customer account creation failed",
            request=self.request,
            exception=error,
            details={'user_type': 'customer'}
        )
        messages.error(self.request, MESSAGES['signup_error'])



@require_http_methods(["GET", "POST"])
@performance_monitor("customer_login")
def customer_login_view(request):
    """
    Handle customer authentication with comprehensive tracking.
    
    Features:
    - Redirects authenticated users
    - Validates credentials
    - Tracks successful/failed attempts
    - Manages session creation
    """
    # Redirect authenticated users
    if request.user.is_authenticated:
        if request.user.is_customer:
            return redirect('accounts_app:customer_profile')
        return redirect('home')
    
    # Process login attempts
    if request.method == 'POST':
        form = CustomerLoginForm(request=request, data=request.POST)
        
        if form.is_valid():
            user = form.get_user()
            email = form.cleaned_data['email']
            
            # Successful authentication
            login(request, user)
            record_login_attempt(user, request, success=True)
            log_user_activity(
                activity_type='login',
                user=user,
                request=request,
                details={'user_type': 'customer', 'login_method': 'web_form'}
            )
            messages.success(request, MESSAGES['login_success'])
            
            # Redirect handling
            next_page = request.GET.get('next')
            return redirect(next_page) if next_page else redirect('accounts_app:customer_profile')
        
        else:
            # Failed authentication
            email = form.cleaned_data.get('email')
            if email:
                try:
                    user = CustomUser.objects.get(email=email)
                    record_login_attempt(user, request, success=False)
                except CustomUser.DoesNotExist:
                    log_authentication_event(
                        event_type='login_failed',
                        user_email=email,
                        success=False,
                        request=request,
                        failure_reason='user_not_found'
                    )

            # The form's clean() method already adds appropriate error messages
            # No need to add additional error messages here

            # Log the failed login attempt for security monitoring
            logger.warning(
                f"Failed customer login attempt for email: {email or 'unknown'}",
                extra={
                    'user_email': email,
                    'ip_address': request.META.get('REMOTE_ADDR'),
                    'user_agent': request.META.get('HTTP_USER_AGENT'),
                    'event_type': 'login_failed'
                }
            )
    
    # Initial GET request
    else:
        form = CustomerLoginForm()
    
    return render(request, 'accounts_app/customer/login.html', {'form': form})


@login_required
def customer_logout_view(request):
    """Handle customer logout with activity logging."""
    if request.user.is_customer:
        # Audit logging
        log_user_activity(
            activity_type='logout',
            user=request.user,
            request=request,
            details={'user_type': 'customer'}
        )
        log_authentication_event(
            event_type='logout',
            user_email=request.user.email,
            success=True,
            request=request
        )
        
        # Session termination
        logout(request)
        messages.success(request, MESSAGES['logout_success'])
    
    return redirect('home')


@login_required
def unified_logout_view(request):
    """
    Unified logout handler for all user types.
    
    Features:
    - Type-specific logging
    - Session termination
    - Post-logout redirection
    """
    if request.user.is_authenticated:
        user_type = None
        user_email = request.user.email
        
        # Customer-specific logging
        if request.user.is_customer:
            user_type = 'customer'
            log_user_activity(
                activity_type='logout',
                user=request.user,
                request=request,
                details={'user_type': 'customer'}
            )
            log_authentication_event(
                event_type='logout',
                user_email=user_email,
                success=True,
                request=request
            )
        
        # Provider-specific logging
        elif request.user.is_service_provider:
            user_type = 'service_provider'
            logger.info(
                f"Service provider logged out: {user_email}",
                extra={
                    'user_email': user_email,
                    'ip_address': request.META.get('REMOTE_ADDR'),
                    'event_type': 'logout'
                }
            )
        
        # Terminate session
        logout(request)
        messages.success(request, MESSAGES['logout_success'])
        
        # Audit log
        if user_type:
            logger.info(
                f"User logged out: {user_email} (Type: {user_type})",
                extra={
                    'user_email': user_email,
                    'user_type': user_type,
                    'event': 'logout_success'
                }
            )
    
    return redirect('home')






# --- Customer Profile Management ---

class CustomerProfileView(DetailView):
    """
    Display authenticated customer's profile details.
    
    Features:
    - Automatic profile creation for new users
    - Role-based access control
    - Context injection for template rendering
    """
    model = CustomerProfile
    template_name = 'accounts_app/customer/profile.html'
    context_object_name = 'profile'

    def dispatch(self, request, *args, **kwargs):
        """Enforce authentication and customer role requirements."""
        if not request.user.is_authenticated or not request.user.is_customer:
            return redirect('accounts_app:customer_login')
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        """Retrieve or create customer profile with fail-safe."""
        try:
            return CustomerProfile.objects.get(user=self.request.user)
        except CustomerProfile.DoesNotExist:
            return CustomerProfile.objects.create(user=self.request.user)

class CustomerProfileEditView(UpdateView):
    """
    Handle customer profile updates with validation.
    
    Features:
    - Automatic profile creation for new users
    - Form validation with error handling
    - Success messaging and activity logging
    """
    model = CustomerProfile
    form_class = CustomerProfileForm
    template_name = 'accounts_app/customer/profile_edit.html'
    success_url = reverse_lazy('accounts_app:customer_profile')

    def dispatch(self, request, *args, **kwargs):
        """Enforce authentication and customer role requirements."""
        if not request.user.is_authenticated or not request.user.is_customer:
            return redirect('accounts_app:customer_login')
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        """Retrieve or create customer profile with fail-safe."""
        try:
            return CustomerProfile.objects.get(user=self.request.user)
        except CustomerProfile.DoesNotExist:
            return CustomerProfile.objects.create(user=self.request.user)

    def post(self, request, *args, **kwargs):
        """Handle POST requests with special logic for profile picture only updates."""
        self.object = self.get_object()

        # Check if this is a profile picture only update
        # Profile picture only updates have only csrf token in POST and profile_picture in FILES
        post_keys = set(request.POST.keys())
        file_keys = set(request.FILES.keys())

        is_profile_picture_only = (
            post_keys == {'csrfmiddlewaretoken'} and
            file_keys == {'profile_picture'}
        )



        if is_profile_picture_only:
            return self._handle_profile_picture_only_update(request)
        else:
            # Handle normal form submission
            return super().post(request, *args, **kwargs)

    def _handle_profile_picture_only_update(self, request):
        """Handle profile picture only updates without affecting other fields."""
        try:
            profile = self.object
            profile_picture = request.FILES.get('profile_picture')

            if profile_picture:
                # Validate the image using the form's clean method
                form = self.get_form_class()(instance=profile)
                form.files = request.FILES

                # Only validate the profile_picture field
                try:
                    cleaned_picture = form.fields['profile_picture'].clean(profile_picture)
                    # Use the form's clean_profile_picture method if it exists
                    if hasattr(form, 'clean_profile_picture'):
                        form.cleaned_data = {'profile_picture': cleaned_picture}
                        cleaned_picture = form.clean_profile_picture()
                except ValidationError as e:
                    error_message = str(e) if hasattr(e, 'message') else str(e)
                    messages.error(request, f"Invalid image: {error_message}")
                    return redirect(self.success_url)

                # Update only the profile picture
                profile.profile_picture = cleaned_picture
                profile.save(update_fields=['profile_picture'])

                # Log the change
                log_profile_change(
                    user=request.user,
                    profile_type='customer',
                    changed_fields={'profile_picture': cleaned_picture},
                    request=request
                )

                messages.success(request, "Profile picture updated successfully!")
                logger.info(
                    f"Customer profile picture updated: {request.user.email}",
                    extra={
                        'user_id': request.user.id,
                        'changed_fields': ['profile_picture']
                    }
                )
            else:
                messages.error(request, "No image file provided.")

        except Exception as error:
            log_error(
                error_type='profile_picture_update',
                error_message="Profile picture update failed",
                user=request.user,
                request=request,
                exception=error
            )
            messages.error(request, 'An error occurred while updating your profile picture.')

        return redirect(self.success_url)

    def form_valid(self, form):
        """Process valid form submissions with activity logging and partial update support."""
        try:
            # Get the current profile instance
            profile = self.object

            # Only update fields that were actually submitted in the form
            # This prevents clearing fields that weren't included in the form submission
            submitted_fields = set(self.request.POST.keys()) | set(self.request.FILES.keys())
            submitted_fields.discard('csrfmiddlewaretoken')  # Remove CSRF token

            # Identify changed fields for audit logging
            changed_fields = {}
            if form.changed_data:
                for field in form.changed_data:
                    changed_fields[field] = form.cleaned_data[field]

            # Save the form but don't commit to database yet
            updated_profile = form.save(commit=False)

            # Only update fields that were actually submitted
            form_fields = set(form.fields.keys())
            fields_to_update = []

            for field_name in form_fields:
                # Check if field was submitted (either in POST data or FILES)
                field_submitted = (
                    field_name in self.request.POST or
                    field_name in self.request.FILES or
                    # Special handling for checkboxes and selects that might not appear in POST when empty
                    (field_name in ['gender', 'birth_month', 'birth_year'] and field_name in submitted_fields)
                )

                if field_submitted:
                    # Update this field
                    new_value = getattr(updated_profile, field_name)
                    setattr(profile, field_name, new_value)
                    fields_to_update.append(field_name)

            # Save only the updated fields
            if fields_to_update:
                profile.save(update_fields=fields_to_update)

            # Log profile changes
            if changed_fields:
                log_profile_change(
                    user=self.request.user,
                    profile_type='customer',
                    changed_fields=changed_fields,
                    request=self.request
                )

            # User feedback
            messages.success(self.request, MESSAGES['profile_update'])
            logger.info(
                f"Customer profile updated: {self.request.user.email}",
                extra={
                    'user_id': self.request.user.id,
                    'changed_fields': list(changed_fields.keys()),
                    'updated_fields': fields_to_update
                }
            )

            return HttpResponseRedirect(self.get_success_url())

        except Exception as error:
            log_error(
                error_type='profile_update',
                error_message="Customer profile update failed",
                user=self.request.user,
                request=self.request,
                exception=error
            )
            messages.error(
                self.request,
                'An error occurred while updating your profile'
            )
            return self.form_invalid(form)




# --- Customer Security Features ---

@login_required
@require_http_methods(["GET", "POST"])
@performance_monitor("customer_password_change")
def customer_change_password_view(request):
    """
    Handle customer password changes with security best practices.
    
    Security Features:
    - Requires current password validation
    - Automatic session termination after change
    - Comprehensive audit logging
    - Error handling with detailed diagnostics
    """
    # Verify customer role
    if not request.user.is_customer:
        return redirect('home')
    
    # Process password change request
    if request.method == 'POST':
        form = CustomerPasswordChangeForm(user=request.user, data=request.POST)
        
        if form.is_valid():
            try:
                user = request.user
                user_email = user.email
                
                # Update password
                form.save()
                
                # Terminate session
                logout(request)
                
                # Security logging
                log_user_activity(
                    activity_type='password_change',
                    user=user,
                    request=request,
                    details={
                        'user_type': 'customer',
                        'ip_address': request.META.get('REMOTE_ADDR')
                    }
                )
                log_authentication_event(
                    event_type='password_change',
                    user_email=user_email,
                    success=True,
                    request=request
                )
                
                # User feedback
                messages.success(request, MESSAGES['password_change'])
                logger.info(
                    f"Customer password changed: {user_email}",
                    extra={
                        'user_id': user.id,
                        'event': 'password_change_success'
                    }
                )
                
                return redirect('home')
            
            except Exception as error:
                # Error handling
                log_error(
                    error_type='password_change',
                    error_message="Password change failed",
                    user=request.user,
                    request=request,
                    exception=error,
                    details={
                        'user_type': 'customer',
                        'form_errors': form.errors
                    }
                )
                messages.error(request, MESSAGES['password_change_error'])
    
    # Initial GET request
    else:
        form = CustomerPasswordChangeForm(user=request.user)
    
    return render(request, 'accounts_app/customer/change_password.html', {'form': form})

@login_required
@require_http_methods(["GET", "POST"])
@performance_monitor("customer_account_deactivation")
def customer_deactivate_account_view(request):
    """
    Handle customer account deactivation with security safeguards.
    
    Security Features:
    - Email confirmation requirement
    - Automatic session termination
    - Audit trail with reason tracking
    - Error handling with rollback safety
    """
    # Verify customer role
    if not request.user.is_customer:
        return redirect('home')
    
    # Process deactivation request
    if request.method == 'POST':
        form = AccountDeactivationForm(user=request.user, data=request.POST)
        
        if form.is_valid():
            try:
                user = request.user
                user_email = user.email
                
                # Deactivate account
                user.is_active = False
                user.save()
                
                # Security logging
                log_account_lifecycle_event(
                    event_type='deactivation',
                    user=user,
                    request=request,
                    reason='user_requested',
                    additional_data={
                        'ip_address': request.META.get('REMOTE_ADDR'),
                        'confirmation_email': form.cleaned_data['confirm_email']
                    }
                )
                
                # Terminate session
                logout(request)
                
                # User feedback
                messages.success(request, MESSAGES['account_deactivated'])
                logger.info(
                    f"Customer account deactivated: {user_email}",
                    extra={
                        'user_id': user.id,
                        'event': 'account_deactivation_success'
                    }
                )
                
                return redirect('home')
            
            except Exception as error:
                # Error handling and rollback
                log_error(
                    error_type='account_deactivation',
                    error_message="Account deactivation failed",
                    user=request.user,
                    request=request,
                    exception=error,
                    details={
                        'user_type': 'customer',
                        'form_data': form.cleaned_data
                    }
                )
                messages.error(request, MESSAGES['deactivation_error'])
                return redirect('accounts_app:customer_profile')
    
    # Initial GET request
    else:
        form = AccountDeactivationForm(user=request.user)
    
    return render(request, 'accounts_app/customer/deactivate_account.html', {'form': form})



# --- Customer Password Reset Views ---

class CustomerPasswordResetView(PasswordResetView):
    """
    Initiate password reset flow for customers.
    
    Features:
    - Pre-populates email from query parameters
    - Stores reset email in session for confirmation
    - Customized form styling
    """
    template_name = 'accounts_app/customer/password_reset.html'
    email_template_name = 'accounts_app/customer/password_reset_email.html'
    subject_template_name = 'accounts_app/customer/password_reset_subject.txt'
    success_url = reverse_lazy('accounts_app:customer_password_reset_done')

    def get_form(self, form_class=None):
        """Apply consistent styling to form fields."""
        form = super().get_form(form_class)
        form.fields['email'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': _('Enter your registered email address'),
            'aria-label': _('Email address for password reset')
        })
        return form

    def get_initial(self):
        """Pre-populate email from query parameters."""
        initial = super().get_initial()
        if email := self.request.GET.get('email'):
            initial['email'] = email
        return initial

    def form_valid(self, form):
        """Store email in session for confirmation display."""
        email = form.cleaned_data['email']
        self.request.session['password_reset_email'] = email
        
        # Log reset initiation
        logger.info(
            f"Password reset initiated for: {email}",
            extra={
                'event': 'password_reset_initiated',
                'user_email': email,
                'ip_address': self.request.META.get('REMOTE_ADDR')
            }
        )
        
        return super().form_valid(form)


class CustomerPasswordResetDoneView(PasswordResetDoneView):
    """
    Display confirmation of password reset email sent.
    
    Features:
    - Retrieves email from session for personalized message
    - Clears session data after retrieval
    """
    template_name = 'accounts_app/customer/password_reset_done.html'

    def get_context_data(self, **kwargs):
        """Inject reset email into template context."""
        context = super().get_context_data(**kwargs)
        context['reset_email'] = self.request.session.pop(
            'password_reset_email', 
            _('your email address')
        )
        return context


class CustomerPasswordResetConfirmView(PasswordResetConfirmView):
    """
    Handle password reset confirmation and validation.
    
    Features:
    - Token validation with expiration
    - Consistent form styling
    - Security best practices
    """
    template_name = 'accounts_app/customer/password_reset_confirm.html'
    success_url = reverse_lazy('accounts_app:customer_password_reset_complete')

    def get_form(self, form_class=None):
        """Apply consistent styling to all form fields."""
        form = super().get_form(form_class)
        for field in form.fields.values():
            field.widget.attrs.update({
                'class': 'form-control',
                'autocomplete': 'new-password'
            })
        return form

    def form_valid(self, form):
        """Log successful password reset."""
        response = super().form_valid(form)
        user = self.user
        
        # Security logging
        logger.info(
            f"Password reset completed for: {user.email}",
            extra={
                'event': 'password_reset_completed',
                'user_id': user.id,
                'ip_address': self.request.META.get('REMOTE_ADDR')
            }
        )
        log_user_activity(
            activity_type='password_reset',
            user=user,
            request=self.request,
            details={'user_type': 'customer'}
        )
        
        return response


class CustomerPasswordResetCompleteView(PasswordResetCompleteView):
    """
    Display confirmation of successful password reset.
    
    Features:
    - Clear success message
    - Login redirection guidance
    """
    template_name = 'accounts_app/customer/password_reset_complete.html'
    
    def get_context_data(self, **kwargs):
        """Add login URL to context for easy redirection."""
        context = super().get_context_data(**kwargs)
        context['login_url'] = reverse_lazy('accounts_app:customer_login')
        return context