# CozyWish Manual Testing Checklist

## 🎯 Testing Overview

This comprehensive manual testing checklist covers all features, screens, and workflows of the CozyWish spa and wellness marketplace application. The checklist is organized by app/module and user type to ensure complete coverage before launch.

### 📋 Quick Testing Summary
- [ ] **Accounts & Authentication Complete** (~45 minutes)
- [ ] **Venue Management Complete** (~30 minutes)
- [ ] **Booking & Cart Complete** (~40 minutes)
- [ ] **Payment Processing Complete** (~25 minutes)
- [ ] **Reviews & Ratings Complete** (~30 minutes)
- [ ] **Notifications Complete** (~20 minutes)
- [ ] **Discounts Complete** (~35 minutes)
- [ ] **Dashboard Features Complete** (~30 minutes)
- [ ] **Admin Features Complete** (~45 minutes)
- [ ] **Security & Edge Cases Complete** (~20 minutes)

**Total Estimated Testing Time: ~5-6 hours**

---

## 1. Accounts & Authentication

### Customer Registration & Login
- [ ] Customer can register with valid email and password
- [ ] Customer receives welcome email after registration
- [ ] Customer cannot register with invalid email format
- [ ] Customer cannot register with duplicate email
- [ ] Customer cannot register with weak password
- [ ] Customer can log in with valid credentials
- [ ] Customer cannot log in with invalid credentials
- [ ] Customer can log out successfully
- [ ] Customer session expires appropriately

### Service Provider Registration & Login
- [ ] Provider can register with business information
- [ ] Provider receives email verification after registration
- [ ] Provider cannot access features before email verification
- [ ] Provider can verify email through verification link
- [ ] Provider can log in after email verification
- [ ] Provider cannot log in before email verification
- [ ] Provider can access provider-specific features after login

### Password Management
- [ ] Customer can request password reset
- [ ] Customer receives password reset email
- [ ] Customer can reset password using reset link
- [ ] Password reset link expires after use
- [ ] Customer can change password from profile
- [ ] Old password required for password change
- [ ] New password must meet security requirements

### Profile Management
- [ ] Customer can view and edit profile information
- [ ] Customer can upload and update profile image
- [ ] Customer profile image displays correctly
- [ ] Provider can view and edit business profile
- [ ] Provider can upload and update business logo
- [ ] Provider can update business contact information
- [ ] Profile changes save correctly
- [ ] Invalid profile data shows appropriate errors

### Team Management (Service Providers)
- [ ] Provider can add team members
- [ ] Provider can edit team member information
- [ ] Provider can upload team member photos
- [ ] Provider can set team member roles/specialties
- [ ] Provider can remove team members
- [ ] Team members display on venue page
- [ ] Team member information validates correctly

### Security Features
- [ ] Multiple failed login attempts trigger security alerts
- [ ] Login history is recorded and viewable
- [ ] Suspicious login activity generates alerts
- [ ] Account deactivation requires email confirmation
- [ ] Deactivated accounts cannot log in
- [ ] Cross-role access is properly restricted

---

## 2. Venue Management

### Venue Creation (Service Providers)
- [ ] Provider can create new venue with required information
- [ ] Venue requires business name, address, and contact details
- [ ] Venue address validation works correctly
- [ ] Provider can select venue categories
- [ ] Provider can add venue description
- [ ] Provider can set operating hours
- [ ] Venue saves in "pending" approval status
- [ ] Provider cannot create multiple venues

### Venue Images & Media
- [ ] Provider can upload venue images
- [ ] Image upload validates file types (JPG/PNG only)
- [ ] Image upload validates file size limits
- [ ] Provider can set primary venue image
- [ ] Provider can reorder venue images
- [ ] Provider can delete venue images
- [ ] Images display correctly on venue page
- [ ] Image optimization works properly

### Venue Information Management
- [ ] Provider can add and edit venue FAQs
- [ ] Provider can update venue description
- [ ] Provider can modify contact information
- [ ] Provider can update operating hours
- [ ] Provider can add venue amenities/features
- [ ] Changes require admin re-approval if significant
- [ ] Venue information displays correctly to customers

### Venue Approval Workflow
- [ ] New venues appear in admin approval queue
- [ ] Admin can view venue details for approval
- [ ] Admin can approve venues with notes
- [ ] Admin can reject venues with reasons
- [ ] Provider receives notification of approval/rejection
- [ ] Approved venues become visible to customers
- [ ] Rejected venues remain hidden from customers

---

## 3. Service Management

### Service Creation (Service Providers)
- [ ] Provider can add services to their venue
- [ ] Service requires name, description, and pricing
- [ ] Provider can set service duration
- [ ] Provider can set minimum and maximum prices
- [ ] Provider can categorize services
- [ ] Provider can set service availability
- [ ] Service images can be uploaded
- [ ] Services display on venue page

### Service Pricing & Availability
- [ ] Provider can set different price ranges
- [ ] Provider can set service capacity limits
- [ ] Provider can configure available time slots
- [ ] Provider can set service-specific operating hours
- [ ] Provider can temporarily disable services
- [ ] Pricing displays correctly to customers
- [ ] Availability updates in real-time

### Service Categories
- [ ] Admin can create service categories
- [ ] Categories display in service creation form
- [ ] Customers can filter services by category
- [ ] Category hierarchy works correctly
- [ ] Category images display properly
- [ ] Inactive categories are hidden appropriately

---

## 4. Booking & Cart Management

### Shopping Cart (Customers)
- [ ] Customer can add services to cart
- [ ] Cart displays service details and pricing
- [ ] Customer can select date and time for services
- [ ] Customer can modify quantity in cart
- [ ] Customer can remove items from cart
- [ ] Cart calculates total price correctly
- [ ] Cart expires after 24 hours
- [ ] Cart persists across browser sessions

### Booking Creation
- [ ] Customer can proceed from cart to booking
- [ ] Booking requires customer contact information
- [ ] Customer can add special requests/notes
- [ ] Booking prevents double-booking time slots
- [ ] Booking validates service availability
- [ ] Booking generates unique booking ID
- [ ] Customer receives booking confirmation
- [ ] Provider receives new booking notification

### Booking Management (Customers)
- [ ] Customer can view booking history
- [ ] Customer can view booking details
- [ ] Customer can cancel bookings (within 6-hour window)
- [ ] Customer cannot cancel bookings after deadline
- [ ] Cancelled bookings update status correctly
- [ ] Customer receives cancellation confirmation
- [ ] Refund process initiates for cancelled bookings

### Booking Management (Providers)
- [ ] Provider can view incoming booking requests
- [ ] Provider can accept booking requests
- [ ] Provider can decline booking requests with reason
- [ ] Provider can view today's confirmed bookings
- [ ] Provider can mark bookings as completed
- [ ] Provider can view booking history and analytics
- [ ] Provider receives notifications for booking changes

---

## 5. Payment Processing

### Payment Flow (Customers)
- [ ] Customer can proceed to payment from confirmed booking
- [ ] Payment form displays booking summary and total
- [ ] Customer can enter payment card information
- [ ] Payment form validates card details
- [ ] Payment processes successfully with valid card
- [ ] Payment fails appropriately with invalid card
- [ ] Customer receives payment confirmation
- [ ] Payment receipt is generated and emailed

### Payment History & Management
- [ ] Customer can view payment history
- [ ] Customer can view payment details and receipts
- [ ] Customer can download payment receipts
- [ ] Payment status updates correctly
- [ ] Failed payments are recorded appropriately
- [ ] Payment methods are stored securely (if applicable)

### Refund Processing
- [ ] Customer can request refunds for eligible bookings
- [ ] Refund request includes reason and details
- [ ] Provider receives refund request notification
- [ ] Admin can review and approve refund requests
- [ ] Approved refunds process correctly
- [ ] Customer receives refund confirmation
- [ ] Refund amount reflects in customer account

### Provider Earnings (Service Providers)
- [ ] Provider can view earnings dashboard
- [ ] Earnings display by date range
- [ ] Provider can view individual payment details
- [ ] Provider can see platform fee deductions
- [ ] Provider can view payout history
- [ ] Earnings reports can be exported
- [ ] Tax information is available if applicable

---

## 6. Reviews & Ratings

### Review Submission (Customers)
- [ ] Customer can submit reviews for completed bookings
- [ ] Review requires star rating (1-5 stars)
- [ ] Review allows written feedback (optional)
- [ ] Customer cannot review same venue multiple times
- [ ] Customer cannot review before booking completion
- [ ] Review submission shows confirmation message
- [ ] Provider receives new review notification

### Review Management (Customers)
- [ ] Customer can view their review history
- [ ] Customer can edit their reviews
- [ ] Customer can delete their reviews
- [ ] Review edits update correctly
- [ ] Review deletion removes from venue page
- [ ] Customer can flag inappropriate reviews

### Review Responses (Service Providers)
- [ ] Provider can view reviews for their venue
- [ ] Provider can respond to customer reviews
- [ ] Provider responses display under reviews
- [ ] Provider can edit their responses
- [ ] Provider can view review analytics and summaries
- [ ] Provider receives notifications for new reviews

### Review Moderation (Admin)
- [ ] Admin can view all reviews in moderation interface
- [ ] Admin can approve or reject reviews
- [ ] Admin can edit inappropriate review content
- [ ] Admin can remove reviews with reasons
- [ ] Admin can resolve flagged reviews
- [ ] Admin can view review analytics across platform
- [ ] Moderation actions are logged appropriately

---

## 7. Notifications

### Email Notifications
- [ ] Welcome emails sent after registration
- [ ] Email verification sent to new providers
- [ ] Booking confirmation emails sent to customers
- [ ] Booking notification emails sent to providers
- [ ] Payment confirmation emails sent to customers
- [ ] Review notification emails sent to providers
- [ ] Password reset emails sent when requested
- [ ] Email templates display correctly with proper formatting

### In-App Notifications
- [ ] Customers receive booking status notifications
- [ ] Providers receive new booking notifications
- [ ] Users receive review-related notifications
- [ ] Payment status notifications appear correctly
- [ ] Notification count badge updates in navigation
- [ ] Users can mark notifications as read
- [ ] Users can view notification history
- [ ] Notifications link to relevant pages when clicked

### Notification Preferences
- [ ] Users can access notification settings
- [ ] Users can enable/disable email notifications
- [ ] Users can enable/disable in-app notifications
- [ ] Notification preferences save correctly
- [ ] Disabled notifications are not sent
- [ ] Users can update preferences at any time

---

## 8. Discount Management

### Customer Discount Discovery
- [ ] Customers can view available discounts
- [ ] Discounts display on relevant venue/service pages
- [ ] Discount details show clearly (percentage/amount off)
- [ ] Discount expiration dates are visible
- [ ] Customers can apply discounts during booking
- [ ] Discount calculations are accurate
- [ ] Expired discounts are not available
- [ ] Usage limits are enforced correctly

### Provider Discount Creation
- [ ] Providers can create venue-wide discounts
- [ ] Providers can create service-specific discounts
- [ ] Discount creation form validates input correctly
- [ ] Providers can set discount start and end dates
- [ ] Providers can set usage limits
- [ ] Providers can set minimum booking requirements
- [ ] Created discounts require admin approval
- [ ] Providers receive approval/rejection notifications

### Platform Discounts (Admin)
- [ ] Admin can create platform-wide discounts
- [ ] Admin can target discounts by category
- [ ] Admin can set featured discounts for homepage
- [ ] Platform discounts can override venue discounts
- [ ] Admin can monitor discount usage analytics
- [ ] Admin can deactivate discounts early if needed

### Discount Analytics
- [ ] Providers can view discount usage statistics
- [ ] Admin can view platform-wide discount analytics
- [ ] Discount ROI and effectiveness metrics are available
- [ ] Usage reports can be exported
- [ ] Analytics update in real-time

---

## 9. Dashboard Features

### Customer Dashboard
- [ ] Customer can view upcoming bookings
- [ ] Customer can see booking status updates
- [ ] Customer can access quick profile editing
- [ ] Customer can view favorite venues
- [ ] Customer can add/remove venue favorites
- [ ] Customer can see recent payment history
- [ ] Dashboard displays personalized recommendations
- [ ] Quick actions work correctly (book again, review, etc.)

### Provider Dashboard
- [ ] Provider can view today's bookings
- [ ] Provider can see upcoming bookings calendar
- [ ] Provider can view earnings summary
- [ ] Provider can access booking management tools
- [ ] Provider can see recent reviews
- [ ] Provider can view venue performance metrics
- [ ] Provider can access team management
- [ ] Dashboard analytics update correctly

### Admin Dashboard
- [ ] Admin can view platform statistics
- [ ] Admin can see user registration trends
- [ ] Admin can monitor booking volume
- [ ] Admin can view revenue analytics
- [ ] Admin can see pending approvals (venues, discounts)
- [ ] Admin can access system health monitoring
- [ ] Admin can view recent user activity
- [ ] Dashboard provides actionable insights

---

## 10. Admin Features

### User Management
- [ ] Admin can view all user accounts
- [ ] Admin can search and filter users
- [ ] Admin can view user details and activity
- [ ] Admin can deactivate user accounts
- [ ] Admin can reset user passwords
- [ ] Admin can change user roles
- [ ] Admin can view login history and security alerts
- [ ] Admin actions are logged appropriately

### Content Management
- [ ] Admin can create and edit static pages
- [ ] Admin can manage blog posts and categories
- [ ] Admin can upload and organize media files
- [ ] Admin can edit homepage content blocks
- [ ] Admin can manage site announcements
- [ ] Admin can configure site settings
- [ ] Content changes publish correctly

### System Monitoring
- [ ] Admin can view system health status
- [ ] Admin can monitor application performance
- [ ] Admin can view error logs and reports
- [ ] Admin can track bulk action results
- [ ] Admin can export system reports
- [ ] Admin receives alerts for critical issues

---

## 11. Security & Edge Cases

### Access Control
- [ ] Unauthenticated users cannot access protected pages
- [ ] Customers cannot access provider-only features
- [ ] Providers cannot access admin-only features
- [ ] Users cannot modify other users' data
- [ ] Direct URL access is properly restricted
- [ ] API endpoints enforce proper authentication

### Data Validation
- [ ] Forms validate required fields
- [ ] Email format validation works correctly
- [ ] Phone number format validation works
- [ ] File upload restrictions are enforced
- [ ] SQL injection attempts are blocked
- [ ] XSS attempts are prevented
- [ ] CSRF protection is active

### Error Handling
- [ ] 404 pages display for invalid URLs
- [ ] 500 errors are handled gracefully
- [ ] Network errors show appropriate messages
- [ ] Form submission errors are clear and helpful
- [ ] File upload errors provide specific feedback
- [ ] Database connection errors are handled

### Performance & Usability
- [ ] Pages load within acceptable time limits
- [ ] Images are optimized and load quickly
- [ ] Search functionality returns results promptly
- [ ] Forms submit without significant delay
- [ ] Mobile responsiveness works correctly
- [ ] Browser compatibility is maintained

---

## 🎯 Testing Notes

### Test Data Requirements
- Create test accounts for each user type (customer, provider, admin)
- Prepare test images for venue and profile uploads
- Have test payment card information ready
- Create sample venue and service data

### Common Issues to Watch For
1. **Email Delivery**: Ensure email notifications are sent and formatted correctly
2. **Image Uploads**: Verify file size limits and format restrictions
3. **Date/Time Handling**: Check timezone handling and date validation
4. **Permission Checks**: Ensure users can only access appropriate features
5. **Data Consistency**: Verify related data updates correctly across apps

### Reporting Issues
When reporting issues, include:
- **User Type**: Which role was being tested
- **Feature**: Specific feature or workflow
- **Steps to Reproduce**: Exact steps that caused the issue
- **Expected vs Actual**: What should happen vs what actually happened
- **Environment**: Browser, device, screen size
- **Screenshots**: If applicable

---

**Total Features Tested**: 200+ individual test cases across all modules
**Estimated Testing Time**: 5-6 hours for complete coverage
**Priority**: Focus on authentication, booking flow, and payment processing first

**Happy Testing! 🚀**
