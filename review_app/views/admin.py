# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.shortcuts import get_object_or_404, redirect, render
from django.utils.translation import gettext_lazy as _

from django.conf import settings

# --- Local App Imports ---
from utils.logging_utils import log_error, log_user_activity
from ..logging_utils import (
    log_review_flag_resolution,
    log_review_moderation,
    performance_monitor,
)
from ..models import Review, ReviewFlag
from .common import is_admin


# --- Helper Functions ---


# --- Admin Views ---


@login_required
@performance_monitor('admin_review_moderation')
def admin_review_moderation_view(request):
    """Admin dashboard for review moderation and management."""
    try:
        if not is_admin(request.user):
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Access denied. Only administrators can view this page.'))
        reviews = (
            Review.objects.select_related('customer', 'venue', 'response')
            .prefetch_related('flags')
            .annotate(pending_count=Count('flags', filter=Q(flags__status=ReviewFlag.PENDING)))
            .order_by('-created_at')
        )
        status_filter = request.GET.get('status', 'all')
        if status_filter == 'flagged':
            reviews = reviews.filter(is_flagged=True)
        elif status_filter == 'pending':
            reviews = reviews.filter(is_approved=False)
        elif status_filter == 'approved':
            reviews = reviews.filter(is_approved=True)
        search_query = request.GET.get('search', '').strip()
        if search_query:
            reviews = reviews.filter(
                Q(written_review__icontains=search_query)
                | Q(customer__email__icontains=search_query)
                | Q(venue__venue_name__icontains=search_query)
            )
        paginator = Paginator(reviews, 20)
        page_obj = paginator.get_page(request.GET.get('page'))
        stats = {
            'total_reviews': Review.objects.count(),
            'flagged_reviews': Review.objects.filter(is_flagged=True).count(),
            'pending_reviews': Review.objects.filter(is_approved=False).count(),
            'approved_reviews': Review.objects.filter(is_approved=True).count(),
            'new_venues_count': Review.objects.values('venue').annotate(
                review_count=Count('id')
            ).filter(review_count__lt=5).count(),
        }
        log_user_activity(
            app_name='review_app',
            activity_type='admin_review_moderation_accessed',
            user=request.user,
            request=request,
            details={
                'status_filter': status_filter,
                'search_query': search_query,
                'total_flagged': stats['flagged_reviews'],
                'total_pending': stats['pending_reviews'],
            },
            target_object='admin_review_moderation',
        )
        context = {
            'reviews': page_obj,
            'stats': stats,
            'status_filter': status_filter,
            'search_query': search_query,
            'page_title': _('Review Moderation'),
        }
        return render(request, 'review_app/admin/review_moderation.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='admin_moderation_error',
            error_message='Failed to load admin review moderation',
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, _('Unable to load review moderation. Please try again.'))
        return redirect('admin_app:admin_dashboard')


@login_required
@performance_monitor('admin_moderate_review')
def admin_moderate_review_view(request, review_slug):
    """Admin view for moderating a specific review."""
    try:
        if not is_admin(request.user):
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Access denied. Only administrators can view this page.'))
        review = get_object_or_404(
            Review.objects.select_related('customer', 'venue', 'response').prefetch_related('flags'),
            slug=review_slug,
        )
        if request.method == 'POST':
            action = request.POST.get('action')
            admin_notes = request.POST.get('admin_notes', '')
            old_status = {
                'is_approved': review.is_approved,
                'is_flagged': review.is_flagged,
            }
            if action == 'approve':
                review.is_approved = True
                review.is_flagged = False
                review.save()
                log_review_moderation(
                    admin_user=request.user,
                    review=review,
                    action='approved',
                    request=request,
                    additional_details={
                        'old_status': old_status,
                        'admin_notes': admin_notes,
                        'moderation_method': 'admin_panel',
                    },
                )
                messages.success(request, _('Review approved successfully.'))
            elif action == 'disapprove':
                review.is_approved = False
                review.save()
                log_review_moderation(
                    admin_user=request.user,
                    review=review,
                    action='disapproved',
                    request=request,
                    additional_details={
                        'old_status': old_status,
                        'admin_notes': admin_notes,
                        'moderation_method': 'admin_panel',
                    },
                )
                messages.success(request, _('Review disapproved successfully.'))
            return redirect('review_app:admin_review_moderation')
        context = {
            'review': review,
            'page_title': _('Moderate Review'),
        }
        return render(request, 'review_app/admin/moderate_review.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='admin_moderate_error',
            error_message='Failed to moderate review',
            user=request.user,
            request=request,
            exception=e,
            details={'review_slug': review_slug},
        )
        messages.error(request, _('Unable to moderate review. Please try again.'))
        return redirect('review_app:admin_review_moderation')


@login_required
@performance_monitor('admin_manage_flags')
def admin_manage_flags_view(request):
    """Admin view for managing review flags."""
    try:
        if not is_admin(request.user):
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Access denied. Only administrators can view this page.'))

        # Get all flags with related data
        flags = (
            ReviewFlag.objects.select_related('review', 'review__customer', 'review__venue', 'flagged_by', 'reviewed_by')
            .order_by('-created_at')
        )

        # Apply filters
        status_filter = request.GET.get('status', 'all')
        if status_filter == 'pending':
            flags = flags.filter(status=ReviewFlag.PENDING)
        elif status_filter == 'reviewed':
            flags = flags.filter(status=ReviewFlag.REVIEWED)
        elif status_filter == 'resolved':
            flags = flags.filter(status=ReviewFlag.RESOLVED)

        reason_filter = request.GET.get('reason')
        if reason_filter:
            flags = flags.filter(reason=reason_filter)

        search_query = request.GET.get('search', '').strip()
        if search_query:
            flags = flags.filter(
                Q(review__written_review__icontains=search_query)
                | Q(review__customer__email__icontains=search_query)
                | Q(review__venue__venue_name__icontains=search_query)
                | Q(reason_text__icontains=search_query)
            )

        # Pagination
        paginator = Paginator(flags, 20)
        page_obj = paginator.get_page(request.GET.get('page'))

        # Calculate statistics
        all_flags = ReviewFlag.objects.all()
        flag_stats = {
            'total_flags': all_flags.count(),
            'pending_flags': all_flags.filter(status=ReviewFlag.PENDING).count(),
            'reviewed_flags': all_flags.filter(status=ReviewFlag.REVIEWED).count(),
            'resolved_flags': all_flags.filter(status=ReviewFlag.RESOLVED).count(),
        }

        context = {
            'flags': page_obj,
            'flag_stats': flag_stats,
            'status_filter': status_filter,
            'reason_filter': reason_filter,
            'search_query': search_query,
            'reason_choices': ReviewFlag.REASON_CHOICES,
            'page_title': _('Manage Review Flags'),
        }
        return render(request, 'review_app/admin/manage_flags.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='admin_flags_error',
            error_message='Failed to load flag management',
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, _('Unable to load flag management. Please try again.'))
        return redirect('dashboard_app:admin_dashboard')


@login_required
@performance_monitor('admin_resolve_flag')
def admin_flag_resolution_view(request, flag_id):
    """Admin view for resolving a review flag."""
    try:
        if not is_admin(request.user):
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Access denied. Only administrators can view this page.'))
        flag = get_object_or_404(ReviewFlag, id=flag_id)
        if request.method == 'POST':
            action = request.POST.get('action')
            admin_notes = request.POST.get('admin_notes', '')
            old_status = flag.status
            if action == 'resolve':
                flag.status = ReviewFlag.RESOLVED
                flag.reviewed_by = request.user
                flag.admin_notes = admin_notes
                flag.save()
                log_review_flag_resolution(
                    admin_user=request.user,
                    flag=flag,
                    action='resolved',
                    request=request,
                    additional_details={
                        'old_status': old_status,
                        'resolution_method': 'admin_panel',
                    },
                )
                messages.success(request, _('Flag resolved successfully.'))
            elif action == 'dismiss':
                flag.status = ReviewFlag.REVIEWED
                flag.reviewed_by = request.user
                flag.admin_notes = admin_notes
                flag.save()
                log_review_flag_resolution(
                    admin_user=request.user,
                    flag=flag,
                    action='dismissed',
                    request=request,
                    additional_details={
                        'old_status': old_status,
                        'resolution_method': 'admin_panel',
                    },
                )
                messages.success(request, _('Flag dismissed successfully.'))
            return redirect('review_app:admin_manage_flags')
        context = {
            'flag': flag,
            'review': flag.review,
            'page_title': _('Resolve Flag'),
        }
        return render(request, 'review_app/admin/resolve_flag.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='admin_resolve_flag_error',
            error_message='Failed to resolve flag',
            user=request.user,
            request=request,
            exception=e,
            details={'flag_id': flag_id},
        )
        messages.error(request, _('Unable to resolve flag. Please try again.'))
        return redirect('review_app:admin_manage_flags')
