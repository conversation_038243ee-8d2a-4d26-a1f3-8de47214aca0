# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils.translation import gettext_lazy as _

from django.conf import settings

# --- Local App Imports ---
from utils.logging_utils import log_error, log_security_event, log_user_activity
from ..forms import ReviewFlagForm, ReviewForm
from ..logging_utils import (
    log_review_creation,
    log_review_flag,
    performance_monitor,
)
from ..models import Review, ReviewFlag, ReviewHelpfulness
from venues_app.models import Venue
from .common import (
    is_customer,
    has_completed_booking,
    get_venue_review_stats,
)


# --- Helper Functions ---


# --- Customer Views ---

@performance_monitor('venue_reviews_view')
def venue_reviews_view(request, venue_id):
    """Display all approved reviews for a specific venue."""
    try:
        venue = get_object_or_404(
            Venue,
            id=venue_id,
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )
        sort = request.GET.get('sort', 'newest')
        rating_filter = request.GET.get('rating')
        reviews = (
            Review.objects.filter(venue=venue, is_approved=True)
            .select_related('customer', 'response')
            .prefetch_related('flags')
        )

        # Apply rating filter if provided
        if rating_filter:
            try:
                rating_value = int(rating_filter)
                if 1 <= rating_value <= 5:
                    reviews = reviews.filter(rating=rating_value)
            except (ValueError, TypeError):
                pass  # Ignore invalid rating values
        if sort == 'oldest':
            reviews = reviews.order_by('created_at')
        elif sort == 'rating_asc':
            reviews = reviews.order_by('rating', '-created_at')
        elif sort == 'rating_desc':
            reviews = reviews.order_by('-rating', '-created_at')
        else:
            reviews = reviews.order_by('-created_at')
        paginator = Paginator(reviews, 10)
        page_obj = paginator.get_page(request.GET.get('page'))
        review_stats = get_venue_review_stats(venue)
        can_review = False
        user_review = None
        has_reviewed = False
        if request.user.is_authenticated:
            user_review = Review.objects.filter(customer=request.user, venue=venue).first()
            has_reviewed = bool(user_review)
            can_review = (
                is_customer(request.user)
                and has_completed_booking(request.user, venue)
                and not has_reviewed
            )
            log_user_activity(
                app_name='review_app',
                activity_type='venue_reviews_viewed',
                user=request.user,
                request=request,
                details={
                    'venue_id': venue.id,
                    'venue_name': venue.venue_name,
                    'total_reviews': review_stats['total_reviews'],
                    'average_rating': float(review_stats['average_rating'] or 0),
                },
                target_object=f'venue_{venue.id}_reviews',
            )
        context = {
            'venue': venue,
            'reviews': page_obj,
            'review_stats': review_stats,
            'can_review': can_review,
            'user_review': user_review,
            'has_reviewed': has_reviewed,
            'average_rating': review_stats['average_rating'] or 0,
            'total_reviews': review_stats['total_reviews'],
            'rating_distribution': {
                5: review_stats['five_star_percent'],
                4: review_stats['four_star_percent'],
                3: review_stats['three_star_percent'],
                2: review_stats['two_star_percent'],
                1: review_stats['one_star_percent'],
            },
            'current_sort': sort,
            'is_paginated': page_obj.has_other_pages(),
        }
        return render(request, 'review_app/venue_reviews.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='venue_reviews_view_error',
            error_message='Failed to load venue reviews',
            user=request.user if request.user.is_authenticated else None,
            request=request,
            exception=e,
            details={'venue_id': venue_id},
        )
        messages.error(request, _('Unable to load reviews. Please try again.'))
        # Try to get the venue to redirect to its detail page
        try:
            venue = Venue.objects.get(id=venue_id)
            return redirect('venues_app:venue_detail', venue_slug=venue.slug)
        except Venue.DoesNotExist:
            return redirect('venues_app:venue_list')


@login_required
@performance_monitor('submit_review')
def submit_review_view(request, venue_id):
    """Allow customers to submit a review for a venue after completing a booking."""
    try:
        venue = get_object_or_404(
            Venue,
            id=venue_id,
            approval_status=Venue.APPROVED,
            visibility=Venue.ACTIVE,
        )
        if not is_customer(request.user):
            log_security_event(
                app_name='review_app',
                event_type='unauthorized_review_attempt',
                user_email=request.user.email,
                user_id=request.user.id,
                request=request,
                details={'venue_id': venue_id, 'user_role': request.user.role},
                severity='WARNING',
            )
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Only customers can leave reviews.'))
        if not has_completed_booking(request.user, venue):
            messages.error(request, _('You can only review venues where you have completed a booking.'))
            return redirect('review_app:venue_reviews', venue_id=venue.id)
        existing_review = Review.objects.filter(customer=request.user, venue=venue).first()
        if existing_review:
            messages.info(request, _('You have already reviewed this venue. You can edit your existing review.'))
            return redirect('review_app:edit_review', review_slug=existing_review.slug)
        if request.method == 'POST':
            form = ReviewForm(request.POST)
            if form.is_valid():
                review = form.save(commit=False)
                review.customer = request.user
                review.venue = venue
                review.save()
                log_review_creation(
                    user=request.user,
                    review=review,
                    request=request,
                    additional_details={
                        'submission_method': 'web_form',
                        'has_completed_booking': True,
                    },
                )
                messages.success(request, _('Your review has been posted successfully!'))
                return redirect('review_app:venue_reviews', venue_id=venue.id)
        else:
            form = ReviewForm()
        context = {
            'form': form,
            'venue': venue,
            'page_title': _('Write a Review'),
        }
        return render(request, 'review_app/submit_review.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='review_submission_error',
            error_message='Failed to submit review',
            user=request.user,
            request=request,
            exception=e,
            details={'venue_id': venue_id},
        )
        messages.error(request, _('Unable to submit review. Please try again.'))
        return redirect('review_app:venue_reviews', venue_id=venue_id)


@login_required
@performance_monitor('edit_review')
def edit_review_view(request, review_slug):
    """Allow customers to edit their existing review."""
    try:
        review = get_object_or_404(Review, slug=review_slug)
        if review.customer != request.user:
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('You can only edit your own reviews.'))
        if request.method == 'POST':
            form = ReviewForm(request.POST, instance=review)
            if form.is_valid():
                old_rating = review.rating
                old_review_text = review.written_review
                updated_review = form.save()
                log_user_activity(
                    app_name='review_app',
                    activity_type='review_updated',
                    user=request.user,
                    request=request,
                    details={
                        'review_id': review.id,
                        'venue_id': review.venue.id,
                        'venue_name': review.venue.venue_name,
                        'old_rating': old_rating,
                        'new_rating': updated_review.rating,
                        'content_changed': old_review_text != updated_review.written_review,
                    },
                    target_object=f'review_{review.id}',
                )
                messages.success(request, _('Your review has been updated successfully!'))
                return redirect('review_app:venue_reviews', venue_id=review.venue.id)
        else:
            form = ReviewForm(instance=review)
        context = {
            'form': form,
            'review': review,
            'venue': review.venue,
            'page_title': _('Edit Review'),
        }
        return render(request, 'review_app/edit_review.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='review_edit_error',
            error_message='Failed to edit review',
            user=request.user,
            request=request,
            exception=e,
            details={'review_slug': review_slug},
        )
        messages.error(request, _('Unable to edit review. Please try again.'))
        return redirect('review_app:customer_review_history')


@login_required
@performance_monitor('flag_review')
def flag_review_view(request, review_slug):
    """Allow customers to flag inappropriate reviews."""
    try:
        review = get_object_or_404(Review, slug=review_slug, is_approved=True)
        if not is_customer(request.user):
            log_security_event(
                app_name='review_app',
                event_type='unauthorized_flag_attempt',
                user_email=request.user.email,
                user_id=request.user.id,
                request=request,
                details={'review_slug': review_slug, 'user_role': request.user.role},
                severity='WARNING',
            )
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('Only customers can flag reviews.'))
        if review.customer == request.user:
            from django.http import HttpResponseForbidden
            return HttpResponseForbidden(_('You cannot flag your own review.'))
        existing_flag = ReviewFlag.objects.filter(review=review, flagged_by=request.user).first()
        if existing_flag:
            messages.info(request, _('You have already flagged this review.'))
            return redirect('review_app:venue_reviews', venue_id=review.venue.id)
        if request.method == 'POST':
            form = ReviewFlagForm(request.POST)
            if form.is_valid():
                flag = form.save(commit=False)
                flag.review = review
                flag.flagged_by = request.user
                flag.save()
                log_review_flag(
                    user=request.user,
                    review=review,
                    flag=flag,
                    request=request,
                    additional_details={
                        'flag_method': 'web_form',
                        'review_author': review.customer.email,
                    },
                )
                messages.success(request, _('Review has been flagged for moderation. Thank you for your feedback.'))
                return redirect('review_app:venue_reviews', venue_id=review.venue.id)
        else:
            form = ReviewFlagForm()
        context = {
            'form': form,
            'review': review,
            'venue': review.venue,
            'page_title': _('Flag Review'),
        }
        return render(request, 'review_app/flag_review.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='review_flag_error',
            error_message='Failed to flag review',
            user=request.user,
            request=request,
            exception=e,
            details={'review_slug': review_slug},
        )
        messages.error(request, _('Unable to flag review. Please try again.'))
        return redirect('venues_app:venue_list')


@login_required
@performance_monitor('customer_review_history')
def customer_review_history_view(request):
    """Display all reviews written by the customer."""
    try:
        if not is_customer(request.user):
            messages.error(request, _('Access denied. Only customers can view this page.'))
            return redirect('accounts_app:customer_login')
        reviews = (
            Review.objects.filter(customer=request.user)
            .select_related('venue', 'response')
            .prefetch_related('flags')
            .order_by('-created_at')
        )
        paginator = Paginator(reviews, 10)
        page_obj = paginator.get_page(request.GET.get('page'))
        log_user_activity(
            app_name='review_app',
            activity_type='customer_review_history_viewed',
            user=request.user,
            request=request,
            details={'total_reviews': reviews.count()},
            target_object='customer_review_history',
        )
        context = {
            'reviews': page_obj,
            'page_title': _('My Reviews'),
        }
        return render(request, 'review_app/customer_review_history.html', context)
    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='customer_history_error',
            error_message='Failed to load customer review history',
            user=request.user,
            request=request,
            exception=e,
        )
        messages.error(request, _('Unable to load your review history. Please try again.'))
        return redirect('dashboard_app:customer_dashboard')


@login_required
@performance_monitor('customer_review_detail')
def customer_review_detail_view(request, review_id):
    """Display detailed view of a review for customers (used by notifications)."""
    try:
        review = get_object_or_404(Review, id=review_id)

        # Check if user has permission to view this review
        if not (is_customer(request.user) and review.customer == request.user):
            messages.error(request, _('You do not have permission to view this review.'))
            return redirect('review_app:customer_review_history')

        log_user_activity(
            app_name='review_app',
            activity_type='customer_review_detail_viewed',
            user=request.user,
            request=request,
            details={
                'review_id': review.id,
                'venue_id': review.venue.id,
                'venue_name': review.venue.venue_name,
                'has_response': hasattr(review, 'response'),
            },
            target_object=f'review_{review.id}',
        )

        context = {
            'review': review,
            'venue': review.venue,
            'page_title': _('Review Details'),
        }
        return render(request, 'review_app/customer_review_detail.html', context)

    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='customer_review_detail_error',
            error_message='Failed to load customer review detail',
            user=request.user,
            request=request,
            exception=e,
            details={'review_id': review_id},
        )
        messages.error(request, _('Unable to load review details. Please try again.'))
        return redirect('review_app:customer_review_history')


@login_required
@performance_monitor('vote_review_helpfulness')
def vote_review_helpfulness_view(request, review_slug):
    """Allow customers to vote on review helpfulness."""
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST requests allowed'}, status=405)

    try:
        review = get_object_or_404(Review, slug=review_slug, is_approved=True)

        if not is_customer(request.user):
            return JsonResponse({'error': 'Only customers can vote on helpfulness'}, status=403)

        if review.customer == request.user:
            return JsonResponse({'error': 'You cannot vote on your own review'}, status=403)

        is_helpful = request.POST.get('is_helpful') == 'true'

        # Get or create the vote
        vote, created = ReviewHelpfulness.objects.get_or_create(
            review=review,
            user=request.user,
            defaults={'is_helpful': is_helpful}
        )

        if not created:
            # Update existing vote
            vote.is_helpful = is_helpful
            vote.save()

        # Get updated stats
        stats = review.get_helpfulness_stats()

        log_user_activity(
            app_name='review_app',
            activity_type='review_helpfulness_vote',
            user=request.user,
            request=request,
            details={
                'review_id': review.id,
                'venue_id': review.venue.id,
                'is_helpful': is_helpful,
                'was_update': not created,
            },
            target_object=f'review_{review.id}_helpfulness',
        )

        return JsonResponse({
            'success': True,
            'helpful_votes': stats['helpful_votes'],
            'not_helpful_votes': stats['not_helpful_votes'],
            'total_votes': stats['total_votes'],
            'user_vote': is_helpful,
        })

    except Exception as e:
        log_error(
            app_name='review_app',
            error_type='review_helpfulness_vote_error',
            error_message='Failed to vote on review helpfulness',
            user=request.user,
            request=request,
            exception=e,
            details={'review_slug': review_slug},
        )
        return JsonResponse({'error': 'Unable to process vote'}, status=500)
